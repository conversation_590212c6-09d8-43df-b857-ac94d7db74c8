using DISAdmin.Api.Models;
using DISAdmin.Core.Data;
using DISAdmin.Core.Data.Entities;
using DISAdmin.Core.Services;
using Microsoft.EntityFrameworkCore;

namespace DISAdmin.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Auth.Authorize]
public class DISInstancesController : ControllerBase
{
    private readonly DISInstanceService _instanceService;
    private readonly CustomerService _customerService;
    private readonly DISAdminDbContext _context;
    private readonly ILogger<DISInstancesController> _logger;

    public DISInstancesController(DISInstanceService instanceService, CustomerService customerService, DISAdminDbContext context, ILogger<DISInstancesController> logger)
    {
        _instanceService = instanceService;
        _customerService = customerService;
        _context = context;
        _logger = logger;
    }

    [HttpGet]
    public async Task<IActionResult> GetAllInstances()
    {
        var instances = await _instanceService.GetAllInstancesAsync();
        var response = instances
            .OrderBy(i => i.Customer.Abbreviation)
            .ThenBy(i => i.Name)
            .Select(i => new DISInstanceResponse
        {
            Id = i.Id,
            CustomerId = i.CustomerId,
            CustomerName = i.Customer.Name,
            CustomerAbbreviation = i.Customer.Abbreviation,
            Name = i.Name,
            Status = i.Status,
            ServerUrl = i.ServerUrl,
            InstallationDate = i.InstallationDate.ToLocalTime(),
            LastConnectionDate = i.LastConnectionDate.HasValue ? i.LastConnectionDate.Value.ToLocalTime() : null,
            ExpirationDate = i.ExpirationDate.HasValue ? i.ExpirationDate.Value.ToLocalTime() : null,
            Notes = i.Notes,
            ApiKey = i.ApiKey,
            BlockReason = i.BlockReason,
            ModuleReporting = i.ModuleReporting,
            ModuleAdvancedSecurity = i.ModuleAdvancedSecurity,
            ModuleApiIntegration = i.ModuleApiIntegration,
            ModuleDataExport = i.ModuleDataExport,
            ModuleCustomization = i.ModuleCustomization,
            EnableIpWhitelisting = i.EnableIpWhitelisting
        });

        return Ok(response);
    }

    [HttpGet("customer/{customerId}")]
    public async Task<IActionResult> GetCustomerInstances(int customerId)
    {
        var customer = await _customerService.GetCustomerByIdAsync(customerId);
        if (customer == null)
            return NotFound(new { message = "Zákazník nebyl nalezen" });

        var instances = await _instanceService.GetCustomerInstancesAsync(customerId);
        var response = instances
            .OrderBy(i => i.Name)
            .Select(i => new DISInstanceResponse
        {
            Id = i.Id,
            CustomerId = i.CustomerId,
            CustomerName = customer.Name,
            CustomerAbbreviation = customer.Abbreviation,
            Name = i.Name,
            Status = i.Status,
            ServerUrl = i.ServerUrl,
            InstallationDate = i.InstallationDate.ToLocalTime(),
            LastConnectionDate = i.LastConnectionDate.HasValue ? i.LastConnectionDate.Value.ToLocalTime() : null,
            ExpirationDate = i.ExpirationDate.HasValue ? i.ExpirationDate.Value.ToLocalTime() : null,
            Notes = i.Notes,
            ApiKey = i.ApiKey,
            BlockReason = i.BlockReason,
            ModuleReporting = i.ModuleReporting,
            ModuleAdvancedSecurity = i.ModuleAdvancedSecurity,
            ModuleApiIntegration = i.ModuleApiIntegration,
            ModuleDataExport = i.ModuleDataExport,
            ModuleCustomization = i.ModuleCustomization,
            EnableIpWhitelisting = i.EnableIpWhitelisting
        });

        return Ok(response);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetInstanceById(int id)
    {
        var instance = await _instanceService.GetInstanceByIdAsync(id);
        if (instance == null)
            return NotFound(new { message = "Instance nebyla nalezena" });

        var latestVersion = await _instanceService.GetLatestInstanceVersionAsync(id);

        var response = new DISInstanceDetailResponse
        {
            Id = instance.Id,
            CustomerId = instance.CustomerId,
            CustomerName = instance.Customer.Name,
            CustomerAbbreviation = instance.Customer.Abbreviation,
            Name = instance.Name,
            ServerUrl = instance.ServerUrl,
            ApiKey = instance.ApiKey,
            InstallationDate = instance.InstallationDate.ToLocalTime(),
            LastConnectionDate = instance.LastConnectionDate.HasValue ? instance.LastConnectionDate.Value.ToLocalTime() : (DateTime?)null,
            Notes = instance.Notes,
            Versions = instance.InstanceVersions.Select(iv => new InstanceVersionResponse
            {
                Id = iv.Id,
                InstanceId = iv.InstanceId,
                VersionId = iv.VersionId,
                VersionNumber = iv.Version.VersionNumber.ToString(),
                InstalledAt = iv.InstalledAt.ToLocalTime(),
                InstalledByUserId = iv.InstalledByUserId,
                InstalledByUserName = iv.InstalledByUser != null ?
                    $"{iv.InstalledByUser.FirstName} {iv.InstalledByUser.LastName}" : "Systém",
                Notes = iv.Notes
            }).ToList()
        };

        return Ok(response);
    }

    [HttpPost]
    [Auth.Authorize(adminOnly: true)]
    public async Task<IActionResult> CreateInstance(CreateDISInstanceRequest model)
    {
        var userId = HttpContext.Items["UserId"]?.ToString();
        var username = HttpContext.Items["Username"]?.ToString();
        var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();

        _logger.LogInformation("Zahájeno vytváření nové DIS instance. Uživatel: {Username} (ID: {UserId}), IP: {IpAddress}, Zákazník ID: {CustomerId}, Název: {InstanceName}",
            username, userId, ipAddress, model.CustomerId, model.Name);

        try
        {
            // Validace vstupních dat
            if (string.IsNullOrWhiteSpace(model.Name))
            {
                _logger.LogWarning("Pokus o vytvoření instance s prázdným názvem. Uživatel: {Username}, Zákazník ID: {CustomerId}", username, model.CustomerId);
                return BadRequest(new { message = "Název instance je povinný" });
            }

            _logger.LogInformation("Ověřování existence zákazníka s ID: {CustomerId}", model.CustomerId);
            var customer = await _customerService.GetCustomerByIdAsync(model.CustomerId);
            if (customer == null)
            {
                _logger.LogWarning("Pokus o vytvoření instance pro neexistujícího zákazníka. Zákazník ID: {CustomerId}, Uživatel: {Username}", model.CustomerId, username);
                return NotFound(new { message = "Zákazník nebyl nalezen" });
            }

            _logger.LogInformation("Zákazník nalezen: {CustomerName} ({CustomerAbbreviation}). Vytváření instance: {InstanceName}",
                customer.Name, customer.Abbreviation, model.Name);

            var instance = new DISInstance
            {
                CustomerId = model.CustomerId,
                Name = model.Name,
                ServerUrl = model.ServerUrl,
                Notes = model.Notes,
                ExpirationDate = model.ExpirationDate,
                Status = model.Status,
                ModuleReporting = model.ModuleReporting ?? true,
                ModuleAdvancedSecurity = model.ModuleAdvancedSecurity ?? false,
                ModuleApiIntegration = model.ModuleApiIntegration ?? false,
                ModuleDataExport = model.ModuleDataExport ?? false,
                ModuleCustomization = model.ModuleCustomization ?? false
            };

            _logger.LogInformation("Volání služby pro vytvoření instance. Instance: {InstanceName}, Zákazník: {CustomerName}, Moduly: Reporting={ModuleReporting}, Security={ModuleAdvancedSecurity}, API={ModuleApiIntegration}, Export={ModuleDataExport}, Customization={ModuleCustomization}",
                instance.Name, customer.Name, instance.ModuleReporting, instance.ModuleAdvancedSecurity, instance.ModuleApiIntegration, instance.ModuleDataExport, instance.ModuleCustomization);

            var createdInstance = await _instanceService.CreateInstanceAsync(instance);

            _logger.LogInformation("Instance úspěšně vytvořena. ID: {InstanceId}, Název: {InstanceName}, API klíč: {ApiKeyPrefix}..., Zákazník: {CustomerName}",
                createdInstance.Id, createdInstance.Name, createdInstance.ApiKey?.Substring(0, Math.Min(8, createdInstance.ApiKey.Length)), customer.Name);

            var response = new DISInstanceResponse
            {
                Id = createdInstance.Id,
                CustomerId = createdInstance.CustomerId,
                CustomerName = customer.Name,
                CustomerAbbreviation = customer.Abbreviation,
                Name = createdInstance.Name,
                ServerUrl = createdInstance.ServerUrl,
                InstallationDate = createdInstance.InstallationDate.ToLocalTime(),
                LastConnectionDate = createdInstance.LastConnectionDate.HasValue ? createdInstance.LastConnectionDate.Value.ToLocalTime() : (DateTime?)null,
                ExpirationDate = createdInstance.ExpirationDate.HasValue ? createdInstance.ExpirationDate.Value.ToLocalTime() : (DateTime?)null,
                Notes = createdInstance.Notes,
                ApiKey = createdInstance.ApiKey,
                Status = createdInstance.Status,
                BlockReason = createdInstance.BlockReason,
                ModuleReporting = createdInstance.ModuleReporting,
                ModuleAdvancedSecurity = createdInstance.ModuleAdvancedSecurity,
                ModuleApiIntegration = createdInstance.ModuleApiIntegration,
                ModuleDataExport = createdInstance.ModuleDataExport,
                ModuleCustomization = createdInstance.ModuleCustomization,
                EnableIpWhitelisting = createdInstance.EnableIpWhitelisting
            };

            return CreatedAtAction(nameof(GetInstanceById), new { id = createdInstance.Id }, response);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogError(ex, "Chyba při vytváření instance - InvalidOperationException. Uživatel: {Username}, Zákazník ID: {CustomerId}, Název: {InstanceName}, Chyba: {ErrorMessage}",
                username, model.CustomerId, model.Name, ex.Message);
            return BadRequest(new { message = ex.Message });
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(ex, "Databázová chyba při vytváření instance. Uživatel: {Username}, Zákazník ID: {CustomerId}, Název: {InstanceName}, Inner Exception: {InnerException}",
                username, model.CustomerId, model.Name, ex.InnerException?.Message);
            return StatusCode(500, new { message = "Chyba při ukládání instance do databáze" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Neočekávaná chyba při vytváření instance. Uživatel: {Username}, Zákazník ID: {CustomerId}, Název: {InstanceName}, Exception Type: {ExceptionType}, Stack Trace: {StackTrace}",
                username, model.CustomerId, model.Name, ex.GetType().FullName, ex.StackTrace);
            return StatusCode(500, new { message = "Došlo k neočekávané chybě při vytváření instance" });
        }
    }

    [HttpPut("{id}")]
    [Auth.Authorize(adminOnly: true)]
    public async Task<IActionResult> UpdateInstance(int id, UpdateDISInstanceRequest model)
    {
        try
        {
            var instance = await _instanceService.GetInstanceByIdAsync(id);
            if (instance == null)
                return NotFound(new { message = "Instance nebyla nalezena" });

            instance.Name = model.Name;
            instance.ServerUrl = model.ServerUrl;
            instance.Notes = model.Notes;
            instance.Status = model.Status;
            instance.BlockReason = model.BlockReason;

            // Aktualizace data expirace
            instance.ExpirationDate = model.ExpirationDate;

            // Aktualizace modulů
            if (model.ModuleReporting.HasValue)
            {
                instance.ModuleReporting = model.ModuleReporting.Value;
            }
            if (model.ModuleAdvancedSecurity.HasValue)
            {
                instance.ModuleAdvancedSecurity = model.ModuleAdvancedSecurity.Value;
            }
            if (model.ModuleApiIntegration.HasValue)
            {
                instance.ModuleApiIntegration = model.ModuleApiIntegration.Value;
            }
            if (model.ModuleDataExport.HasValue)
            {
                instance.ModuleDataExport = model.ModuleDataExport.Value;
            }
            if (model.ModuleCustomization.HasValue)
            {
                instance.ModuleCustomization = model.ModuleCustomization.Value;
            }

            await _instanceService.UpdateInstanceAsync(instance);

            return Ok(new DISInstanceResponse
            {
                Id = instance.Id,
                CustomerId = instance.CustomerId,
                CustomerName = instance.Customer.Name,
                CustomerAbbreviation = instance.Customer.Abbreviation,
                Name = instance.Name,
                ServerUrl = instance.ServerUrl,
                InstallationDate = instance.InstallationDate.ToLocalTime(),
                LastConnectionDate = instance.LastConnectionDate.HasValue ? instance.LastConnectionDate.Value.ToLocalTime() : (DateTime?)null,
                ExpirationDate = instance.ExpirationDate.HasValue ? instance.ExpirationDate.Value.ToLocalTime() : (DateTime?)null,
                Notes = instance.Notes,
                ApiKey = instance.ApiKey,
                Status = instance.Status,
                BlockReason = instance.BlockReason,
                ModuleReporting = instance.ModuleReporting,
                ModuleAdvancedSecurity = instance.ModuleAdvancedSecurity,
                ModuleApiIntegration = instance.ModuleApiIntegration,
                ModuleDataExport = instance.ModuleDataExport,
                ModuleCustomization = instance.ModuleCustomization,
                EnableIpWhitelisting = instance.EnableIpWhitelisting
            });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpDelete("{id}")]
    [Auth.Authorize(adminOnly: true)]
    public async Task<IActionResult> DeleteInstance(int id)
    {
        try
        {
            var instance = await _instanceService.GetInstanceByIdAsync(id);
            if (instance == null)
                return NotFound(new { message = "Instance nebyla nalezena" });

            await _instanceService.DeleteInstanceAsync(id);

            return Ok(new { message = "Instance byla úspěšně smazána" });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("{id}/regenerate-api-key")]
    [Auth.Authorize(adminOnly: true)]
    public async Task<IActionResult> RegenerateApiKey(int id)
    {
        try
        {
            var instance = await _instanceService.GetInstanceByIdAsync(id);
            if (instance == null)
                return NotFound(new { message = "Instance nebyla nalezena" });

            await _instanceService.RegenerateApiKeyAsync(id);
            instance = await _instanceService.GetInstanceByIdAsync(id);

            return Ok(new { apiKey = instance!.ApiKey });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    // Verze instancí
    [HttpGet("{id}/versions")]
    public async Task<IActionResult> GetInstanceVersions(int id)
    {
        var instance = await _instanceService.GetInstanceByIdAsync(id);
        if (instance == null)
            return NotFound(new { message = "Instance nebyla nalezena" });

        var versions = await _instanceService.GetInstanceVersionsAsync(id);
        var response = versions.Select(iv => new InstanceVersionResponse
        {
            Id = iv.Id,
            InstanceId = iv.InstanceId,
            VersionId = iv.VersionId,
            VersionNumber = iv.Version.VersionNumber.ToString(),
            InstalledAt = iv.InstalledAt.ToLocalTime(),
            InstalledByUserId = iv.InstalledByUserId,
            InstalledByUserName = iv.InstalledByUserId.HasValue && iv.InstalledByUser != null ?
                $"{iv.InstalledByUser.FirstName} {iv.InstalledByUser.LastName}" : "Systém",
            Notes = iv.Notes
        });

        return Ok(response);
    }

    [HttpPost("{id}/versions")]
    public async Task<IActionResult> AddInstanceVersion(int id, CreateInstanceVersionRequest model)
    {
        try
        {
            var instance = await _instanceService.GetInstanceByIdAsync(id);
            if (instance == null)
                return NotFound(new { message = "Instance nebyla nalezena" });

            var instanceVersion = new InstanceVersion
            {
                InstanceId = id,
                VersionId = model.VersionId,
                InstalledByUserId = model.InstalledByUserId,
                Notes = model.Notes
            };

            var createdInstanceVersion = await _instanceService.AddInstanceVersionAsync(instanceVersion);

            // Načtení uživatele
            User? user = null;
            if (createdInstanceVersion.InstalledByUserId.HasValue)
            {
                user = await _context.Users.FindAsync(createdInstanceVersion.InstalledByUserId);
            }

            return Ok(new InstanceVersionResponse
            {
                Id = createdInstanceVersion.Id,
                InstanceId = createdInstanceVersion.InstanceId,
                VersionId = createdInstanceVersion.VersionId,
                InstalledAt = createdInstanceVersion.InstalledAt.ToLocalTime(),
                InstalledByUserId = createdInstanceVersion.InstalledByUserId,
                InstalledByUserName = user != null ? $"{user.FirstName} {user.LastName}" : "Systém",
                Notes = createdInstanceVersion.Notes
            });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    // Diagnostické logy
    [HttpGet("{id}/logs")]
    public async Task<IActionResult> GetInstanceLogs(int id, [FromQuery] int count = 100)
    {
        var instance = await _instanceService.GetInstanceByIdAsync(id);
        if (instance == null)
            return NotFound(new { message = "Instance nebyla nalezena" });

        var logs = await _instanceService.GetInstanceLogsAsync(id, count);
        var response = logs.Select(l => new DiagnosticLogResponse
        {
            Id = l.Id,
            InstanceId = l.InstanceId,
            Timestamp = l.Timestamp.ToLocalTime(),
            Message = l.Message,
            Severity = l.Severity,
            SeverityName = l.Severity.ToString(),
            Source = l.Source,
            StackTrace = l.StackTrace
        });

        return Ok(response);
    }
}
