using DISAdmin.Api.Models;
using DISAdmin.Core.Attributes;
using DISAdmin.Core.Services;

namespace DISAdmin.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Auth.Authorize(AdminOnly = true)]
[SkipLogging]
public class LogsController : ControllerBase
{
    private readonly LoggingService _loggingService;
    private readonly DISInstanceService _instanceService;

    public LogsController(LoggingService loggingService, DISInstanceService instanceService)
    {
        _loggingService = loggingService;
        _instanceService = instanceService;
    }

    [HttpGet("activity")]
    public async Task<IActionResult> GetActivityLogs([FromQuery] LogFilterRequest filter)
    {
        var logs = await _loggingService.GetActivityLogsAsync(
            filter.UserId,
            filter.FromDate,
            filter.ToDate,
            filter.EntityName,
            filter.EntityId,
            filter.ActivityType,
            filter.Source,
            filter.MaxResults);

        var response = logs.Select(l => new ActivityLogResponse
        {
            Id = l.Id,
            UserId = l.UserId,
            Username = l.Username ?? l.User?.Username,
            Timestamp = l.Timestamp.ToLocalTime(),
            ActivityType = l.ActivityType.ToString(),
            EntityName = l.EntityName,
            EntityId = l.EntityId,
            Description = l.Description,
            IpAddress = l.IpAddress,
            AdditionalInfo = l.AdditionalInfo,
            // Použití int hodnoty zdroje z entity
            Source = (int)l.Source,
            // Nové sloupce pro API přístupy
            Method = l.Method,
            Endpoint = l.Endpoint,
            StatusCode = l.StatusCode,
            ResponseTimeMs = l.ResponseTimeMs
        });

        return Ok(response);
    }

    [HttpGet("activity/entity/{entityName}/{entityId}")]
    public async Task<IActionResult> GetEntityActivityLogs(string entityName, int entityId, [FromQuery] int maxResults = 100)
    {
        var logs = await _loggingService.GetEntityActivityLogsAsync(entityName, entityId, maxResults);

        var response = logs.Select(l => new ActivityLogResponse
        {
            Id = l.Id,
            UserId = l.UserId,
            Username = l.Username ?? l.User?.Username,
            Timestamp = l.Timestamp.ToLocalTime(),
            ActivityType = l.ActivityType.ToString(),
            EntityName = l.EntityName,
            EntityId = l.EntityId,
            Description = l.Description,
            IpAddress = l.IpAddress,
            AdditionalInfo = l.AdditionalInfo,
            // Použití int hodnoty zdroje z entity
            Source = (int)l.Source,
            // Nové sloupce pro API přístupy
            Method = l.Method,
            Endpoint = l.Endpoint,
            StatusCode = l.StatusCode,
            ResponseTimeMs = l.ResponseTimeMs
        });

        return Ok(response);
    }

    [HttpGet("errors")]
    [Auth.Authorize(adminOnly: true)]
    public async Task<IActionResult> GetErrorLogs([FromQuery] LogFilterRequest filter)
    {
        var logs = await _loggingService.GetErrorLogsAsync(
            filter.FromDate,
            filter.ToDate,
            filter.LogLevel,
            filter.Category,
            filter.MaxResults);

        var response = logs.Select(l => new ErrorLogResponse
        {
            Id = l.Id,
            Timestamp = l.Timestamp.ToLocalTime(),
            Message = l.Message,
            StackTrace = l.StackTrace,
            Source = l.Source,
            RequestPath = l.RequestPath,
            RequestMethod = l.RequestMethod,
            StatusCode = l.StatusCode,
            UserId = l.UserId,
            Username = l.Username ?? l.User?.Username,
            IpAddress = l.IpAddress,
            LogLevel = l.LogLevel.ToString(),
            Category = l.Category
        });

        return Ok(response);
    }



    [HttpGet("performance/{instanceId}")]
    public async Task<IActionResult> GetInstancePerformanceMetrics(int instanceId, [FromQuery] string? methodName = null, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null, [FromQuery] int maxResults = 100)
    {
        var instance = await _instanceService.GetInstanceByIdAsync(instanceId);
        if (instance == null)
            return NotFound(new { message = "Instance nebyla nalezena" });

        var metrics = await _loggingService.GetInstancePerformanceMetricsAsync(instanceId, methodName, fromDate, toDate, maxResults);

        var response = metrics.Select(m => new PerformanceMetricResponse
        {
            Id = m.Id,
            InstanceId = m.InstanceId,
            InstanceName = instance.Name,
            ClassName = m.ClassName,
            MethodName = m.MethodName,
            Parameters = m.Parameters,
            TotalCount = m.TotalCount,
            NonZeroCount = m.NonZeroCount,
            Min = m.Min,
            Max = m.Max,
            Avg = m.Avg,
            Median = m.Median,
            Percentil95 = m.Percentil95,
            Percentil99 = m.Percentil99,
            StdDev = m.StdDev,
            Timestamp = m.Timestamp.ToLocalTime(),
            VersionNumber = m.VersionNumber
        });

        return Ok(response);
    }

    [HttpGet("performance/{instanceId}/statistics")]
    public async Task<IActionResult> GetInstancePerformanceStatistics(int instanceId, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
    {
        var instance = await _instanceService.GetInstanceByIdAsync(instanceId);
        if (instance == null)
            return NotFound(new { message = "Instance nebyla nalezena" });

        var metrics = await _loggingService.GetPerformanceStatisticsAsync(instanceId, fromDate, toDate);

        var response = metrics.Select(m => new PerformanceStatisticsResponse
        {
            ClassName = m.ClassName,
            MethodName = m.MethodName,
            AverageExecutionTimeMs = m.Avg,
            MedianExecutionTimeMs = m.Median,
            MinExecutionTimeMs = m.Min,
            MaxExecutionTimeMs = m.Max,
            Percentil95 = m.Percentil95,
            Percentil99 = m.Percentil99,
            StdDev = m.StdDev,
            TotalCount = m.TotalCount,
            NonZeroCount = m.NonZeroCount
        });

        return Ok(response);
    }
}
