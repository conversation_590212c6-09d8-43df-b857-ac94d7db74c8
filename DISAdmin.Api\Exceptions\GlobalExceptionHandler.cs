using Microsoft.AspNetCore.Diagnostics;
using Microsoft.Extensions.Logging;
using DISAdmin.Core.Data.Entities;
using DISAdmin.Core.Services;
using DISAdmin.Core.Exceptions;
using System.Text.Json;

namespace DISAdmin.Api.Exceptions;

/// <summary>
/// Globální handler pro zachytávání všech neošetřených výjimek
/// Implementuje IExceptionHandler pro .NET 8+
/// </summary>
public class GlobalExceptionHandler : IExceptionHandler
{
    private readonly ILogger<GlobalExceptionHandler> _logger;
    private readonly IServiceProvider _serviceProvider;

    public GlobalExceptionHandler(
        ILogger<GlobalExceptionHandler> logger,
        IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    public async ValueTask<bool> TryHandleAsync(
        HttpContext httpContext,
        Exception exception,
        CancellationToken cancellationToken)
    {
        _logger.LogError(exception, "Globáln<PERSON> zach<PERSON>cen<PERSON> neošet<PERSON>en<PERSON> výjimky: {Message}", exception.Message);

        // Uložení chyby do databáze
        await LogErrorToDatabaseAsync(httpContext, exception);

        // Vytvoření odpovědi pro klienta
        await WriteErrorResponseAsync(httpContext, exception, cancellationToken);

        return true; // Výjimka byla zpracována
    }

    private async Task LogErrorToDatabaseAsync(HttpContext context, Exception exception)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var loggingService = scope.ServiceProvider.GetRequiredService<LoggingService>();

            // Získání informací o uživateli a požadavku
            var userId = GetUserId(context);
            var username = GetUsername(context);
            var ipAddress = GetIpAddress(context);

            var errorLog = new ErrorLog
            {
                Message = exception.Message,
                StackTrace = exception.StackTrace,
                Source = "DISAdmin",
                RequestPath = context.Request.Path,
                RequestMethod = context.Request.Method,
                StatusCode = context.Response.StatusCode,
                UserId = userId,
                Username = username,
                IpAddress = ipAddress,
                AdditionalInfo = exception.ToString(),
                LogLevel = ApplicationLogLevel.Critical, // Globální výjimky jsou kritické
                Category = exception.GetType().FullName
            };

            await loggingService.LogErrorAsync(errorLog);
        }
        catch (Exception logEx)
        {
            _logger.LogError(logEx, "Chyba při ukládání globální výjimky do databáze");
        }
    }

    private async Task WriteErrorResponseAsync(
        HttpContext context,
        Exception exception,
        CancellationToken cancellationToken)
    {
        var statusCode = GetStatusCode(exception);
        var message = GetErrorMessage(exception);

        context.Response.StatusCode = statusCode;
        context.Response.ContentType = "application/json";

        var response = new
        {
            success = false,
            message,
            statusCode,
            timestamp = DateTime.UtcNow,
            path = context.Request.Path.Value
        };

        var jsonResponse = JsonSerializer.Serialize(response);
        await context.Response.WriteAsync(jsonResponse, cancellationToken);
    }

    private int GetStatusCode(Exception exception)
    {
        return exception switch
        {
            NotFoundException => 404,
            ValidationException => 400,
            UnauthorizedException => 401,
            ForbiddenException => 403,
            _ => 500
        };
    }

    private string GetErrorMessage(Exception exception)
    {
        return exception switch
        {
            NotFoundException notFound => notFound.Message,
            ValidationException validation => validation.Message,
            UnauthorizedException unauthorized => unauthorized.Message,
            ForbiddenException forbidden => forbidden.Message,
            _ => "Došlo k neočekávané chybě na serveru."
        };
    }

    private int? GetUserId(HttpContext context)
    {
        try
        {
            var userIdClaim = context.User?.FindFirst("userId")?.Value;
            if (int.TryParse(userIdClaim, out var userId))
            {
                return userId;
            }
        }
        catch
        {
            // Ignorujeme chyby při získávání userId
        }
        return null;
    }

    private string? GetUsername(HttpContext context)
    {
        try
        {
            return context.User?.FindFirst("username")?.Value;
        }
        catch
        {
            // Ignorujeme chyby při získávání username
        }
        return null;
    }

    private string GetIpAddress(HttpContext context)
    {
        try
        {
            var ipAddress = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            }
            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = context.Connection.RemoteIpAddress?.ToString();
            }

            return ipAddress ?? "unknown";
        }
        catch
        {
            return "unknown";
        }
    }
}
