using System.Net;
using System.Text.Json;
using DISAdmin.Core.Exceptions;
using DISAdmin.Core.Data.Entities;
using DISAdmin.Core.Services;
using Microsoft.EntityFrameworkCore;

namespace DISAdmin.Api.Middleware;

/// <summary>
/// Middleware pro zachytávání a logování všech neošetřených výjimek v Web projektu
/// </summary>
public class WebErrorHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<WebErrorHandlingMiddleware> _logger;
    private readonly IServiceProvider _serviceProvider;

    public WebErrorHandlingMiddleware(
        RequestDelegate next, 
        ILogger<WebErrorHandlingMiddleware> logger,
        IServiceProvider serviceProvider)
    {
        _next = next;
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Neošetřen<PERSON> výjimka v Web aplikaci: {Message}", ex.Message);
            
            // Uložení chyby do databáze
            await LogErrorToDatabaseAsync(context, ex);
            
            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task LogErrorToDatabaseAsync(HttpContext context, Exception exception)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var loggingService = scope.ServiceProvider.GetRequiredService<LoggingService>();
            
            // Získání informací o uživateli
            var userId = GetUserId(context);
            var username = GetUsername(context);
            var ipAddress = GetIpAddress(context);
            
            var errorLog = new ErrorLog
            {
                Message = exception.Message,
                StackTrace = exception.StackTrace,
                Source = "DISAdmin",
                RequestPath = context.Request.Path,
                RequestMethod = context.Request.Method,
                StatusCode = context.Response.StatusCode,
                UserId = userId,
                Username = username,
                IpAddress = ipAddress,
                AdditionalInfo = exception.ToString(),
                LogLevel = ApplicationLogLevel.Error,
                Category = exception.GetType().FullName
            };

            await loggingService.LogErrorAsync(errorLog);
        }
        catch (Exception logEx)
        {
            // Pokud se nepodaří uložit do databáze, zalogujeme to alespoň do konzole
            _logger.LogError(logEx, "Chyba při ukládání error logu do databáze");
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var statusCode = HttpStatusCode.InternalServerError;
        var message = "Došlo k neočekávané chybě.";
        string? details = null;

        // Rozlišení typů výjimek
        switch (exception)
        {
            case NotFoundException notFoundException:
                statusCode = HttpStatusCode.NotFound;
                message = notFoundException.Message;
                break;
            case ValidationException validationException:
                statusCode = HttpStatusCode.BadRequest;
                message = validationException.Message;
                break;
            case UnauthorizedException unauthorizedException:
                statusCode = HttpStatusCode.Unauthorized;
                message = unauthorizedException.Message;
                break;
            case ForbiddenException forbiddenException:
                statusCode = HttpStatusCode.Forbidden;
                message = forbiddenException.Message;
                break;
            case DbUpdateException dbUpdateException:
                statusCode = HttpStatusCode.BadRequest;
                message = "Chyba při ukládání dat.";
                details = dbUpdateException.InnerException?.Message ?? dbUpdateException.Message;
                break;
            case JsonException jsonException:
                statusCode = HttpStatusCode.BadRequest;
                message = "Neplatný formát JSON.";
                details = jsonException.Message;
                break;
            default:
                // Pro neznámé výjimky zobrazíme obecnou chybu
                _logger.LogError(exception, "Neošetřená výjimka: {Message}", exception.Message);
                break;
        }

        context.Response.ContentType = "application/json";
        context.Response.StatusCode = (int)statusCode;

        var result = JsonSerializer.Serialize(new
        {
            success = false,
            message,
            details,
            statusCode = (int)statusCode
        });

        await context.Response.WriteAsync(result);
    }

    private int? GetUserId(HttpContext context)
    {
        try
        {
            var userIdClaim = context.User?.FindFirst("userId")?.Value;
            if (int.TryParse(userIdClaim, out var userId))
            {
                return userId;
            }
        }
        catch
        {
            // Ignorujeme chyby při získávání userId
        }
        return null;
    }

    private string? GetUsername(HttpContext context)
    {
        try
        {
            return context.User?.FindFirst("username")?.Value;
        }
        catch
        {
            // Ignorujeme chyby při získávání username
        }
        return null;
    }

    private string GetIpAddress(HttpContext context)
    {
        try
        {
            // Zkusíme získat IP adresu z různých headerů
            var ipAddress = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            }
            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = context.Connection.RemoteIpAddress?.ToString();
            }
            
            return ipAddress ?? "unknown";
        }
        catch
        {
            return "unknown";
        }
    }
}
