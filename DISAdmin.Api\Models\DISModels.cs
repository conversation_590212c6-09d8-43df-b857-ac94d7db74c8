using System.ComponentModel.DataAnnotations;
using DISAdmin.Core.Data.Entities;

namespace DISAdmin.Api.Models;

public class DISInstanceResponse
{
    public int Id { get; set; }
    public int CustomerId { get; set; }
    public string CustomerName { get; set; } = string.Empty;
    public string CustomerAbbreviation { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string ServerUrl { get; set; } = string.Empty;
    public DateTime InstallationDate { get; set; }
    public DateTime? LastConnectionDate { get; set; }
    public DateTime? ExpirationDate { get; set; }
    public string Notes { get; set; } = string.Empty;
    public string ApiKey { get; set; } = string.Empty;
    public InstanceStatus Status { get; set; }
    public string? BlockReason { get; set; }
    public bool ModuleReporting { get; set; } = true;
    public bool ModuleAdvancedSecurity { get; set; } = false;
    public bool ModuleApiIntegration { get; set; } = false;
    public bool ModuleDataExport { get; set; } = false;
    public bool ModuleCustomization { get; set; } = false;
    public bool EnableIpWhitelisting { get; set; } = false;
}

public class DISInstanceDetailResponse : DISInstanceResponse
{
    // Použijeme new, aby se potlačilo varování o skrytí zděděného člena
    public new string ApiKey { get; set; } = string.Empty;
    public string? ClientCertificateThumbprint { get; set; }
    public string? ClientCertificateSubject { get; set; }
    public string? ClientCertificateIssuer { get; set; }
    public DateTime? CertificateExpirationDate { get; set; }
    public DateTime? LastCertificateValidation { get; set; }
    public List<InstanceVersionResponse> Versions { get; set; } = new List<InstanceVersionResponse>();
}

public class CreateDISInstanceRequest
{
    [Required(ErrorMessage = "ID zákazníka je povinné")]
    public int CustomerId { get; set; }

    [Required(ErrorMessage = "Název instance je povinný")]
    [StringLength(200, ErrorMessage = "Název instance může mít maximálně 200 znaků")]
    public string Name { get; set; } = string.Empty;

    [StringLength(255, ErrorMessage = "URL serveru může mít maximálně 255 znaků")]
    public string ServerUrl { get; set; } = string.Empty;

    [StringLength(500, ErrorMessage = "Poznámky mohou mít maximálně 500 znaků")]
    public string Notes { get; set; } = string.Empty;

    public DateTime? ExpirationDate { get; set; }
    public InstanceStatus Status { get; set; }
    public bool? ModuleReporting { get; set; }
    public bool? ModuleAdvancedSecurity { get; set; }
    public bool? ModuleApiIntegration { get; set; }
    public bool? ModuleDataExport { get; set; }
    public bool? ModuleCustomization { get; set; }
}

public class UpdateDISInstanceRequest
{
    [Required]
    public string Name { get; set; } = string.Empty;

    public string ServerUrl { get; set; } = string.Empty;
    public string Notes { get; set; } = string.Empty;
    public DateTime? ExpirationDate { get; set; }
    public InstanceStatus Status { get; set; }
    public string? BlockReason { get; set; }
    public bool? ModuleReporting { get; set; }
    public bool? ModuleAdvancedSecurity { get; set; }
    public bool? ModuleApiIntegration { get; set; }
    public bool? ModuleDataExport { get; set; }
    public bool? ModuleCustomization { get; set; }
}

public class DISVersionResponse
{
    public int Id { get; set; }

    // Používáme string pro VersionNumber v DTO, ale interně pracujeme s System.Version
    public string VersionNumber { get; set; } = string.Empty;

    // Pomocná vlastnost pro přístup k System.Version
    public Version? VersionNumberObject
    {
        get => Version.TryParse(VersionNumber, out Version? v) ? v : null;
        set => VersionNumber = value?.ToString() ?? string.Empty;
    }

    public DateTime ReleaseDate { get; set; }
    public int CreatedByUserId { get; set; }
    public string CreatedByUserName { get; set; } = string.Empty;
    public int ChangeLogCount { get; set; }
    public int InstancesCount { get; set; }
}

public class DISVersionDetailResponse : DISVersionResponse
{
    public List<VersionChangeLogResponse> ChangeLogs { get; set; } = new List<VersionChangeLogResponse>();
}

public class CreateDISVersionRequest
{
    [Required]
    public string VersionNumber { get; set; } = string.Empty;

    public DateTime ReleaseDate { get; set; } = DateTime.UtcNow;

    // Seznam změn, které budou vytvořeny spolu s verzí
    public List<CreateVersionChangeLogRequest>? ChangeLogs { get; set; }
}

public class UpdateDISVersionRequest
{
    [Required]
    public string VersionNumber { get; set; } = string.Empty;

    public DateTime ReleaseDate { get; set; }

    // ID uživatele, který verzi vytvořil
    public int? CreatedByUserId { get; set; }
}

public class VersionChangeLogResponse
{
    public int Id { get; set; }
    public int VersionId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int? TaskId { get; set; }
    public string? TaskName { get; set; }
    public ChangeType Type { get; set; }
    public string TypeName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public bool IsPublic { get; set; }
}

public class CreateVersionChangeLogRequest
{
    [Required]
    [StringLength(250)]
    public string Name { get; set; } = string.Empty;

    [StringLength(2000)]
    public string Description { get; set; } = string.Empty;

    public int? TaskId { get; set; }

    [StringLength(250)]
    public string? TaskName { get; set; }

    [Required]
    public ChangeType Type { get; set; }

    public bool IsPublic { get; set; } = true;
}

public class UpdateVersionChangeLogRequest
{
    [Required]
    [StringLength(250)]
    public string Name { get; set; } = string.Empty;

    [StringLength(2000)]
    public string Description { get; set; } = string.Empty;

    public int? TaskId { get; set; }

    [StringLength(250)]
    public string? TaskName { get; set; }

    [Required]
    public ChangeType Type { get; set; }

    public bool IsPublic { get; set; } = true;
}

public class InstanceVersionResponse
{
    public int Id { get; set; }
    public int InstanceId { get; set; }
    public int VersionId { get; set; }

    // Používáme string pro VersionNumber v DTO, ale interně pracujeme s System.Version
    public string VersionNumber { get; set; } = string.Empty;

    // Pomocná vlastnost pro přístup k System.Version
    public Version? VersionNumberObject
    {
        get => Version.TryParse(VersionNumber, out Version? v) ? v : null;
        set => VersionNumber = value?.ToString() ?? string.Empty;
    }

    public DateTime InstalledAt { get; set; }
    public int? InstalledByUserId { get; set; }
    public string InstalledByUserName { get; set; } = string.Empty;
    public string Notes { get; set; } = string.Empty;
}

public class CreateInstanceVersionRequest
{
    [Required]
    public int VersionId { get; set; }

    public int? InstalledByUserId { get; set; }

    public string Notes { get; set; } = string.Empty;
}

public class DiagnosticLogResponse
{
    public int Id { get; set; }
    public int InstanceId { get; set; }
    public DateTime Timestamp { get; set; }
    public string Message { get; set; } = string.Empty;
    public LogSeverity Severity { get; set; }
    public string SeverityName { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public string StackTrace { get; set; } = string.Empty;
}

public class CreateDiagnosticLogRequest
{
    [Required]
    public string Message { get; set; } = string.Empty;

    [Required]
    public LogSeverity Severity { get; set; }

    public string Source { get; set; } = string.Empty;
    public string StackTrace { get; set; } = string.Empty;
}

