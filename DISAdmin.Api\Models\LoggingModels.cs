using DISAdmin.Core.Data.Entities;

namespace DISAdmin.Api.Models;

public class ActivityLogResponse
{
    public int Id { get; set; }
    public int? UserId { get; set; }
    public string? Username { get; set; }
    public DateTime Timestamp { get; set; }
    public string ActivityType { get; set; } = string.Empty;
    public string? EntityName { get; set; }
    public int? EntityId { get; set; }
    public string? Description { get; set; }
    public string IpAddress { get; set; } = string.Empty;
    public string? AdditionalInfo { get; set; }
    public int Source { get; set; } // Změna ze string na int pro explicitní zdroj

    // Nové sloupce pro API přístupy
    public string? Method { get; set; }
    public string? Endpoint { get; set; }
    public int? StatusCode { get; set; }
    public int? ResponseTimeMs { get; set; }
}

public class ErrorLogResponse
{
    public int Id { get; set; }
    public DateTime Timestamp { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? StackTrace { get; set; }
    public string Source { get; set; } = string.Empty;
    public string? RequestPath { get; set; }
    public string? RequestMethod { get; set; }
    public int? StatusCode { get; set; }
    public int? UserId { get; set; }
    public string? Username { get; set; }
    public string IpAddress { get; set; } = string.Empty;
    public string LogLevel { get; set; } = string.Empty; // Nová vlastnost pro úroveň logování
    public string? Category { get; set; } // Nová vlastnost pro kategorii loggeru
}

public class PerformanceMetricResponse
{
    public int Id { get; set; }
    public int InstanceId { get; set; }
    public string InstanceName { get; set; } = string.Empty;
    public string ClassName { get; set; } = string.Empty;
    public string MethodName { get; set; } = string.Empty;
    public string? Parameters { get; set; }
    public int TotalCount { get; set; }
    public int NonZeroCount { get; set; }
    public long Min { get; set; }
    public long Max { get; set; }
    public long Avg { get; set; }
    public long Median { get; set; }
    public long Percentil95 { get; set; }
    public long Percentil99 { get; set; }
    public long StdDev { get; set; }
    public DateTime Timestamp { get; set; }
    public string? VersionNumber { get; set; }
}

public class PerformanceStatisticsResponse
{
    public string ClassName { get; set; } = string.Empty;
    public string MethodName { get; set; } = string.Empty;
    public long AverageExecutionTimeMs { get; set; }
    public long MedianExecutionTimeMs { get; set; }
    public long MinExecutionTimeMs { get; set; }
    public long MaxExecutionTimeMs { get; set; }
    public long Percentil95 { get; set; }
    public long Percentil99 { get; set; }
    public long StdDev { get; set; }
    public int TotalCount { get; set; }
    public int NonZeroCount { get; set; }
}

public class LogFilterRequest
{
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public int? UserId { get; set; }
    public string? EntityName { get; set; }
    public int? EntityId { get; set; }
    public ActivityType? ActivityType { get; set; }
    public int? Source { get; set; } // Změna ze string na int pro explicitní zdroj
    public int? LogLevel { get; set; } // Nový filtr pro úroveň logování (pro ErrorLog)
    public string? Category { get; set; } // Nový filtr pro kategorii loggeru
    public int MaxResults { get; set; } = 100;
}
