using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using DISAdmin.Core.Data.Entities;
using DISAdmin.Core.Services;

namespace DISAdmin.Api.Services;

/// <summary>
/// Služba pro zachytávání neošetřených výjimek mimo HTTP kontext
/// </summary>
public class UnhandledExceptionService : IHostedService
{
    private readonly ILogger<UnhandledExceptionService> _logger;
    private readonly IServiceProvider _serviceProvider;

    public UnhandledExceptionService(
        ILogger<UnhandledExceptionService> logger,
        IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        // Registrace event handlerů pro neošetřené výjimky
        AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
        TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;

        _logger.LogInformation("UnhandledExceptionService byl spuštěn");
        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        // Odregistrace event handlerů
        AppDomain.CurrentDomain.UnhandledException -= OnUnhandledException;
        TaskScheduler.UnobservedTaskException -= OnUnobservedTaskException;

        _logger.LogInformation("UnhandledExceptionService byl zastaven");
        return Task.CompletedTask;
    }

    private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        if (e.ExceptionObject is Exception exception)
        {
            _logger.LogCritical(exception, "Neošetřená výjimka v AppDomain: {Message}", exception.Message);
            
            // Uložení do databáze
            _ = Task.Run(async () => await LogExceptionToDatabaseAsync(exception, "AppDomain.UnhandledException"));
        }
    }

    private void OnUnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
    {
        _logger.LogCritical(e.Exception, "Neošetřená výjimka v Task: {Message}", e.Exception.Message);
        
        // Označíme výjimku jako pozorovanou, aby nezpůsobila pád aplikace
        e.SetObserved();
        
        // Uložení do databáze
        _ = Task.Run(async () => await LogExceptionToDatabaseAsync(e.Exception, "TaskScheduler.UnobservedTaskException"));
    }

    private async Task LogExceptionToDatabaseAsync(Exception exception, string source)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var loggingService = scope.ServiceProvider.GetRequiredService<LoggingService>();

            var errorLog = new ErrorLog
            {
                Message = exception.Message,
                StackTrace = exception.StackTrace,
                Source = "DISAdmin",
                RequestPath = null, // Není HTTP požadavek
                RequestMethod = null,
                StatusCode = null,
                UserId = null, // Není uživatelský kontext
                Username = null,
                IpAddress = "localhost",
                AdditionalInfo = $"Source: {source}\n\n{exception}",
                LogLevel = ApplicationLogLevel.Critical,
                Category = exception.GetType().FullName
            };

            await loggingService.LogErrorAsync(errorLog);
            
            _logger.LogInformation("Neošetřená výjimka byla uložena do databáze: {Source}", source);
        }
        catch (Exception logEx)
        {
            _logger.LogError(logEx, "Chyba při ukládání neošetřené výjimky do databáze");
            
            // Jako poslední možnost zapíšeme do konzole
            Console.Error.WriteLine($"CRITICAL ERROR - Failed to log unhandled exception: {exception}");
            Console.Error.WriteLine($"Logging error: {logEx}");
        }
    }
}
