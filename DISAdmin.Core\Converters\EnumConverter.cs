using System.Text.Json;
using System.Text.Json.Serialization;

namespace DISAdmin.Core.Converters;

/// <summary>
/// Generický JSON converter pro enum typy, který umí zpracovat jak číselné hodnoty, tak stringy
/// </summary>
/// <typeparam name="TEnum">Typ enum</typeparam>
public class EnumConverter<TEnum> : JsonConverter<TEnum> where TEnum : struct, Enum
{
    public override TEnum Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.Number)
        {
            // Číselná hodnota - standardní enum parsing
            var intValue = reader.GetInt32();
            if (Enum.IsDefined(typeof(TEnum), intValue))
            {
                return (TEnum)Enum.ToObject(typeof(TEnum), intValue);
            }
            throw new JsonException($"Value {intValue} is not a valid {typeof(TEnum).Name}.");
        }

        if (reader.TokenType == JsonTokenType.String)
        {
            var stringValue = reader.GetString();
            
            if (string.IsNullOrWhiteSpace(stringValue))
            {
                throw new JsonException($"Empty string cannot be converted to {typeof(TEnum).Name}.");
            }
            
            // Pokusíme se parsovat string jako enum název (case-insensitive)
            if (Enum.TryParse<TEnum>(stringValue, true, out var enumValue))
            {
                return enumValue;
            }

            // Pokusíme se parsovat string jako číslo
            if (int.TryParse(stringValue, out var intValue) && Enum.IsDefined(typeof(TEnum), intValue))
            {
                return (TEnum)Enum.ToObject(typeof(TEnum), intValue);
            }

            throw new JsonException($"Unable to convert \"{stringValue}\" to {typeof(TEnum).Name}. Valid values are: {string.Join(", ", Enum.GetNames<TEnum>())}.");
        }

        throw new JsonException($"Unexpected token type {reader.TokenType} when parsing {typeof(TEnum).Name}.");
    }

    public override void Write(Utf8JsonWriter writer, TEnum value, JsonSerializerOptions options)
    {
        // Zapisujeme jako číslo pro konzistenci s API
        writer.WriteNumberValue(Convert.ToInt32(value));
    }
}

/// <summary>
/// Generický JSON converter pro nullable enum typy
/// </summary>
/// <typeparam name="TEnum">Typ enum</typeparam>
public class NullableEnumConverter<TEnum> : JsonConverter<TEnum?> where TEnum : struct, Enum
{
    private readonly EnumConverter<TEnum> _enumConverter = new();

    public override TEnum? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.Null)
        {
            return null;
        }

        return _enumConverter.Read(ref reader, typeof(TEnum), options);
    }

    public override void Write(Utf8JsonWriter writer, TEnum? value, JsonSerializerOptions options)
    {
        if (value.HasValue)
        {
            _enumConverter.Write(writer, value.Value, options);
        }
        else
        {
            writer.WriteNullValue();
        }
    }
}
