using System.Text.Json;
using System.Text.Json.Serialization;
using DISAdmin.Core.Data.Entities;

namespace DISAdmin.Core.Converters;

/// <summary>
/// JSON converter pro InstanceStatus enum, který umí zpracovat jak číselné hodnoty, tak stringy
/// </summary>
public class InstanceStatusConverter : JsonConverter<InstanceStatus>
{
    public override InstanceStatus Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.Number)
        {
            // Číselná hodnota - standardní enum parsing
            var intValue = reader.GetInt32();
            if (Enum.IsDefined(typeof(InstanceStatus), intValue))
            {
                return (InstanceStatus)intValue;
            }
            throw new JsonException($"Value {intValue} is not a valid InstanceStatus.");
        }

        if (reader.TokenType == JsonTokenType.String)
        {
            var stringValue = reader.GetString();
            
            // Pokusíme se parsovat string jako enum název
            if (Enum.TryParse<InstanceStatus>(stringValue, true, out var enumValue))
            {
                return enumValue;
            }

            // Pokusíme se parsovat string jako číslo
            if (int.TryParse(stringValue, out var intValue) && Enum.IsDefined(typeof(InstanceStatus), intValue))
            {
                return (InstanceStatus)intValue;
            }

            throw new JsonException($"Unable to convert \"{stringValue}\" to InstanceStatus.");
        }

        throw new JsonException($"Unexpected token type {reader.TokenType} when parsing InstanceStatus.");
    }

    public override void Write(Utf8JsonWriter writer, InstanceStatus value, JsonSerializerOptions options)
    {
        // Zapisujeme jako číslo pro konzistenci s API
        writer.WriteNumberValue((int)value);
    }
}
