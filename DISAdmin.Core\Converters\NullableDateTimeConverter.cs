using System.Text.Json;
using System.Text.Json.Serialization;

namespace DISAdmin.Core.Converters;

/// <summary>
/// JSON converter pro nullable DateTime, který umí zpracovat prázdné stringy jako null
/// </summary>
public class NullableDateTimeConverter : JsonConverter<DateTime?>
{
    public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.Null)
        {
            return null;
        }

        if (reader.TokenType == JsonTokenType.String)
        {
            var stringValue = reader.GetString();
            
            // Pokud je string prázdný nebo obsahuje pouze whitespace, vrátíme null
            if (string.IsNullOrWhiteSpace(stringValue))
            {
                return null;
            }

            // Pokusíme se parsovat datum
            if (DateTime.TryParse(stringValue, out var dateTime))
            {
                return dateTime;
            }

            // Pokud se nepoda<PERSON><PERSON> parsovat, vyhodíme výjim<PERSON>
            throw new JsonException($"Unable to convert \"{stringValue}\" to DateTime.");
        }

        // Pro ostatní typy tokenů použijeme standardní parsing
        return JsonSerializer.Deserialize<DateTime?>(ref reader, options);
    }

    public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
    {
        if (value.HasValue)
        {
            writer.WriteStringValue(value.Value.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"));
        }
        else
        {
            writer.WriteNullValue();
        }
    }
}
