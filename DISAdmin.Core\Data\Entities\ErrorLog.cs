using System.ComponentModel.DataAnnotations;

namespace DISAdmin.Core.Data.Entities;

public enum ApplicationLogLevel
{
    Trace = 0,
    Debug = 1,
    Information = 2,
    Warning = 3,
    Error = 4,
    Critical = 5
}

public class ErrorLog
{
    public int Id { get; set; }

    public DateTime Timestamp { get; set; }

    public string Message { get; set; } = string.Empty;

    public string? StackTrace { get; set; }

    [MaxLength(255)]
    public string Source { get; set; } = string.Empty;

    [MaxLength(255)]
    public string? RequestPath { get; set; }

    [MaxLength(10)]
    public string? RequestMethod { get; set; }

    public int? StatusCode { get; set; }

    public int? UserId { get; set; }

    [MaxLength(100)]
    public string? Username { get; set; }

    [MaxLength(45)]
    public string IpAddress { get; set; } = string.Empty;

    public string? AdditionalInfo { get; set; }

    // Nový sloupec pro úroveň logování s výcho<PERSON>í hodnotou Error pro zpětnou kompatibilitu
    public ApplicationLogLevel LogLevel { get; set; } = ApplicationLogLevel.Error;

    [MaxLength(100)]
    public string? Category { get; set; }

    // Navigační vlastnosti
    public User? User { get; set; }
}
