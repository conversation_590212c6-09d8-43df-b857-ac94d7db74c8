using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using DISAdmin.Core.Data.Entities;
using DISAdmin.Core.Services;

namespace DISAdmin.Core.Logging;

/// <summary>
/// Logger, který ukládá chyby do databáze
/// </summary>
public class DatabaseLogger : ILogger
{
    private readonly string _categoryName;
    private readonly IServiceProvider _serviceProvider;
    private readonly Func<string, LogLevel, bool> _filter;
    private readonly string _source;
    private readonly ConcurrentQueue<ErrorLog> _errorQueue = new ConcurrentQueue<ErrorLog>();
    private readonly object _processQueueLock = new object();
    private bool _isProcessingQueue = false;

    public DatabaseLogger(
        string categoryName,
        IServiceProvider serviceProvider,
        Func<string, LogLevel, bool> filter,
        string source = "DISAdmin")
    {
        _categoryName = categoryName;
        _serviceProvider = serviceProvider;
        _filter = filter;
        _source = source;
    }

    public IDisposable BeginScope<TState>(TState state) => default!;

    public bool IsEnabled(LogLevel logLevel)
    {
        // Používáme konfigurovaný filtr pro určení, zda logovat
        return _filter == null || _filter(_categoryName, logLevel);
    }

    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
    {
        if (!IsEnabled(logLevel))
            return;

        if (formatter == null)
            throw new ArgumentNullException(nameof(formatter));

        var message = formatter(state, exception);

        if (string.IsNullOrEmpty(message) && exception == null)
            return;

        // Vytvoření záznamu o logu (přejmenováno z "chybě" na obecnější "logu")
        var errorLog = new ErrorLog
        {
            Message = message,
            StackTrace = exception?.StackTrace,
            Source = _source,
            IpAddress = "unknown", // Toto by mělo být doplněno z kontextu, pokud je dostupný
            AdditionalInfo = exception?.ToString(),
            LogLevel = ConvertLogLevel(logLevel),
            Category = _categoryName
        };

        // Přidání do fronty pro asynchronní zpracování
        _errorQueue.Enqueue(errorLog);

        // Spuštění zpracování fronty, pokud ještě neběží
        ProcessQueueAsync();
    }

    private void ProcessQueueAsync()
    {
        // Zajištění, že zpracování fronty běží pouze jednou
        lock (_processQueueLock)
        {
            if (_isProcessingQueue)
                return;

            _isProcessingQueue = true;
        }

        // Spuštění asynchronního zpracování
        Task.Run(async () =>
        {
            try
            {
                while (_errorQueue.TryDequeue(out var errorLog))
                {
                    try
                    {
                        using (var scope = _serviceProvider.CreateScope())
                        {
                            var loggingService = scope.ServiceProvider.GetRequiredService<LoggingService>();
                            await loggingService.LogErrorAsync(errorLog);
                        }
                    }
                    catch (Exception ex)
                    {
                        // Zde nemůžeme použít logger, protože by to mohlo způsobit nekonečnou smyčku
                        Console.Error.WriteLine($"Chyba při ukládání logu do databáze: {ex.Message}");
                    }
                }
            }
            finally
            {
                lock (_processQueueLock)
                {
                    _isProcessingQueue = false;

                    // Pokud se během zpracování objevily nové položky, spustíme zpracování znovu
                    if (!_errorQueue.IsEmpty)
                    {
                        _isProcessingQueue = true;
                        Task.Run(async () => await ProcessQueueInternalAsync());
                    }
                }
            }
        });
    }

    private async Task ProcessQueueInternalAsync()
    {
        try
        {
            while (_errorQueue.TryDequeue(out var errorLog))
            {
                try
                {
                    using (var scope = _serviceProvider.CreateScope())
                    {
                        var loggingService = scope.ServiceProvider.GetRequiredService<LoggingService>();
                        await loggingService.LogErrorAsync(errorLog);
                    }
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Chyba při ukládání logu do databáze: {ex.Message}");
                }
            }
        }
        finally
        {
            lock (_processQueueLock)
            {
                _isProcessingQueue = false;

                // Pokud se během zpracování objevily nové položky, spustíme zpracování znovu
                if (!_errorQueue.IsEmpty)
                {
                    _isProcessingQueue = true;
                    Task.Run(async () => await ProcessQueueInternalAsync());
                }
            }
        }
    }

    /// <summary>
    /// Převede Microsoft.Extensions.Logging.LogLevel na ApplicationLogLevel
    /// </summary>
    private static ApplicationLogLevel ConvertLogLevel(LogLevel logLevel)
    {
        return logLevel switch
        {
            LogLevel.Trace => ApplicationLogLevel.Trace,
            LogLevel.Debug => ApplicationLogLevel.Debug,
            LogLevel.Information => ApplicationLogLevel.Information,
            LogLevel.Warning => ApplicationLogLevel.Warning,
            LogLevel.Error => ApplicationLogLevel.Error,
            LogLevel.Critical => ApplicationLogLevel.Critical,
            _ => ApplicationLogLevel.Information
        };
    }
}
