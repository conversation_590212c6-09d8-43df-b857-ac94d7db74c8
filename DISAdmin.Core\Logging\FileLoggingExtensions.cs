using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Formatting.Compact;

namespace DISAdmin.Core.Logging;

/// <summary>
/// Rozšiřující metody pro konfiguraci file loggingu
/// </summary>
public static class FileLoggingExtensions
{
    /// <summary>
    /// Přidá Serilog file logging do aplikace
    /// </summary>
    public static IHostBuilder AddFileLogging(this IHostBuilder hostBuilder)
    {
        return hostBuilder.UseSerilog((context, services, configuration) =>
        {
            var fileLoggingOptions = new FileLoggingOptions();
            context.Configuration.GetSection("FileLogging").Bind(fileLoggingOptions);

            // Základní konfigurace Serilog
            configuration
                .ReadFrom.Configuration(context.Configuration)
                .ReadFrom.Services(services)
                .Enrich.FromLogContext()
                .Enrich.WithProperty("Application", "DISAdmin")
                .Enrich.WithProperty("Environment", context.HostingEnvironment.EnvironmentName);

            // Přidání file sink pouze pokud je povoleno
            if (fileLoggingOptions.Enabled)
            {
                ConfigureFileSink(configuration, fileLoggingOptions);
            }

            // Přidání console sink pro development
            if (context.HostingEnvironment.IsDevelopment())
            {
                configuration.WriteTo.Console(
                    restrictedToMinimumLevel: fileLoggingOptions.GetSerilogLogLevel(),
                    outputTemplate: fileLoggingOptions.OutputTemplate);
            }
        });
    }

    /// <summary>
    /// Konfiguruje file sink pro Serilog
    /// </summary>
    private static void ConfigureFileSink(LoggerConfiguration configuration, FileLoggingOptions options)
    {
        // Vytvoření adresáře pokud neexistuje
        if (!Directory.Exists(options.LogDirectory))
        {
            Directory.CreateDirectory(options.LogDirectory);
        }

        var loggerConfig = configuration.WriteTo;

        if (options.UseJsonFormat)
        {
            // JSON formát
            loggerConfig.File(
                formatter: new CompactJsonFormatter(),
                path: options.GetLogFilePath(),
                restrictedToMinimumLevel: options.GetSerilogLogLevel(),
                rollingInterval: options.GetRollingInterval(),
                retainedFileCountLimit: options.RetainedFileCountLimit,
                fileSizeLimitBytes: options.FileSizeLimitBytes,
                rollOnFileSizeLimit: true,
                shared: true,
                flushToDiskInterval: TimeSpan.FromSeconds(1));
        }
        else
        {
            // Textový formát
            loggerConfig.File(
                path: options.GetLogFilePath(),
                restrictedToMinimumLevel: options.GetSerilogLogLevel(),
                outputTemplate: options.OutputTemplate,
                rollingInterval: options.GetRollingInterval(),
                retainedFileCountLimit: options.RetainedFileCountLimit,
                fileSizeLimitBytes: options.FileSizeLimitBytes,
                rollOnFileSizeLimit: true,
                shared: true,
                flushToDiskInterval: TimeSpan.FromSeconds(1));
        }
    }

    /// <summary>
    /// Přidá file logging služby do DI kontejneru
    /// </summary>
    public static IServiceCollection AddFileLoggingServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Registrace konfigurace
        services.Configure<FileLoggingOptions>(configuration.GetSection("FileLogging"));

        return services;
    }

    /// <summary>
    /// Testuje file logging konfiguraci
    /// </summary>
    public static void TestFileLogging(this Microsoft.Extensions.Logging.ILogger logger, FileLoggingOptions options)
    {
        if (!options.Enabled)
        {
            logger.LogInformation("File logging je vypnuto");
            return;
        }

        try
        {
            logger.LogTrace("Test Trace zpráva pro file logging");
            logger.LogDebug("Test Debug zpráva pro file logging");
            logger.LogInformation("Test Information zpráva pro file logging");
            logger.LogWarning("Test Warning zpráva pro file logging");
            logger.LogError("Test Error zpráva pro file logging");
            logger.LogCritical("Test Critical zpráva pro file logging");

            logger.LogInformation("File logging test dokončen. Logy by měly být uloženy v: {LogDirectory}",
                Path.GetFullPath(options.LogDirectory));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Chyba při testování file loggingu");
        }
    }
}
