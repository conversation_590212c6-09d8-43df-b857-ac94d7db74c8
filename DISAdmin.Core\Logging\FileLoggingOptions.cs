using Microsoft.Extensions.Logging;

namespace DISAdmin.Core.Logging;

/// <summary>
/// Konfigurace pro logování do souborů
/// </summary>
public class FileLoggingOptions
{
    /// <summary>
    /// Zda je logování do souborů povoleno
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Adresář pro ukládání log souborů
    /// </summary>
    public string LogDirectory { get; set; } = "Logs";

    /// <summary>
    /// Minimální úroveň logování pro soubory
    /// </summary>
    public LogLevel LogLevel { get; set; } = LogLevel.Information;

    /// <summary>
    /// Počet uchovávaných souborů (rotace)
    /// </summary>
    public int RetainedFileCountLimit { get; set; } = 30;

    /// <summary>
    /// Maximální velikost souboru v bytech
    /// </summary>
    public long FileSizeLimitBytes { get; set; } = 10 * 1024 * 1024; // 10 MB

    /// <summary>
    /// Interval rotace souborů
    /// </summary>
    public string RollingInterval { get; set; } = "Day";

    /// <summary>
    /// Šablona pro formátování log zpráv
    /// </summary>
    public string OutputTemplate { get; set; } = "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}";

    /// <summary>
    /// Zda použít JSON formát
    /// </summary>
    public bool UseJsonFormat { get; set; } = false;

    /// <summary>
    /// Prefix pro názvy log souborů
    /// </summary>
    public string FileNamePrefix { get; set; } = "disadmin";

    /// <summary>
    /// Získání úplné cesty k log souboru
    /// </summary>
    public string GetLogFilePath()
    {
        var fileName = UseJsonFormat 
            ? $"{FileNamePrefix}-.json" 
            : $"{FileNamePrefix}-.log";
        
        return Path.Combine(LogDirectory, fileName);
    }

    /// <summary>
    /// Převod string hodnoty RollingInterval na Serilog enum
    /// </summary>
    public Serilog.RollingInterval GetRollingInterval()
    {
        return RollingInterval.ToLowerInvariant() switch
        {
            "minute" => Serilog.RollingInterval.Minute,
            "hour" => Serilog.RollingInterval.Hour,
            "day" => Serilog.RollingInterval.Day,
            "month" => Serilog.RollingInterval.Month,
            "year" => Serilog.RollingInterval.Year,
            _ => Serilog.RollingInterval.Day
        };
    }

    /// <summary>
    /// Převod Microsoft LogLevel na Serilog LogEventLevel
    /// </summary>
    public Serilog.Events.LogEventLevel GetSerilogLogLevel()
    {
        return LogLevel switch
        {
            LogLevel.Trace => Serilog.Events.LogEventLevel.Verbose,
            LogLevel.Debug => Serilog.Events.LogEventLevel.Debug,
            LogLevel.Information => Serilog.Events.LogEventLevel.Information,
            LogLevel.Warning => Serilog.Events.LogEventLevel.Warning,
            LogLevel.Error => Serilog.Events.LogEventLevel.Error,
            LogLevel.Critical => Serilog.Events.LogEventLevel.Fatal,
            _ => Serilog.Events.LogEventLevel.Information
        };
    }
}
