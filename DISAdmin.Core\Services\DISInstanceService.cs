using System.Security.Cryptography;
using DISAdmin.Core.Data;
using DISAdmin.Core.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DISAdmin.Core.Services;

public class DISInstanceService
{
    private readonly DISAdminDbContext _context;
    private readonly ILogger<DISInstanceService> _logger;

    public DISInstanceService(DISAdminDbContext context, ILogger<DISInstanceService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<List<DISInstance>> GetAllInstancesAsync()
    {
        return await _context.DISInstances
            .Include(i => i.Customer)
            .ToListAsync();
    }

    public async Task<List<DISInstance>> GetCustomerInstancesAsync(int customerId)
    {
        return await _context.DISInstances
            .Where(i => i.CustomerId == customerId)
            .ToListAsync();
    }

    public async Task<DISInstance?> GetInstanceByIdAsync(int id)
    {
        return await _context.DISInstances
            .Include(i => i.Customer)
            .Include(i => i.InstanceVersions)
                .ThenInclude(iv => iv.Version)
            .Include(i => i.InstanceVersions)
                .ThenInclude(iv => iv.InstalledByUser)
            .FirstOrDefaultAsync(i => i.Id == id);
    }

    public async Task<DISInstance?> GetInstanceByApiKeyAsync(string apiKey)
    {
        return await _context.DISInstances
            .Include(i => i.Customer)
            .FirstOrDefaultAsync(i => i.ApiKey == apiKey);
    }

    public async Task<DISInstance> CreateInstanceAsync(DISInstance instance)
    {
        _logger.LogInformation("Zahájeno vytváření instance v databázi. Název: {InstanceName}, Zákazník ID: {CustomerId}",
            instance.Name, instance.CustomerId);

        try
        {
            // Generování API klíče
            _logger.LogInformation("Generování API klíče pro instanci: {InstanceName}", instance.Name);
            instance.ApiKey = GenerateApiKey();
            instance.InstallationDate = DateTime.UtcNow;

            _logger.LogInformation("API klíč vygenerován: {ApiKeyPrefix}..., Datum instalace: {InstallationDate}",
                instance.ApiKey?.Substring(0, Math.Min(8, instance.ApiKey.Length)), instance.InstallationDate);

            _logger.LogInformation("Přidávání instance do kontextu databáze: {InstanceName}", instance.Name);
            _context.DISInstances.Add(instance);

            _logger.LogInformation("Ukládání změn do databáze pro instanci: {InstanceName}", instance.Name);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Instance úspěšně uložena do databáze. ID: {InstanceId}, Název: {InstanceName}, API klíč: {ApiKeyPrefix}...",
                instance.Id, instance.Name, instance.ApiKey?.Substring(0, Math.Min(8, instance.ApiKey.Length)));

            return instance;
        }
        catch (DbUpdateException ex)
        {
            _logger.LogError(ex, "Databázová chyba při vytváření instance. Název: {InstanceName}, Zákazník ID: {CustomerId}, Inner Exception: {InnerException}",
                instance.Name, instance.CustomerId, ex.InnerException?.Message);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Neočekávaná chyba při vytváření instance. Název: {InstanceName}, Zákazník ID: {CustomerId}, Exception Type: {ExceptionType}",
                instance.Name, instance.CustomerId, ex.GetType().FullName);
            throw;
        }
    }

    public async Task UpdateInstanceAsync(DISInstance instance)
    {
        var existingInstance = await _context.DISInstances.FindAsync(instance.Id);
        if (existingInstance == null)
        {
            throw new InvalidOperationException("Instance nebyla nalezena.");
        }

        // Aktualizace základních vlastností
        existingInstance.Name = instance.Name;
        existingInstance.ServerUrl = instance.ServerUrl;
        existingInstance.Notes = instance.Notes;

        // Aktualizace statusu a data expirace
        existingInstance.Status = instance.Status;
        existingInstance.BlockReason = instance.BlockReason;
        existingInstance.ExpirationDate = instance.ExpirationDate;

        // Aktualizace modulů
        existingInstance.ModuleReporting = instance.ModuleReporting;
        existingInstance.ModuleAdvancedSecurity = instance.ModuleAdvancedSecurity;
        existingInstance.ModuleApiIntegration = instance.ModuleApiIntegration;
        existingInstance.ModuleDataExport = instance.ModuleDataExport;
        existingInstance.ModuleCustomization = instance.ModuleCustomization;

        // Explicitní uložení změn
        await _context.SaveChangesAsync();
    }

    public async Task DeleteInstanceAsync(int id)
    {
        var instance = await _context.DISInstances.FindAsync(id);
        if (instance == null)
        {
            throw new InvalidOperationException("Instance nebyla nalezena.");
        }

        _context.DISInstances.Remove(instance);
        await _context.SaveChangesAsync();
        return;
    }

    public async Task RegenerateApiKeyAsync(int instanceId)
    {
        var instance = await _context.DISInstances.FindAsync(instanceId);
        if (instance == null)
        {
            throw new InvalidOperationException("Instance nebyla nalezena.");
        }

        instance.ApiKey = GenerateApiKey();
        await _context.SaveChangesAsync();
    }

    // Metody pro správu verzí instancí
    public async Task<List<InstanceVersion>> GetInstanceVersionsAsync(int instanceId)
    {
        return await _context.InstanceVersions
            .Include(iv => iv.Version)
            .Include(iv => iv.InstalledByUser)
            .Where(iv => iv.InstanceId == instanceId)
            .OrderByDescending(iv => iv.InstalledAt)
            .ToListAsync();
    }

    /// <summary>
    /// Získá nejnovější verzi instance
    /// </summary>
    /// <param name="instanceId">ID instance</param>
    /// <returns>Nejnovější verze instance nebo null, pokud instance nemá žádnou verzi</returns>
    public async Task<InstanceVersion?> GetLatestInstanceVersionAsync(int instanceId)
    {
        return await _context.InstanceVersions
            .Include(iv => iv.Version)
            .Include(iv => iv.InstalledByUser)
            .Where(iv => iv.InstanceId == instanceId)
            .OrderByDescending(iv => iv.InstalledAt)
            .FirstOrDefaultAsync();
    }

    public async Task<InstanceVersion> AddInstanceVersionAsync(InstanceVersion instanceVersion)
    {
        instanceVersion.InstalledAt = DateTime.UtcNow;

        _context.InstanceVersions.Add(instanceVersion);
        await _context.SaveChangesAsync();

        return instanceVersion;
    }

    // Metody pro správu diagnostických logů
    public async Task<List<DiagnosticLog>> GetInstanceLogsAsync(int instanceId, int count = 100)
    {
        return await _context.DiagnosticLogs
            .Where(dl => dl.InstanceId == instanceId)
            .OrderByDescending(dl => dl.Timestamp)
            .Take(count)
            .ToListAsync();
    }

    public async Task<DiagnosticLog> AddDiagnosticLogAsync(DiagnosticLog log)
    {
        log.Timestamp = DateTime.UtcNow;

        _context.DiagnosticLogs.Add(log);
        await _context.SaveChangesAsync();

        return log;
    }

    private static string GenerateApiKey()
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[32];
        rng.GetBytes(bytes);
        return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
    }

    /// <summary>
    /// Získání ID instance podle API klíče
    /// </summary>
    public async Task<int?> GetInstanceIdFromApiKeyAsync(string apiKey)
    {
        if (string.IsNullOrEmpty(apiKey))
            return null;

        var instance = await _context.DISInstances
            .FirstOrDefaultAsync(i => i.ApiKey == apiKey);

        return instance?.Id;
    }

    // Statická keš pro systémového uživatele
    private static User? _systemUser = null;

    /// <summary>
    /// Získá systémového uživatele (system)
    /// </summary>
    /// <returns>Systémový uživatel nebo null, pokud neexistuje</returns>
    public async Task<User?> GetSystemUserAsync()
    {
        // Pokud je uživatel již v keši, vrátíme ho
        if (_systemUser != null)
        {
            return _systemUser;
        }

        // Jinak načteme systémového uživatele z databáze
        var systemUser = await _context.Users
            .FirstOrDefaultAsync(u => u.Username == "system" && u.IsSystem);

        // Pokud systémový uživatel neexistuje, pokusíme se ho vytvořit
        if (systemUser == null)
        {
            try
            {
                systemUser = new User
                {
                    Username = "system",
                    Email = "<EMAIL>",
                    PasswordHash = string.Empty,
                    FirstName = "Systém",
                    LastName = string.Empty,
                    IsAdmin = true,
                    IsSystem = true,
                    IsHidden = true,
                    CreatedAt = DateTime.UtcNow
                };

                _context.Users.Add(systemUser);
                await _context.SaveChangesAsync();
            }
            catch (Exception)
            {
                // Pokud se nepodaří vytvořit systémového uživatele, zkusíme ho znovu načíst
                // (může nastat situace, kdy ho mezitím vytvořil jiný proces)
                systemUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == "system" && u.IsSystem);
            }
        }

        // Uložíme uživatele do keše
        _systemUser = systemUser;

        return systemUser;
    }
}
