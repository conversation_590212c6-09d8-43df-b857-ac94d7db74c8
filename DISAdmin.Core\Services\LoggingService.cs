using DISAdmin.Core.Data;
using DISAdmin.Core.Data.Entities;
using Microsoft.EntityFrameworkCore;

namespace DISAdmin.Core.Services;

public class LoggingService
{
    private readonly DISAdminDbContext _context;

    public LoggingService(DISAdminDbContext context)
    {
        _context = context;
    }

    // Metody pro logování aktivit
    public async Task LogActivityAsync(ActivityLog activityLog)
    {
        activityLog.Timestamp = DateTime.UtcNow;
        _context.ActivityLogs.Add(activityLog);
        await _context.SaveChangesAsync();
    }

    public async Task<List<ActivityLog>> GetActivityLogsAsync(int? userId = null, DateTime? fromDate = null, DateTime? toDate = null, string? entityName = null, int? entityId = null, ActivityType? activityType = null, int? source = null, int maxResults = 100)
    {
        IQueryable<ActivityLog> query = _context.ActivityLogs
            .Include(al => al.User)
            .OrderByDescending(al => al.Timestamp);

        if (userId.HasValue)
        {
            query = query.Where(al => al.UserId == userId);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(al => al.Timestamp >= fromDate);
        }

        if (toDate.HasValue)
        {
            query = query.Where(al => al.Timestamp <= toDate);
        }

        // Filtrování podle entity
        if (!string.IsNullOrEmpty(entityName))
        {
            query = query.Where(al => al.EntityName.Contains(entityName));
        }

        // Filtrování podle ID entity
        if (entityId.HasValue)
        {
            query = query.Where(al => al.EntityId == entityId);
        }

        // Filtrování podle typu aktivity
        if (activityType.HasValue)
        {
            query = query.Where(al => al.ActivityType == activityType);
        }

        // Filtrování podle zdroje jako int hodnoty
        if (source.HasValue)
        {
            query = query.Where(al => (int)al.Source == source.Value);
        }

        return await query.Take(maxResults).ToListAsync();
    }

    public async Task<List<ActivityLog>> GetEntityActivityLogsAsync(string entityName, int entityId, int maxResults = 100)
    {
        return await _context.ActivityLogs
            .Include(al => al.User)
            .Where(al => al.EntityName == entityName && al.EntityId == entityId)
            .OrderByDescending(al => al.Timestamp)
            .Take(maxResults)
            .ToListAsync();
    }

    // Metody pro logování chyb
    public async Task LogErrorAsync(ErrorLog errorLog)
    {
        errorLog.Timestamp = DateTime.UtcNow;
        _context.ErrorLogs.Add(errorLog);
        await _context.SaveChangesAsync();
    }

    public async Task<List<ErrorLog>> GetErrorLogsAsync(DateTime? fromDate = null, DateTime? toDate = null, int? logLevel = null, string? category = null, int maxResults = 100)
    {
        IQueryable<ErrorLog> query = _context.ErrorLogs
            .Include(el => el.User)
            .OrderByDescending(el => el.Timestamp);

        if (fromDate.HasValue)
        {
            query = query.Where(el => el.Timestamp >= fromDate);
        }

        if (toDate.HasValue)
        {
            query = query.Where(el => el.Timestamp <= toDate);
        }

        if (logLevel.HasValue)
        {
            query = query.Where(el => (int)el.LogLevel == logLevel.Value);
        }

        if (!string.IsNullOrEmpty(category))
        {
            query = query.Where(el => el.Category != null && el.Category.Contains(category));
        }

        return await query.Take(maxResults).ToListAsync();
    }

    // Metody pro výkonnostní metriky
    public async Task LogPerformanceMetricAsync(PerformanceMetric metric)
    {
        // Pokud není nastaveno Timestamp, použijeme aktuální čas
        if (metric.Timestamp == default)
        {
            metric.Timestamp = DateTime.UtcNow;
        }

        _context.PerformanceMetrics.Add(metric);
        await _context.SaveChangesAsync();
    }

    /// <summary>
    /// Uloží kolekci výkonnostních metrik do databáze
    /// </summary>
    /// <param name="metrics">Kolekce výkonnostních metrik</param>
    public async Task LogPerformanceMetricsAsync(List<PerformanceMetric> metrics)
    {
        // Kontrola časových razítek a nastavení aktuálního času, pokud není nastaveno
        foreach (var metric in metrics)
        {
            if (metric.Timestamp == default)
            {
                metric.Timestamp = DateTime.UtcNow;
            }
        }

        _context.PerformanceMetrics.AddRange(metrics);
        await _context.SaveChangesAsync();
    }



    public async Task<List<PerformanceMetric>> GetInstancePerformanceMetricsAsync(int instanceId, string? methodName = null, DateTime? fromDate = null, DateTime? toDate = null, int maxResults = 100)
    {
        IQueryable<PerformanceMetric> query = _context.PerformanceMetrics
            .Where(pm => pm.InstanceId == instanceId)
            .OrderByDescending(pm => pm.Timestamp);

        if (!string.IsNullOrEmpty(methodName))
        {
            query = query.Where(pm => pm.MethodName == methodName);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(pm => pm.Timestamp >= fromDate);
        }

        if (toDate.HasValue)
        {
            query = query.Where(pm => pm.Timestamp <= toDate);
        }

        return await query.Take(maxResults).ToListAsync();
    }

    public async Task<Dictionary<string, double>> GetAveragePerformanceMetricsAsync(int instanceId, DateTime? fromDate = null, DateTime? toDate = null)
    {
        IQueryable<PerformanceMetric> query = _context.PerformanceMetrics
            .Where(pm => pm.InstanceId == instanceId);

        if (fromDate.HasValue)
        {
            query = query.Where(pm => pm.Timestamp >= fromDate);
        }

        if (toDate.HasValue)
        {
            query = query.Where(pm => pm.Timestamp <= toDate);
        }

        var result = await query
            .GroupBy(pm => new { pm.ClassName, pm.MethodName })
            .Select(g => new
            {
                ClassName = g.Key.ClassName,
                MethodName = g.Key.MethodName,
                AverageExecutionTime = g.Average(pm => pm.Avg)
            })
            .ToListAsync();

        return result.ToDictionary(r => $"{r.ClassName}.{r.MethodName}", r => (double)r.AverageExecutionTime);
    }

    /// <summary>
    /// Získá statistiky výkonu metod pro instanci
    /// </summary>
    public async Task<List<PerformanceMetric>> GetPerformanceStatisticsAsync(int instanceId, DateTime? fromDate = null, DateTime? toDate = null)
    {
        IQueryable<PerformanceMetric> query = _context.PerformanceMetrics
            .Where(pm => pm.InstanceId == instanceId);

        if (fromDate.HasValue)
        {
            query = query.Where(pm => pm.Timestamp >= fromDate);
        }

        if (toDate.HasValue)
        {
            query = query.Where(pm => pm.Timestamp <= toDate);
        }

        // Seskupení podle třídy a metody a výpočet agregovaných hodnot
        var result = await query
            .GroupBy(pm => new { pm.ClassName, pm.MethodName })
            .Select(g => new PerformanceMetric
            {
                InstanceId = instanceId,
                ClassName = g.Key.ClassName,
                MethodName = g.Key.MethodName,
                TotalCount = g.Sum(pm => pm.TotalCount),
                NonZeroCount = g.Sum(pm => pm.NonZeroCount),
                Min = g.Min(pm => pm.Min),
                Max = g.Max(pm => pm.Max),
                Avg = (long)g.Average(pm => pm.Avg),
                Median = (long)g.Average(pm => pm.Median),
                Percentil95 = (long)g.Average(pm => pm.Percentil95),
                Percentil99 = (long)g.Average(pm => pm.Percentil99),
                StdDev = (long)g.Average(pm => pm.StdDev),
                Timestamp = DateTime.UtcNow // Aktuální čas jako timestamp pro agregované hodnoty
            })
            .OrderByDescending(pm => pm.Avg)
            .ToListAsync();

        return result;
    }
}
