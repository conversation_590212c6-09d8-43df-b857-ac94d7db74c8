2025-05-29 15:34:49.422 +02:00 [INF] Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager: User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-29 15:34:49.578 +02:00 [INF] Program: DISApi aplikace se spouští...
2025-05-29 15:34:49.581 +02:00 [INF] Program: Test Information zpráva pro file logging
2025-05-29 15:34:49.582 +02:00 [WRN] Program: Test Warning zpráva pro file logging
2025-05-29 15:34:49.584 +02:00 [ERR] Program: Test Error zpráva pro file logging
2025-05-29 15:34:49.587 +02:00 [FTL] Program: Test Critical zpráva pro file logging
2025-05-29 15:34:49.589 +02:00 [INF] Program: File logging test dokončen. Logy by m<PERSON><PERSON> b<PERSON><PERSON> v: C:\Users\<USER>\Documents\VSCodeProjects\DISAdminAugment\DISAdmin.DISApi\Logs
2025-05-29 15:34:49.599 +02:00 [INF] DISAdmin.Api.Services.UnhandledExceptionService: UnhandledExceptionService byl spuštěn
2025-05-29 15:34:52.067 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 15:34:52.086 +02:00 [INF] Microsoft.EntityFrameworkCore.Migrations: Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-05-29 15:34:52.119 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-05-29 15:34:52.238 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-05-29 15:34:52.256 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 15:34:52.261 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-05-29 15:34:52.272 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-05-29 15:34:52.283 +02:00 [INF] Microsoft.EntityFrameworkCore.Migrations: No migrations were applied. The database is already up to date.
2025-05-29 15:34:52.291 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-05-29 15:34:52.594 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-05-29 15:34:52.636 +02:00 [INF] DISAdmin.Core.Services.EncryptionService: EncryptionService initialized with key size: 32 bytes, IV size: 16 bytes
2025-05-29 15:34:52.849 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (30ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 15:34:52.876 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 15:34:52.883 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (2ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 15:34:52.897 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 15:34:52.903 +02:00 [INF] DISAdmin.Core.Services.ServerCertificateService: Načítání certifikátu z databáze (AppSettings) s thumbprintem 899BFB9199F2EB6B9A4F8683B098ACE3B9541417
2025-05-29 15:34:52.969 +02:00 [WRN] Microsoft.AspNetCore.Server.Kestrel: Overriding address(es) 'https://localhost:7177, http://localhost:5177'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-05-29 15:34:52.983 +02:00 [INF] Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware: Certificate with thumbprint 899BFB9199F2EB6B9A4F8683B098ACE3B9541417 lacks the subjectAlternativeName (SAN) extension and may not be accepted by browsers.
2025-05-29 15:34:53.026 +02:00 [INF] Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware: Certificate with thumbprint 899BFB9199F2EB6B9A4F8683B098ACE3B9541417 lacks the subjectAlternativeName (SAN) extension and may not be accepted by browsers.
2025-05-29 15:34:53.030 +02:00 [INF] Microsoft.Hosting.Lifetime: Now listening on: http://localhost:5177
2025-05-29 15:34:53.031 +02:00 [INF] Microsoft.Hosting.Lifetime: Now listening on: https://localhost:7177
2025-05-29 15:34:53.033 +02:00 [INF] Microsoft.Hosting.Lifetime: Application started. Press Ctrl+C to shut down.
2025-05-29 15:34:53.035 +02:00 [INF] Microsoft.Hosting.Lifetime: Hosting environment: Development
2025-05-29 15:34:53.036 +02:00 [INF] Microsoft.Hosting.Lifetime: Content root path: C:\Users\<USER>\Documents\VSCodeProjects\DISAdminAugment\DISAdmin.DISApi
2025-05-29 15:53:31.125 +02:00 [INF] Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager: User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-29 15:53:31.299 +02:00 [INF] DISAdmin.Api.Services.UnhandledExceptionService: UnhandledExceptionService byl spuštěn
2025-05-29 15:53:32.935 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 15:53:32.950 +02:00 [INF] Microsoft.EntityFrameworkCore.Migrations: Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-05-29 15:53:32.977 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-05-29 15:53:33.058 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-05-29 15:53:33.070 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 15:53:33.075 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-05-29 15:53:33.085 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-05-29 15:53:33.101 +02:00 [INF] Microsoft.EntityFrameworkCore.Migrations: No migrations were applied. The database is already up to date.
2025-05-29 15:53:33.109 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-05-29 15:53:33.364 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-05-29 15:53:33.399 +02:00 [INF] DISAdmin.Core.Services.EncryptionService: EncryptionService initialized with key size: 32 bytes, IV size: 16 bytes
2025-05-29 15:53:33.552 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (24ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 15:53:33.571 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 15:53:33.578 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 15:53:33.588 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 15:53:33.592 +02:00 [INF] DISAdmin.Core.Services.ServerCertificateService: Načítání certifikátu z databáze (AppSettings) s thumbprintem 899BFB9199F2EB6B9A4F8683B098ACE3B9541417
2025-05-29 15:53:33.643 +02:00 [WRN] Microsoft.AspNetCore.Server.Kestrel: Overriding address(es) 'https://localhost:7177, http://localhost:5177'. Binding to endpoints defined via IConfiguration and/or UseKestrel() instead.
2025-05-29 15:53:33.655 +02:00 [INF] Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware: Certificate with thumbprint 899BFB9199F2EB6B9A4F8683B098ACE3B9541417 lacks the subjectAlternativeName (SAN) extension and may not be accepted by browsers.
2025-05-29 15:53:33.700 +02:00 [INF] Microsoft.AspNetCore.Server.Kestrel.Https.Internal.HttpsConnectionMiddleware: Certificate with thumbprint 899BFB9199F2EB6B9A4F8683B098ACE3B9541417 lacks the subjectAlternativeName (SAN) extension and may not be accepted by browsers.
2025-05-29 15:53:33.705 +02:00 [INF] Microsoft.Hosting.Lifetime: Now listening on: http://localhost:5177
2025-05-29 15:53:33.706 +02:00 [INF] Microsoft.Hosting.Lifetime: Now listening on: https://localhost:7177
2025-05-29 15:53:33.708 +02:00 [INF] Microsoft.Hosting.Lifetime: Application started. Press Ctrl+C to shut down.
2025-05-29 15:53:33.709 +02:00 [INF] Microsoft.Hosting.Lifetime: Hosting environment: Development
2025-05-29 15:53:33.710 +02:00 [INF] Microsoft.Hosting.Lifetime: Content root path: C:\Users\<USER>\Documents\VSCodeProjects\DISAdminAugment\DISAdmin.DISApi
2025-05-29 19:36:52.851 +02:00 [INF] DISAdmin.Core.Services.EncryptionService: EncryptionService initialized with key size: 32 bytes, IV size: 16 bytes
2025-05-29 19:36:53.281 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (17ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 19:36:53.288 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 19:36:53.300 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 19:36:53.309 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 19:36:53.317 +02:00 [INF] DISAdmin.Core.Services.ServerCertificateService: Načítání certifikátu z databáze (AppSettings) s thumbprintem 899BFB9199F2EB6B9A4F8683B098ACE3B9541417
2025-05-29 19:36:53.371 +02:00 [INF] DISAdmin.Core.Services.EncryptionService: EncryptionService initialized with key size: 32 bytes, IV size: 16 bytes
2025-05-29 19:36:53.386 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 19:36:53.392 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 19:36:53.400 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 19:36:53.409 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 19:36:53.415 +02:00 [INF] DISAdmin.Core.Services.ServerCertificateService: Načítání certifikátu z databáze (AppSettings) s thumbprintem 899BFB9199F2EB6B9A4F8683B098ACE3B9541417
2025-05-29 19:36:53.629 +02:00 [INF] DISAdmin.Core.Services.EncryptionService: EncryptionService initialized with key size: 32 bytes, IV size: 16 bytes
2025-05-29 19:36:53.647 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 19:36:53.653 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 19:36:53.668 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 19:36:53.684 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__key_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[Category], [a].[Key], [a].[LastModified], [a].[ModifiedBy], [a].[Type], [a].[Value]
FROM [AppSettings] AS [a]
WHERE [a].[Key] = @__key_0
2025-05-29 19:36:53.700 +02:00 [INF] DISAdmin.Core.Services.ServerCertificateService: Načítání certifikátu z databáze (AppSettings) s thumbprintem 899BFB9199F2EB6B9A4F8683B098ACE3B9541417
