{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}, "Database": {"Enabled": true, "LogLevel": "Warning", "Source": "DISApi"}}, "FileLogging": {"Enabled": true, "LogDirectory": "Logs", "LogLevel": "Information", "RetainedFileCountLimit": 30, "FileSizeLimitBytes": 10485760, "RollingInterval": "Day", "OutputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}", "FileNamePrefix": "disapi"}, "ConnectionStrings": {"DefaultConnection": "Data Source=VITA-DELL\\SQL16DEV;Initial Catalog=DISAdminAugment;Integrated Security=True;MultipleActiveResultSets=True;Connection Timeout=180;TrustServerCertificate=True"}, "AllowedHosts": "*", "Security": {"SkipCertValidation": false}, "Kestrel": {"Endpoints": {"Https": {"Url": "https://localhost:7177", "ClientCertificateMode": "AllowCertificate"}, "Http": {"Url": "http://localhost:5177"}}}}