﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DISAdmin.Migrations.Migrations
{
    /// <inheritdoc />
    public partial class AddLogLevelToErrorLog : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Category",
                table: "ErrorLogs",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "LogLevel",
                table: "ErrorLogs",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Category",
                table: "ErrorLogs");

            migrationBuilder.DropColumn(
                name: "LogLevel",
                table: "ErrorLogs");
        }
    }
}
