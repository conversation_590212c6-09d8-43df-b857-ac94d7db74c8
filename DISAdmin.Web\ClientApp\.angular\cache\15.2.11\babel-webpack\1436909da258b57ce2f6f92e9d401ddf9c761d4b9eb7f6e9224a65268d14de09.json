{"ast": null, "code": "export var LogSource;\n(function (LogSource) {\n  LogSource[LogSource[\"Undefined\"] = 0] = \"Undefined\";\n  LogSource[LogSource[\"DISAdmin\"] = 1] = \"DISAdmin\";\n  LogSource[LogSource[\"DISApi\"] = 2] = \"DISApi\";\n})(LogSource || (LogSource = {}));\nexport var ApplicationLogLevel;\n(function (ApplicationLogLevel) {\n  ApplicationLogLevel[ApplicationLogLevel[\"Trace\"] = 0] = \"Trace\";\n  ApplicationLogLevel[ApplicationLogLevel[\"Debug\"] = 1] = \"Debug\";\n  ApplicationLogLevel[ApplicationLogLevel[\"Information\"] = 2] = \"Information\";\n  ApplicationLogLevel[ApplicationLogLevel[\"Warning\"] = 3] = \"Warning\";\n  ApplicationLogLevel[ApplicationLogLevel[\"Error\"] = 4] = \"Error\";\n  ApplicationLogLevel[ApplicationLogLevel[\"Critical\"] = 5] = \"Critical\";\n})(ApplicationLogLevel || (ApplicationLogLevel = {}));\nexport var ActivityTypeEnum;\n(function (ActivityTypeEnum) {\n  ActivityTypeEnum[\"Login\"] = \"Login\";\n  ActivityTypeEnum[\"Logout\"] = \"Logout\";\n  ActivityTypeEnum[\"Create\"] = \"Create\";\n  ActivityTypeEnum[\"Update\"] = \"Update\";\n  ActivityTypeEnum[\"Delete\"] = \"Delete\";\n  ActivityTypeEnum[\"Export\"] = \"Export\";\n  ActivityTypeEnum[\"Import\"] = \"Import\";\n  ActivityTypeEnum[\"PasswordChange\"] = \"PasswordChange\";\n  ActivityTypeEnum[\"ApiAccess\"] = \"ApiAccess\";\n  ActivityTypeEnum[\"Other\"] = \"Other\";\n})(ActivityTypeEnum || (ActivityTypeEnum = {}));\n// Pomocná třída pro překlad typů aktivit do češtiny\nexport class ActivityTypeHelper {\n  static getLocalizedName(type) {\n    switch (type) {\n      case 'Login':\n        return 'Přihlášení';\n      case 'Logout':\n        return 'Odhlášení';\n      case 'Create':\n        return 'Vytvoření';\n      case 'Update':\n        return 'Úprava';\n      case 'Delete':\n        return 'Smazání';\n      case 'Export':\n        return 'Export';\n      case 'Import':\n        return 'Import';\n      case 'PasswordChange':\n        return 'Změna hesla';\n      case 'ApiAccess':\n        return 'Přístup k API';\n      case 'Other':\n        return 'Ostatní';\n      default:\n        return type;\n    }\n  }\n  static getActivityTypes() {\n    return [{\n      value: 'Login',\n      label: 'Přihlášení'\n    }, {\n      value: 'Logout',\n      label: 'Odhlášení'\n    }, {\n      value: 'Create',\n      label: 'Vytvoření'\n    }, {\n      value: 'Update',\n      label: 'Úprava'\n    }, {\n      value: 'Delete',\n      label: 'Smazání'\n    }, {\n      value: 'Export',\n      label: 'Export'\n    }, {\n      value: 'Import',\n      label: 'Import'\n    }, {\n      value: 'PasswordChange',\n      label: 'Změna hesla'\n    }, {\n      value: 'ApiAccess',\n      label: 'Přístup k API'\n    }, {\n      value: 'Other',\n      label: 'Ostatní'\n    }];\n  }\n}\n// Pomocná třída pro překlad zdrojů logů do češtiny\nexport class LogSourceHelper {\n  static getLocalizedName(source) {\n    switch (source) {\n      case LogSource.DISAdmin:\n        return 'DISAdmin';\n      case LogSource.DISApi:\n        return 'DIS API';\n      case LogSource.Undefined:\n        return 'Neurčeno';\n      default:\n        return 'Neznámý';\n    }\n  }\n  static getLogSources() {\n    return [{\n      value: LogSource.DISAdmin,\n      label: 'DISAdmin'\n    }, {\n      value: LogSource.DISApi,\n      label: 'DIS API'\n    }, {\n      value: LogSource.Undefined,\n      label: 'Neurčeno'\n    }];\n  }\n}\n// Pomocná třída pro práci s úrovněmi logování\nexport class ApplicationLogLevelHelper {\n  static getLocalizedName(logLevel) {\n    switch (logLevel) {\n      case 'Trace':\n        return 'Trasování';\n      case 'Debug':\n        return 'Ladění';\n      case 'Information':\n        return 'Informace';\n      case 'Warning':\n        return 'Varování';\n      case 'Error':\n        return 'Chyba';\n      case 'Critical':\n        return 'Kritická';\n      default:\n        return logLevel;\n    }\n  }\n  static getLogLevels() {\n    return [{\n      value: ApplicationLogLevel.Trace,\n      label: 'Trasování'\n    }, {\n      value: ApplicationLogLevel.Debug,\n      label: 'Ladění'\n    }, {\n      value: ApplicationLogLevel.Information,\n      label: 'Informace'\n    }, {\n      value: ApplicationLogLevel.Warning,\n      label: 'Varování'\n    }, {\n      value: ApplicationLogLevel.Error,\n      label: 'Chyba'\n    }, {\n      value: ApplicationLogLevel.Critical,\n      label: 'Kritická'\n    }];\n  }\n  static getLogLevelClass(logLevel) {\n    switch (logLevel) {\n      case 'Trace':\n        return 'bg-secondary';\n      case 'Debug':\n        return 'bg-info';\n      case 'Information':\n        return 'bg-primary';\n      case 'Warning':\n        return 'bg-warning';\n      case 'Error':\n        return 'bg-danger';\n      case 'Critical':\n        return 'bg-dark';\n      default:\n        return 'bg-light';\n    }\n  }\n}", "map": {"version": 3, "mappings": "AAoEA,WAAYA,SAIX;AAJD,WAAYA,SAAS;EACnBA,mDAAa;EACbA,iDAAY;EACZA,6CAAU;AACZ,CAAC,EAJWA,SAAS,KAATA,SAAS;AAMrB,WAAYC,mBAOX;AAPD,WAAYA,mBAAmB;EAC7BA,+DAAS;EACTA,+DAAS;EACTA,2EAAe;EACfA,mEAAW;EACXA,+DAAS;EACTA,qEAAY;AACd,CAAC,EAPWA,mBAAmB,KAAnBA,mBAAmB;AAS/B,WAAYC,gBAWX;AAXD,WAAYA,gBAAgB;EAC1BA,mCAAe;EACfA,qCAAiB;EACjBA,qCAAiB;EACjBA,qCAAiB;EACjBA,qCAAiB;EACjBA,qCAAiB;EACjBA,qCAAiB;EACjBA,qDAAiC;EACjCA,2CAAuB;EACvBA,mCAAe;AACjB,CAAC,EAXWA,gBAAgB,KAAhBA,gBAAgB;AAa5B;AACA,OAAM,MAAOC,kBAAkB;EAC7B,OAAOC,gBAAgB,CAACC,IAAY;IAClC,QAAQA,IAAI;MACV,KAAK,OAAO;QAAE,OAAO,YAAY;MACjC,KAAK,QAAQ;QAAE,OAAO,WAAW;MACjC,KAAK,QAAQ;QAAE,OAAO,WAAW;MACjC,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,gBAAgB;QAAE,OAAO,aAAa;MAC3C,KAAK,WAAW;QAAE,OAAO,eAAe;MACxC,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B;QAAS,OAAOA,IAAI;IAAC;EAEzB;EAEA,OAAOC,gBAAgB;IACrB,OAAO,CACL;MAAEC,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAY,CAAE,EACvC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAW,CAAE,EACvC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAW,CAAE,EACvC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAS,CAAE,EACrC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAED,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;IAAa,CAAE,EACjD;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAe,CAAE,EAC9C;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAS,CAAE,CACrC;EACH;;AAGF;AACA,OAAM,MAAOC,eAAe;EAC1B,OAAOL,gBAAgB,CAACM,MAAc;IACpC,QAAQA,MAAM;MACZ,KAAKV,SAAS,CAACW,QAAQ;QAAE,OAAO,UAAU;MAC1C,KAAKX,SAAS,CAACY,MAAM;QAAE,OAAO,SAAS;MACvC,KAAKZ,SAAS,CAACa,SAAS;QAAE,OAAO,UAAU;MAC3C;QAAS,OAAO,SAAS;IAAC;EAE9B;EAEA,OAAOC,aAAa;IAClB,OAAO,CACL;MAAEP,KAAK,EAAEP,SAAS,CAACW,QAAQ;MAAEH,KAAK,EAAE;IAAU,CAAE,EAChD;MAAED,KAAK,EAAEP,SAAS,CAACY,MAAM;MAAEJ,KAAK,EAAE;IAAS,CAAE,EAC7C;MAAED,KAAK,EAAEP,SAAS,CAACa,SAAS;MAAEL,KAAK,EAAE;IAAU,CAAE,CAClD;EACH;;AAGF;AACA,OAAM,MAAOO,yBAAyB;EACpC,OAAOX,gBAAgB,CAACY,QAAgB;IACtC,QAAQA,QAAQ;MACd,KAAK,OAAO;QAAE,OAAO,WAAW;MAChC,KAAK,OAAO;QAAE,OAAO,QAAQ;MAC7B,KAAK,aAAa;QAAE,OAAO,WAAW;MACtC,KAAK,SAAS;QAAE,OAAO,UAAU;MACjC,KAAK,OAAO;QAAE,OAAO,OAAO;MAC5B,KAAK,UAAU;QAAE,OAAO,UAAU;MAClC;QAAS,OAAOA,QAAQ;IAAC;EAE7B;EAEA,OAAOC,YAAY;IACjB,OAAO,CACL;MAAEV,KAAK,EAAEN,mBAAmB,CAACiB,KAAK;MAAEV,KAAK,EAAE;IAAW,CAAE,EACxD;MAAED,KAAK,EAAEN,mBAAmB,CAACkB,KAAK;MAAEX,KAAK,EAAE;IAAQ,CAAE,EACrD;MAAED,KAAK,EAAEN,mBAAmB,CAACmB,WAAW;MAAEZ,KAAK,EAAE;IAAW,CAAE,EAC9D;MAAED,KAAK,EAAEN,mBAAmB,CAACoB,OAAO;MAAEb,KAAK,EAAE;IAAU,CAAE,EACzD;MAAED,KAAK,EAAEN,mBAAmB,CAACqB,KAAK;MAAEd,KAAK,EAAE;IAAO,CAAE,EACpD;MAAED,KAAK,EAAEN,mBAAmB,CAACsB,QAAQ;MAAEf,KAAK,EAAE;IAAU,CAAE,CAC3D;EACH;EAEA,OAAOgB,gBAAgB,CAACR,QAAgB;IACtC,QAAQA,QAAQ;MACd,KAAK,OAAO;QAAE,OAAO,cAAc;MACnC,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,aAAa;QAAE,OAAO,YAAY;MACvC,KAAK,SAAS;QAAE,OAAO,YAAY;MACnC,KAAK,OAAO;QAAE,OAAO,WAAW;MAChC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC;QAAS,OAAO,UAAU;IAAC;EAE/B", "names": ["LogSource", "ApplicationLogLevel", "ActivityTypeEnum", "ActivityTypeHelper", "getLocalizedName", "type", "getActivityTypes", "value", "label", "LogSourceHelper", "source", "DISAdmin", "DISApi", "Undefined", "getLogSources", "ApplicationLogLevelHelper", "logLevel", "getLogLevels", "Trace", "Debug", "Information", "Warning", "Error", "Critical", "getLogLevelClass"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\models\\logs.model.ts"], "sourcesContent": ["export interface ActivityLogResponse {\n  id: number;\n  userId?: number;\n  username?: string;\n  timestamp: Date;\n  activityType: string;\n  entityName: string;\n  entityId?: number;\n  description: string;\n  ipAddress: string;\n  additionalInfo?: string;\n  source: number; // Změna ze string na number pro explicitní zdroj\n\n  // Nové sloupce pro API přístupy\n  method?: string;\n  endpoint?: string;\n  statusCode?: number;\n  responseTimeMs?: number;\n}\n\nexport interface ErrorLogResponse {\n  id: number;\n  timestamp: Date;\n  message: string;\n  stackTrace?: string;\n  source: string; // Zůstává string, protože ErrorLog.Source je string v backendu\n  requestPath?: string;\n  requestMethod?: string;\n  statusCode?: number;\n  userId?: number;\n  username?: string;\n  ipAddress: string;\n  additionalInfo?: string;\n  logLevel: string; // Nová vlastnost pro úroveň logování\n  category?: string; // Nová vlastnost pro kategorii loggeru\n}\n\nexport interface LogFilterRequest {\n  userId?: number;\n  username?: string;\n  fromDate?: Date;\n  toDate?: Date;\n  maxResults?: number;\n  entityName?: string;\n  entityId?: number;\n  activityType?: string;\n  ipAddress?: string;\n  source?: number; // Změna ze string na number pro explicitní zdroj\n  logLevel?: number; // Nový filtr pro úroveň logování\n  category?: string; // Nový filtr pro kategorii loggeru\n}\n\nexport interface SavedLogFilterRequest {\n  name: string;\n  logType: string; // activity, error, api\n  filterData: string; // JSON serializovaný LogFilterRequest\n}\n\nexport interface SavedLogFilterResponse {\n  id: number;\n  userId: number;\n  name: string;\n  logType: string;\n  filterData: string;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport enum LogSource {\n  Undefined = 0,\n  DISAdmin = 1,\n  DISApi = 2\n}\n\nexport enum ApplicationLogLevel {\n  Trace = 0,\n  Debug = 1,\n  Information = 2,\n  Warning = 3,\n  Error = 4,\n  Critical = 5\n}\n\nexport enum ActivityTypeEnum {\n  Login = 'Login',\n  Logout = 'Logout',\n  Create = 'Create',\n  Update = 'Update',\n  Delete = 'Delete',\n  Export = 'Export',\n  Import = 'Import',\n  PasswordChange = 'PasswordChange',\n  ApiAccess = 'ApiAccess',\n  Other = 'Other'\n}\n\n// Pomocná třída pro překlad typů aktivit do češtiny\nexport class ActivityTypeHelper {\n  static getLocalizedName(type: string): string {\n    switch (type) {\n      case 'Login': return 'Přihlášení';\n      case 'Logout': return 'Odhlášení';\n      case 'Create': return 'Vytvoření';\n      case 'Update': return 'Úprava';\n      case 'Delete': return 'Smazání';\n      case 'Export': return 'Export';\n      case 'Import': return 'Import';\n      case 'PasswordChange': return 'Změna hesla';\n      case 'ApiAccess': return 'Přístup k API';\n      case 'Other': return 'Ostatní';\n      default: return type;\n    }\n  }\n\n  static getActivityTypes(): { value: string, label: string }[] {\n    return [\n      { value: 'Login', label: 'Přihlášení' },\n      { value: 'Logout', label: 'Odhlášení' },\n      { value: 'Create', label: 'Vytvoření' },\n      { value: 'Update', label: 'Úprava' },\n      { value: 'Delete', label: 'Smazání' },\n      { value: 'Export', label: 'Export' },\n      { value: 'Import', label: 'Import' },\n      { value: 'PasswordChange', label: 'Změna hesla' },\n      { value: 'ApiAccess', label: 'Přístup k API' },\n      { value: 'Other', label: 'Ostatní' }\n    ];\n  }\n}\n\n// Pomocná třída pro překlad zdrojů logů do češtiny\nexport class LogSourceHelper {\n  static getLocalizedName(source: number): string {\n    switch (source) {\n      case LogSource.DISAdmin: return 'DISAdmin';\n      case LogSource.DISApi: return 'DIS API';\n      case LogSource.Undefined: return 'Neurčeno';\n      default: return 'Neznámý';\n    }\n  }\n\n  static getLogSources(): { value: number, label: string }[] {\n    return [\n      { value: LogSource.DISAdmin, label: 'DISAdmin' },\n      { value: LogSource.DISApi, label: 'DIS API' },\n      { value: LogSource.Undefined, label: 'Neurčeno' }\n    ];\n  }\n}\n\n// Pomocná třída pro práci s úrovněmi logování\nexport class ApplicationLogLevelHelper {\n  static getLocalizedName(logLevel: string): string {\n    switch (logLevel) {\n      case 'Trace': return 'Trasování';\n      case 'Debug': return 'Ladění';\n      case 'Information': return 'Informace';\n      case 'Warning': return 'Varování';\n      case 'Error': return 'Chyba';\n      case 'Critical': return 'Kritická';\n      default: return logLevel;\n    }\n  }\n\n  static getLogLevels(): { value: number, label: string }[] {\n    return [\n      { value: ApplicationLogLevel.Trace, label: 'Trasování' },\n      { value: ApplicationLogLevel.Debug, label: 'Ladění' },\n      { value: ApplicationLogLevel.Information, label: 'Informace' },\n      { value: ApplicationLogLevel.Warning, label: 'Varování' },\n      { value: ApplicationLogLevel.Error, label: 'Chyba' },\n      { value: ApplicationLogLevel.Critical, label: 'Kritická' }\n    ];\n  }\n\n  static getLogLevelClass(logLevel: string): string {\n    switch (logLevel) {\n      case 'Trace': return 'bg-secondary';\n      case 'Debug': return 'bg-info';\n      case 'Information': return 'bg-primary';\n      case 'Warning': return 'bg-warning';\n      case 'Error': return 'bg-danger';\n      case 'Critical': return 'bg-dark';\n      default: return 'bg-light';\n    }\n  }\n}\n\nexport interface PerformanceMetricResponse {\n  id: number;\n  instanceId: number;\n  instanceName: string;\n  className: string;\n  methodName: string;\n  parameters?: string;\n  totalCount: number;\n  nonZeroCount: number;\n  min: number;\n  max: number;\n  avg: number;\n  median: number;\n  percentil95: number;\n  percentil99: number;\n  stdDev: number;\n  timestamp: Date;\n  versionNumber?: string;\n}\n\nexport interface PerformanceStatisticsResponse {\n  className: string;\n  methodName: string;\n  averageExecutionTimeMs: number;\n  medianExecutionTimeMs: number;\n  minExecutionTimeMs: number;\n  maxExecutionTimeMs: number;\n  percentil95: number;\n  percentil99: number;\n  stdDev: number;\n  totalCount: number;\n  nonZeroCount: number;\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}