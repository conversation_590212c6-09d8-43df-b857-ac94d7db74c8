{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"../shared/tab-navigation/tab-navigation.component\";\nimport * as i4 from \"./activity-logs/activity-logs.component\";\nimport * as i5 from \"./error-logs/error-logs.component\";\nimport * as i6 from \"./api-logs/api-logs.component\";\nfunction LogsComponent_app_activity_logs_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-activity-logs\");\n  }\n}\nfunction LogsComponent_app_error_logs_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-error-logs\");\n  }\n}\nfunction LogsComponent_app_api_logs_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-api-logs\");\n  }\n}\nexport let LogsComponent = /*#__PURE__*/(() => {\n  class LogsComponent {\n    constructor(router, route) {\n      this.router = router;\n      this.route = route;\n      this.activeTab = 'activity';\n      // Definice záložek\n      this.tabs = [{\n        id: 'activity',\n        label: 'Auditní logy',\n        icon: 'activity'\n      }, {\n        id: 'error',\n        label: 'Logy aplikace',\n        icon: 'exclamation-triangle'\n      }, {\n        id: 'api',\n        label: 'Logy DIS API',\n        icon: 'hdd-network'\n      }];\n      // Ukládání stavu filtrů pro jednotlivé záložky\n      this.activityFilterState = null;\n      this.errorFilterState = null;\n      this.apiFilterState = null;\n    }\n    ngOnInit() {\n      // Získání aktivní záložky z URL parametru nebo z localStorage\n      this.route.queryParams.subscribe(params => {\n        if (params['tab']) {\n          // Priorita: URL parametr\n          this.activeTab = params['tab'];\n          // Uložení do localStorage pro budoucí použití\n          this.saveActiveTabToLocalStorage(params['tab']);\n        } else {\n          // Pokud není v URL, zkusíme načíst z localStorage\n          const lastActiveTab = this.loadActiveTabFromLocalStorage();\n          if (lastActiveTab) {\n            this.activeTab = lastActiveTab;\n            // Aktualizace URL parametru\n            this.router.navigate([], {\n              relativeTo: this.route,\n              queryParams: {\n                tab: lastActiveTab\n              },\n              queryParamsHandling: 'merge'\n            });\n          }\n        }\n      });\n    }\n    /**\r\n     * Změna aktivní záložky\r\n     */\n    changeTab(tab) {\n      // Uložení stavu aktuálního filtru před přepnutím záložky\n      this.saveCurrentFilterState();\n      // Převod parametru na string, pokud není\n      const tabId = typeof tab === 'string' ? tab : String(tab);\n      this.activeTab = tabId;\n      // Uložení aktivní záložky do localStorage\n      this.saveActiveTabToLocalStorage(tabId);\n      // Aktualizace URL parametru\n      this.router.navigate([], {\n        relativeTo: this.route,\n        queryParams: {\n          tab: tabId\n        },\n        queryParamsHandling: 'merge'\n      });\n    }\n    /**\r\n     * Uložení stavu aktuálního filtru\r\n     */\n    saveCurrentFilterState() {\n      // Uložení stavu filtru podle aktuální záložky\n      switch (this.activeTab) {\n        case 'activity':\n          this.activityFilterState = this.getFilterState('activity');\n          break;\n        case 'error':\n          this.errorFilterState = this.getFilterState('error');\n          break;\n        case 'api':\n          this.apiFilterState = this.getFilterState('api');\n          break;\n      }\n    }\n    /**\r\n     * Získání stavu filtru pro daný typ logu\r\n     */\n    getFilterState(logType) {\n      // Získání stavu filtru z localStorage\n      try {\n        const filterKey = `current_filter_${logType}`;\n        const filterJson = localStorage.getItem(filterKey);\n        if (filterJson) {\n          return JSON.parse(filterJson);\n        }\n      } catch (error) {\n        console.error(`Chyba při načítání stavu filtru pro ${logType}`, error);\n      }\n      return null;\n    }\n    /**\r\n     * Uložení aktivní záložky do localStorage\r\n     */\n    saveActiveTabToLocalStorage(tabId) {\n      try {\n        localStorage.setItem('logs_active_tab', tabId);\n        console.log(`Uložena aktivní záložka do localStorage: ${tabId}`);\n      } catch (error) {\n        console.error('Chyba při ukládání aktivní záložky do localStorage:', error);\n      }\n    }\n    /**\r\n     * Načtení aktivní záložky z localStorage\r\n     */\n    loadActiveTabFromLocalStorage() {\n      try {\n        const activeTab = localStorage.getItem('logs_active_tab');\n        console.log(`Načtena aktivní záložka z localStorage: ${activeTab}`);\n        return activeTab;\n      } catch (error) {\n        console.error('Chyba při načítání aktivní záložky z localStorage:', error);\n        return null;\n      }\n    }\n    static {\n      this.ɵfac = function LogsComponent_Factory(t) {\n        return new (t || LogsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LogsComponent,\n        selectors: [[\"app-logs\"]],\n        decls: 9,\n        vars: 6,\n        consts: [[1, \"container\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [3, \"tabs\", \"activeTabId\", \"tabChange\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"]],\n        template: function LogsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n            i0.ɵɵtext(3, \"Logy syst\\u00E9mu\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(4, \"app-tab-navigation\", 2);\n            i0.ɵɵlistener(\"tabChange\", function LogsComponent_Template_app_tab_navigation_tabChange_4_listener($event) {\n              return ctx.changeTab($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"div\", 3);\n            i0.ɵɵtemplate(6, LogsComponent_app_activity_logs_6_Template, 1, 0, \"app-activity-logs\", 4);\n            i0.ɵɵtemplate(7, LogsComponent_app_error_logs_7_Template, 1, 0, \"app-error-logs\", 4);\n            i0.ɵɵtemplate(8, LogsComponent_app_api_logs_8_Template, 1, 0, \"app-api-logs\", 4);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"tabs\", ctx.tabs)(\"activeTabId\", ctx.activeTab);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngSwitch\", ctx.activeTab);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngSwitchCase\", \"activity\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngSwitchCase\", \"error\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngSwitchCase\", \"api\");\n          }\n        },\n        dependencies: [i2.NgSwitch, i2.NgSwitchCase, i3.TabNavigationComponent, i4.ActivityLogsComponent, i5.ErrorLogsComponent, i6.ApiLogsComponent]\n      });\n    }\n  }\n  return LogsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}