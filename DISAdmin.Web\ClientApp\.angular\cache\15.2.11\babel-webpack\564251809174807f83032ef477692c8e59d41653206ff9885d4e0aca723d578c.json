{"ast": null, "code": "export var LogSource = /*#__PURE__*/(() => {\n  (function (LogSource) {\n    LogSource[LogSource[\"Undefined\"] = 0] = \"Undefined\";\n    LogSource[LogSource[\"DISAdmin\"] = 1] = \"DISAdmin\";\n    LogSource[LogSource[\"DISApi\"] = 2] = \"DISApi\";\n  })(LogSource || (LogSource = {}));\n  return LogSource;\n})();\nexport var ApplicationLogLevel = /*#__PURE__*/(() => {\n  (function (ApplicationLogLevel) {\n    ApplicationLogLevel[ApplicationLogLevel[\"Trace\"] = 0] = \"Trace\";\n    ApplicationLogLevel[ApplicationLogLevel[\"Debug\"] = 1] = \"Debug\";\n    ApplicationLogLevel[ApplicationLogLevel[\"Information\"] = 2] = \"Information\";\n    ApplicationLogLevel[ApplicationLogLevel[\"Warning\"] = 3] = \"Warning\";\n    ApplicationLogLevel[ApplicationLogLevel[\"Error\"] = 4] = \"Error\";\n    ApplicationLogLevel[ApplicationLogLevel[\"Critical\"] = 5] = \"Critical\";\n  })(ApplicationLogLevel || (ApplicationLogLevel = {}));\n  return ApplicationLogLevel;\n})();\nexport var ActivityTypeEnum = /*#__PURE__*/(() => {\n  (function (ActivityTypeEnum) {\n    ActivityTypeEnum[\"Login\"] = \"Login\";\n    ActivityTypeEnum[\"Logout\"] = \"Logout\";\n    ActivityTypeEnum[\"Create\"] = \"Create\";\n    ActivityTypeEnum[\"Update\"] = \"Update\";\n    ActivityTypeEnum[\"Delete\"] = \"Delete\";\n    ActivityTypeEnum[\"Export\"] = \"Export\";\n    ActivityTypeEnum[\"Import\"] = \"Import\";\n    ActivityTypeEnum[\"PasswordChange\"] = \"PasswordChange\";\n    ActivityTypeEnum[\"ApiAccess\"] = \"ApiAccess\";\n    ActivityTypeEnum[\"Other\"] = \"Other\";\n  })(ActivityTypeEnum || (ActivityTypeEnum = {}));\n  return ActivityTypeEnum;\n})();\n// Pomocná třída pro překlad typů aktivit do češtiny\nexport class ActivityTypeHelper {\n  static getLocalizedName(type) {\n    switch (type) {\n      case 'Login':\n        return 'Přihlášení';\n      case 'Logout':\n        return 'Odhlášení';\n      case 'Create':\n        return 'Vytvoření';\n      case 'Update':\n        return 'Úprava';\n      case 'Delete':\n        return 'Smazání';\n      case 'Export':\n        return 'Export';\n      case 'Import':\n        return 'Import';\n      case 'PasswordChange':\n        return 'Změna hesla';\n      case 'ApiAccess':\n        return 'Přístup k API';\n      case 'Other':\n        return 'Ostatní';\n      default:\n        return type;\n    }\n  }\n  static getActivityTypes() {\n    return [{\n      value: 'Login',\n      label: 'Přihlášení'\n    }, {\n      value: 'Logout',\n      label: 'Odhlášení'\n    }, {\n      value: 'Create',\n      label: 'Vytvoření'\n    }, {\n      value: 'Update',\n      label: 'Úprava'\n    }, {\n      value: 'Delete',\n      label: 'Smazání'\n    }, {\n      value: 'Export',\n      label: 'Export'\n    }, {\n      value: 'Import',\n      label: 'Import'\n    }, {\n      value: 'PasswordChange',\n      label: 'Změna hesla'\n    }, {\n      value: 'ApiAccess',\n      label: 'Přístup k API'\n    }, {\n      value: 'Other',\n      label: 'Ostatní'\n    }];\n  }\n}\n// Pomocná třída pro překlad zdrojů logů do češtiny\nexport class LogSourceHelper {\n  static getLocalizedName(source) {\n    switch (source) {\n      case LogSource.DISAdmin:\n        return 'DISAdmin';\n      case LogSource.DISApi:\n        return 'DIS API';\n      case LogSource.Undefined:\n        return 'Neurčeno';\n      default:\n        return 'Neznámý';\n    }\n  }\n  static getLogSources() {\n    return [{\n      value: LogSource.DISAdmin,\n      label: 'DISAdmin'\n    }, {\n      value: LogSource.DISApi,\n      label: 'DIS API'\n    }, {\n      value: LogSource.Undefined,\n      label: 'Neurčeno'\n    }];\n  }\n}\n// Pomocná třída pro práci s úrovněmi logování\nexport class ApplicationLogLevelHelper {\n  static getLocalizedName(logLevel) {\n    switch (logLevel) {\n      case 'Trace':\n        return 'Trasování';\n      case 'Debug':\n        return 'Ladění';\n      case 'Information':\n        return 'Informace';\n      case 'Warning':\n        return 'Varování';\n      case 'Error':\n        return 'Chyba';\n      case 'Critical':\n        return 'Kritická';\n      default:\n        return logLevel;\n    }\n  }\n  static getLogLevels() {\n    return [{\n      value: ApplicationLogLevel.Trace,\n      label: 'Trasování'\n    }, {\n      value: ApplicationLogLevel.Debug,\n      label: 'Ladění'\n    }, {\n      value: ApplicationLogLevel.Information,\n      label: 'Informace'\n    }, {\n      value: ApplicationLogLevel.Warning,\n      label: 'Varování'\n    }, {\n      value: ApplicationLogLevel.Error,\n      label: 'Chyba'\n    }, {\n      value: ApplicationLogLevel.Critical,\n      label: 'Kritická'\n    }];\n  }\n  static getLogLevelClass(logLevel) {\n    switch (logLevel) {\n      case 'Trace':\n        return 'bg-secondary';\n      case 'Debug':\n        return 'bg-info';\n      case 'Information':\n        return 'bg-primary';\n      case 'Warning':\n        return 'bg-warning';\n      case 'Error':\n        return 'bg-danger';\n      case 'Critical':\n        return 'bg-dark';\n      default:\n        return 'bg-light';\n    }\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}