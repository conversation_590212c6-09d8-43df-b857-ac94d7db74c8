{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { InstanceStatus } from '../models/instance.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../services/customer.service\";\nimport * as i4 from \"../services/instance.service\";\nimport * as i5 from \"../services/certificate.service\";\nimport * as i6 from \"../services/modal.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../shared/certificate-modal/certificate-modal.component\";\nfunction InstanceWizardComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction InstanceWizardComponent_div_28_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const customer_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", customer_r11.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(customer_r11.name);\n  }\n}\nfunction InstanceWizardComponent_div_28_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1, \" Vyberte z\\u00E1kazn\\u00EDka \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\nfunction InstanceWizardComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h5\", 30);\n    i0.ɵɵtext(2, \"Vyberte z\\u00E1kazn\\u00EDka\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"form\", 31)(4, \"div\", 30)(5, \"label\", 32);\n    i0.ɵɵtext(6, \"Z\\u00E1kazn\\u00EDk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"select\", 33)(8, \"option\", 34);\n    i0.ɵɵtext(9, \"-- Vyberte z\\u00E1kazn\\u00EDka --\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, InstanceWizardComponent_div_28_option_10_Template, 2, 2, \"option\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, InstanceWizardComponent_div_28_div_11_Template, 2, 0, \"div\", 36);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_3_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.customerForm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ((tmp_1_0 = ctx_r1.customerForm.get(\"customerId\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r1.customerForm.get(\"customerId\")) == null ? null : tmp_1_0.touched)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.customers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r1.customerForm.get(\"customerId\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r1.customerForm.get(\"customerId\")) == null ? null : tmp_3_0.touched));\n  }\n}\nfunction InstanceWizardComponent_div_29_div_8_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Zadejte n\\u00E1zev instance\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InstanceWizardComponent_div_29_div_8_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"N\\u00E1zev instance nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 200 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InstanceWizardComponent_div_29_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, InstanceWizardComponent_div_29_div_8_span_1_Template, 2, 0, \"span\", 10);\n    i0.ɵɵtemplate(2, InstanceWizardComponent_div_29_div_8_span_2_Template, 2, 0, \"span\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r12.instanceForm.get(\"name\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r12.instanceForm.get(\"name\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction InstanceWizardComponent_div_29_div_13_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Zadejte URL serveru\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InstanceWizardComponent_div_29_div_13_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"URL serveru nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 255 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InstanceWizardComponent_div_29_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, InstanceWizardComponent_div_29_div_13_span_1_Template, 2, 0, \"span\", 10);\n    i0.ɵɵtemplate(2, InstanceWizardComponent_div_29_div_13_span_2_Template, 2, 0, \"span\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r13.instanceForm.get(\"serverUrl\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r13.instanceForm.get(\"serverUrl\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction InstanceWizardComponent_div_29_div_36_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Pozn\\u00E1mky nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 500 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InstanceWizardComponent_div_29_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, InstanceWizardComponent_div_29_div_36_span_1_Template, 2, 0, \"span\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r14.instanceForm.get(\"notes\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction InstanceWizardComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h5\", 30);\n    i0.ɵɵtext(2, \"Informace o instanci\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"form\", 31)(4, \"div\", 30)(5, \"label\", 39);\n    i0.ɵɵtext(6, \"N\\u00E1zev instance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 40);\n    i0.ɵɵtemplate(8, InstanceWizardComponent_div_29_div_8_Template, 3, 2, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 30)(10, \"label\", 41);\n    i0.ɵɵtext(11, \"URL serveru\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 42);\n    i0.ɵɵtemplate(13, InstanceWizardComponent_div_29_div_13_Template, 3, 2, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 30)(15, \"label\", 43);\n    i0.ɵɵtext(16, \"Datum expirace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 30)(19, \"label\", 45);\n    i0.ɵɵtext(20, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"select\", 46)(22, \"option\", 37);\n    i0.ɵɵtext(23, \"Aktivn\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"option\", 37);\n    i0.ɵɵtext(25, \"Trial\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"option\", 37);\n    i0.ɵɵtext(27, \"\\u00DAdr\\u017Eba\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"option\", 37);\n    i0.ɵɵtext(29, \"Blokov\\u00E1no\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"option\", 37);\n    i0.ɵɵtext(31, \"Expirov\\u00E1no\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 30)(33, \"label\", 47);\n    i0.ɵɵtext(34, \"Pozn\\u00E1mky\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(35, \"textarea\", 48);\n    i0.ɵɵtemplate(36, InstanceWizardComponent_div_29_div_36_Template, 2, 1, \"div\", 36);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_10_0;\n    let tmp_11_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.instanceForm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, ((tmp_1_0 = ctx_r2.instanceForm.get(\"name\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r2.instanceForm.get(\"name\")) == null ? null : tmp_1_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r2.instanceForm.get(\"name\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r2.instanceForm.get(\"name\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(14, _c0, ((tmp_3_0 = ctx_r2.instanceForm.get(\"serverUrl\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r2.instanceForm.get(\"serverUrl\")) == null ? null : tmp_3_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r2.instanceForm.get(\"serverUrl\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r2.instanceForm.get(\"serverUrl\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"value\", ctx_r2.InstanceStatus.Active);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r2.InstanceStatus.Trial);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r2.InstanceStatus.Maintenance);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r2.InstanceStatus.Blocked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r2.InstanceStatus.Expired);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(16, _c0, ((tmp_10_0 = ctx_r2.instanceForm.get(\"notes\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx_r2.instanceForm.get(\"notes\")) == null ? null : tmp_10_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx_r2.instanceForm.get(\"notes\")) == null ? null : tmp_11_0.invalid) && ((tmp_11_0 = ctx_r2.instanceForm.get(\"notes\")) == null ? null : tmp_11_0.touched));\n  }\n}\nfunction InstanceWizardComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h5\", 30);\n    i0.ɵɵtext(2, \"Povolen\\u00E9 moduly\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"form\", 31)(4, \"div\", 49);\n    i0.ɵɵelement(5, \"input\", 50);\n    i0.ɵɵelementStart(6, \"label\", 51);\n    i0.ɵɵtext(7, \"Reporting\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 49);\n    i0.ɵɵelement(9, \"input\", 52);\n    i0.ɵɵelementStart(10, \"label\", 53);\n    i0.ɵɵtext(11, \"Roz\\u0161\\u00ED\\u0159en\\u00E9 zabezpe\\u010Den\\u00ED\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 49);\n    i0.ɵɵelement(13, \"input\", 54);\n    i0.ɵɵelementStart(14, \"label\", 55);\n    i0.ɵɵtext(15, \"API integrace\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 49);\n    i0.ɵɵelement(17, \"input\", 56);\n    i0.ɵɵelementStart(18, \"label\", 57);\n    i0.ɵɵtext(19, \"Export dat\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 49);\n    i0.ɵɵelement(21, \"input\", 58);\n    i0.ɵɵelementStart(22, \"label\", 59);\n    i0.ɵɵtext(23, \"P\\u0159izp\\u016Fsoben\\u00ED\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.modulesForm);\n  }\n}\nfunction InstanceWizardComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h5\", 30);\n    i0.ɵɵtext(2, \"Certifik\\u00E1t\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"form\", 31)(4, \"div\", 49);\n    i0.ɵɵelement(5, \"input\", 60);\n    i0.ɵɵelementStart(6, \"label\", 61);\n    i0.ɵɵtext(7, \"Vygenerovat certifik\\u00E1t pro instanci\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 62);\n    i0.ɵɵelement(9, \"i\", 63);\n    i0.ɵɵtext(10, \" Certifik\\u00E1t bude vygenerov\\u00E1n automaticky po vytvo\\u0159en\\u00ED instance. Bude platn\\u00FD po dobu 1 roku. \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formGroup\", ctx_r4.certificateForm);\n  }\n}\nfunction InstanceWizardComponent_button_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function InstanceWizardComponent_button_33_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.previousStep());\n    });\n    i0.ɵɵelement(1, \"i\", 65);\n    i0.ɵɵtext(2, \"Zp\\u011Bt \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r5.loading);\n  }\n}\nfunction InstanceWizardComponent_button_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function InstanceWizardComponent_button_35_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.nextStep());\n    });\n    i0.ɵɵtext(1, \" Dal\\u0161\\u00ED\");\n    i0.ɵɵelement(2, \"i\", 67);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r6.loading);\n  }\n}\nfunction InstanceWizardComponent_button_36_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 70);\n  }\n}\nfunction InstanceWizardComponent_button_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function InstanceWizardComponent_button_36_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.createInstance());\n    });\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵtext(2, \"Vytvo\\u0159it instanci \");\n    i0.ɵɵtemplate(3, InstanceWizardComponent_button_36_span_3_Template, 1, 0, \"span\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r7.loading);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.loading);\n  }\n}\nfunction InstanceWizardComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 62);\n    i0.ɵɵelement(2, \"i\", 63);\n    i0.ɵɵtext(3, \" Pro instanci byl vygenerov\\u00E1n certifik\\u00E1t. M\\u016F\\u017Eete ho st\\u00E1hnout kliknut\\u00EDm na tla\\u010D\\u00EDtko n\\u00ED\\u017Ee. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function InstanceWizardComponent_div_48_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.showCertificateModal());\n    });\n    i0.ɵɵelement(5, \"i\", 73);\n    i0.ɵɵtext(6, \"Zobrazit certifik\\u00E1t \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class InstanceWizardComponent {\n  constructor(fb, router, customerService, instanceService, certificateService, modalService) {\n    this.fb = fb;\n    this.router = router;\n    this.customerService = customerService;\n    this.instanceService = instanceService;\n    this.certificateService = certificateService;\n    this.modalService = modalService;\n    this.currentStep = 1;\n    this.totalSteps = 4;\n    this.customers = [];\n    this.loading = false;\n    this.error = null;\n    // Export enum pro použití v template\n    this.InstanceStatus = InstanceStatus;\n    this.createdInstanceId = null;\n    this.generatedCertificate = null;\n    this.customerForm = this.fb.group({\n      customerId: ['', Validators.required]\n    });\n    this.instanceForm = this.fb.group({\n      name: ['', [Validators.required, Validators.maxLength(200)]],\n      serverUrl: ['', [Validators.required, Validators.maxLength(255)]],\n      expirationDate: [''],\n      status: [InstanceStatus.Active, Validators.required],\n      notes: ['', Validators.maxLength(500)]\n    });\n    this.modulesForm = this.fb.group({\n      moduleReporting: [true],\n      moduleAdvancedSecurity: [false],\n      moduleApiIntegration: [false],\n      moduleDataExport: [false],\n      moduleCustomization: [false]\n    });\n    this.certificateForm = this.fb.group({\n      generateCertificate: [true]\n    });\n  }\n  ngOnInit() {\n    this.loadCustomers();\n  }\n  loadCustomers() {\n    this.loading = true;\n    this.customerService.getCustomers().subscribe({\n      next: customers => {\n        this.customers = customers;\n        this.loading = false;\n      },\n      error: err => {\n        console.error('Chyba při načítání zákazníků', err);\n        this.error = 'Chyba při načítání zákazníků';\n        this.loading = false;\n      }\n    });\n  }\n  nextStep() {\n    if (this.currentStep < this.totalSteps) {\n      // Validace aktuálního kroku\n      if (this.currentStep === 1 && this.customerForm.invalid) {\n        this.customerForm.markAllAsTouched();\n        return;\n      }\n      if (this.currentStep === 2 && this.instanceForm.invalid) {\n        this.instanceForm.markAllAsTouched();\n        return;\n      }\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  createInstance() {\n    if (this.customerForm.invalid || this.instanceForm.invalid || this.modulesForm.invalid) {\n      return;\n    }\n    this.loading = true;\n    this.error = null;\n    const customerId = this.customerForm.value.customerId;\n    const instanceFormValue = this.instanceForm.value;\n    // Převod prázdného stringu na null pro expirationDate a správný převod status\n    const instanceData = {\n      ...instanceFormValue,\n      ...this.modulesForm.value,\n      customerId,\n      expirationDate: instanceFormValue.expirationDate && instanceFormValue.expirationDate.trim() !== '' ? new Date(instanceFormValue.expirationDate) : null,\n      status: Number(instanceFormValue.status) // Zajistíme, že status je číslo\n    };\n\n    this.instanceService.createInstance(instanceData).subscribe({\n      next: response => {\n        this.createdInstanceId = response.id;\n        if (this.certificateForm.value.generateCertificate && this.createdInstanceId) {\n          this.generateCertificate(this.createdInstanceId);\n        } else {\n          this.loading = false;\n          this.showSuccessModal();\n        }\n      },\n      error: err => {\n        console.error('Chyba při vytváření instance - detailní informace:', {\n          error: err,\n          status: err.status,\n          statusText: err.statusText,\n          message: err.error?.message || err.message,\n          url: err.url,\n          instanceData: instanceData,\n          timestamp: new Date().toISOString()\n        });\n        // Zobrazení detailnější chybové zprávy uživateli\n        let errorMessage = 'Chyba při vytváření instance';\n        if (err.error?.message) {\n          errorMessage = err.error.message;\n          // Pokud jsou k dispozici validační chyby, zobrazíme je\n          if (err.error.errors) {\n            const validationErrors = Object.keys(err.error.errors).map(key => err.error.errors[key].join(', ')).join('; ');\n            errorMessage += ': ' + validationErrors;\n          }\n        } else if (err.status === 0) {\n          errorMessage = 'Chyba připojení k serveru';\n        } else if (err.status >= 500) {\n          errorMessage = 'Chyba serveru při vytváření instance';\n        } else if (err.status === 400) {\n          errorMessage = 'Neplatná data pro vytvoření instance';\n        } else if (err.status === 404) {\n          errorMessage = 'Zákazník nebyl nalezen';\n        }\n        this.error = errorMessage;\n        this.loading = false;\n      }\n    });\n  }\n  generateCertificate(instanceId) {\n    this.certificateService.generateCertificate(instanceId).subscribe({\n      next: response => {\n        // Uložení vygenerovaného certifikátu - vytvoříme kopii, aby se zajistilo, že se data nepřepíší\n        // Zkontrolujeme, zda heslo existuje v odpovědi, jinak použijeme výchozí heslo\n        const certificatePassword = response.password || 'password';\n        this.generatedCertificate = {\n          certificate: response.certificate,\n          privateKey: response.privateKey,\n          thumbprint: response.thumbprint,\n          expirationDate: response.expirationDate,\n          password: certificatePassword,\n          certificatePassword: certificatePassword\n        };\n        // Explicitní nastavení hesla pro zobrazení v modálním okně\n        setTimeout(() => {\n          const passwordElement = document.getElementById('wizardCertificatePassword');\n          if (passwordElement && this.generatedCertificate) {\n            passwordElement.textContent = this.generatedCertificate.password || 'Heslo není k dispozici';\n          }\n        }, 100);\n        this.loading = false;\n        this.showSuccessModal();\n      },\n      error: err => {\n        console.error('Chyba při generování certifikátu', err);\n        this.error = 'Instance byla vytvořena, ale došlo k chybě při generování certifikátu';\n        this.loading = false;\n        this.showSuccessModal();\n      }\n    });\n  }\n  showSuccessModal() {\n    this.modalService.open('successModal');\n  }\n  closeSuccessModal() {\n    this.modalService.close('successModal');\n  }\n  closeCertificateModal() {\n    this.modalService.close('certificateInfoModal');\n  }\n  showCertificateModal() {\n    this.modalService.open('certificateInfoModal');\n  }\n  downloadCertificate() {\n    if (!this.generatedCertificate) {\n      return;\n    }\n    // Vytvoření a stažení souboru .pfx\n    const blob = this.base64ToBlob(this.generatedCertificate.privateKey, 'application/x-pkcs12');\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `certificate-${this.createdInstanceId}.pfx`;\n    document.body.appendChild(a);\n    a.click();\n    window.URL.revokeObjectURL(url);\n    document.body.removeChild(a);\n  }\n  /**\r\n   * Pomocná metoda pro konverzi Base64 na Blob\r\n   */\n  base64ToBlob(base64, contentType) {\n    const byteCharacters = atob(base64);\n    const byteArrays = [];\n    for (let offset = 0; offset < byteCharacters.length; offset += 512) {\n      const slice = byteCharacters.slice(offset, offset + 512);\n      const byteNumbers = new Array(slice.length);\n      for (let i = 0; i < slice.length; i++) {\n        byteNumbers[i] = slice.charCodeAt(i);\n      }\n      const byteArray = new Uint8Array(byteNumbers);\n      byteArrays.push(byteArray);\n    }\n    return new Blob(byteArrays, {\n      type: contentType\n    });\n  }\n  goToCustomers() {\n    this.closeSuccessModal();\n    this.router.navigate(['/customers']);\n  }\n  getStepClass(step) {\n    if (step === this.currentStep) {\n      return 'active';\n    } else if (step < this.currentStep) {\n      return 'completed';\n    } else {\n      return '';\n    }\n  }\n  static {\n    this.ɵfac = function InstanceWizardComponent_Factory(t) {\n      return new (t || InstanceWizardComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.CustomerService), i0.ɵɵdirectiveInject(i4.InstanceService), i0.ɵɵdirectiveInject(i5.CertificateService), i0.ɵɵdirectiveInject(i6.ModalService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: InstanceWizardComponent,\n      selectors: [[\"app-instance-wizard\"]],\n      decls: 55,\n      vars: 15,\n      consts: [[1, \"container\", \"mt-4\"], [1, \"card\"], [1, \"card-header\", \"bg-primary\", \"text-white\"], [1, \"mb-0\"], [1, \"card-body\"], [\"class\", \"alert alert-danger mb-4\", 4, \"ngIf\"], [1, \"wizard-steps\", \"mb-4\"], [1, \"step\", 3, \"ngClass\"], [1, \"step-number\"], [1, \"step-title\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\", \"mt-4\"], [\"class\", \"btn btn-secondary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary me-2\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-success\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"id\", \"successModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"successModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\", \"bg-success\", \"text-white\"], [\"id\", \"successModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\", \"btn-close-white\", 3, \"click\"], [1, \"modal-body\"], [1, \"alert\", \"alert-success\"], [1, \"bi\", \"bi-check-circle-fill\", \"me-2\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [\"modalId\", \"certificateInfoModal\", 3, \"certificate\", \"instanceName\", \"close\", \"download\"], [1, \"alert\", \"alert-danger\", \"mb-4\"], [1, \"mb-3\"], [3, \"formGroup\"], [\"for\", \"customerId\", 1, \"form-label\", \"required\"], [\"id\", \"customerId\", \"formControlName\", \"customerId\", 1, \"form-select\", 3, \"ngClass\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [3, \"value\"], [1, \"invalid-feedback\"], [\"for\", \"name\", 1, \"form-label\", \"required\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"serverUrl\", 1, \"form-label\", \"required\"], [\"type\", \"text\", \"id\", \"serverUrl\", \"formControlName\", \"serverUrl\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"expirationDate\", 1, \"form-label\"], [\"type\", \"date\", \"id\", \"expirationDate\", \"formControlName\", \"expirationDate\", 1, \"form-control\"], [\"for\", \"status\", 1, \"form-label\"], [\"id\", \"status\", \"formControlName\", \"status\", 1, \"form-select\"], [\"for\", \"notes\", 1, \"form-label\"], [\"id\", \"notes\", \"formControlName\", \"notes\", \"rows\", \"3\", 1, \"form-control\", 3, \"ngClass\"], [1, \"mb-3\", \"form-check\"], [\"type\", \"checkbox\", \"id\", \"moduleReporting\", \"formControlName\", \"moduleReporting\", 1, \"form-check-input\"], [\"for\", \"moduleReporting\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleAdvancedSecurity\", \"formControlName\", \"moduleAdvancedSecurity\", 1, \"form-check-input\"], [\"for\", \"moduleAdvancedSecurity\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleApiIntegration\", \"formControlName\", \"moduleApiIntegration\", 1, \"form-check-input\"], [\"for\", \"moduleApiIntegration\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleDataExport\", \"formControlName\", \"moduleDataExport\", 1, \"form-check-input\"], [\"for\", \"moduleDataExport\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleCustomization\", \"formControlName\", \"moduleCustomization\", 1, \"form-check-input\"], [\"for\", \"moduleCustomization\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"generateCertificate\", \"formControlName\", \"generateCertificate\", 1, \"form-check-input\"], [\"for\", \"generateCertificate\", 1, \"form-check-label\"], [1, \"alert\", \"alert-info\"], [1, \"bi\", \"bi-info-circle-fill\", \"me-2\"], [1, \"btn\", \"btn-secondary\", 3, \"disabled\", \"click\"], [1, \"bi\", \"bi-arrow-left\", \"me-2\"], [1, \"btn\", \"btn-primary\", \"me-2\", 3, \"disabled\", \"click\"], [1, \"bi\", \"bi-arrow-right\", \"ms-2\"], [1, \"btn\", \"btn-success\", 3, \"disabled\", \"click\"], [\"class\", \"spinner-border spinner-border-sm ms-2\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"ms-2\"], [1, \"mt-3\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-info-circle\", \"me-2\"]],\n      template: function InstanceWizardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h4\", 3);\n          i0.ɵɵtext(4, \"Pr\\u016Fvodce vytvo\\u0159en\\u00EDm instance DIS\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵtemplate(6, InstanceWizardComponent_div_6_Template, 2, 1, \"div\", 5);\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8);\n          i0.ɵɵtext(10, \"1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 9);\n          i0.ɵɵtext(12, \"Z\\u00E1kazn\\u00EDk\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 7)(14, \"div\", 8);\n          i0.ɵɵtext(15, \"2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 9);\n          i0.ɵɵtext(17, \"Instance\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 7)(19, \"div\", 8);\n          i0.ɵɵtext(20, \"3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 9);\n          i0.ɵɵtext(22, \"Moduly\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 7)(24, \"div\", 8);\n          i0.ɵɵtext(25, \"4\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 9);\n          i0.ɵɵtext(27, \"Certifik\\u00E1t\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(28, InstanceWizardComponent_div_28_Template, 12, 6, \"div\", 10);\n          i0.ɵɵtemplate(29, InstanceWizardComponent_div_29_Template, 37, 18, \"div\", 10);\n          i0.ɵɵtemplate(30, InstanceWizardComponent_div_30_Template, 24, 1, \"div\", 10);\n          i0.ɵɵtemplate(31, InstanceWizardComponent_div_31_Template, 11, 1, \"div\", 10);\n          i0.ɵɵelementStart(32, \"div\", 11);\n          i0.ɵɵtemplate(33, InstanceWizardComponent_button_33_Template, 3, 1, \"button\", 12);\n          i0.ɵɵelementStart(34, \"div\");\n          i0.ɵɵtemplate(35, InstanceWizardComponent_button_35_Template, 3, 1, \"button\", 13);\n          i0.ɵɵtemplate(36, InstanceWizardComponent_button_36_Template, 4, 2, \"button\", 14);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(37, \"div\", 15)(38, \"div\", 16)(39, \"div\", 17)(40, \"div\", 18)(41, \"h5\", 19);\n          i0.ɵɵtext(42, \"Instance vytvo\\u0159ena\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function InstanceWizardComponent_Template_button_click_43_listener() {\n            return ctx.closeSuccessModal();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"div\", 21)(45, \"div\", 22);\n          i0.ɵɵelement(46, \"i\", 23);\n          i0.ɵɵtext(47, \" Instance byla \\u00FAsp\\u011B\\u0161n\\u011B vytvo\\u0159ena. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(48, InstanceWizardComponent_div_48_Template, 7, 0, \"div\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 25)(50, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function InstanceWizardComponent_Template_button_click_50_listener() {\n            return ctx.closeSuccessModal();\n          });\n          i0.ɵɵtext(51, \"Zav\\u0159\\u00EDt\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function InstanceWizardComponent_Template_button_click_52_listener() {\n            return ctx.goToCustomers();\n          });\n          i0.ɵɵtext(53, \" P\\u0159ej\\u00EDt na seznam z\\u00E1kazn\\u00EDk\\u016F \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(54, \"app-certificate-modal\", 28);\n          i0.ɵɵlistener(\"close\", function InstanceWizardComponent_Template_app_certificate_modal_close_54_listener() {\n            return ctx.closeCertificateModal();\n          })(\"download\", function InstanceWizardComponent_Template_app_certificate_modal_download_54_listener() {\n            return ctx.downloadCertificate();\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", ctx.getStepClass(1));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngClass\", ctx.getStepClass(2));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngClass\", ctx.getStepClass(3));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngClass\", ctx.getStepClass(4));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 4);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < ctx.totalSteps);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === ctx.totalSteps);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.generatedCertificate);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"certificate\", ctx.generatedCertificate)(\"instanceName\", ctx.instanceForm.value.name || \"\");\n        }\n      },\n      dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i8.CertificateModalComponent],\n      styles: [\".wizard-steps[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 2rem;\\n  position: relative;\\n}\\n\\n.wizard-steps[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 20px;\\n  left: 0;\\n  right: 0;\\n  height: 2px;\\n  background: #e9ecef;\\n  z-index: 0;\\n}\\n\\n.step[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  flex: 1;\\n}\\n\\n.step-number[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background-color: #e9ecef;\\n  color: #6c757d;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: bold;\\n  margin-bottom: 0.5rem;\\n  border: 2px solid #e9ecef;\\n}\\n\\n.step-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #6c757d;\\n}\\n\\n.step.active[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background-color: #0d6efd;\\n  color: white;\\n  border-color: #0d6efd;\\n}\\n\\n.step.active[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%] {\\n  color: #0d6efd;\\n  font-weight: bold;\\n}\\n\\n.step.completed[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background-color: #198754;\\n  color: white;\\n  border-color: #198754;\\n}\\n\\n.step.completed[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%] {\\n  color: #198754;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  border-radius: 0.5rem;\\n  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);\\n  overflow: hidden;\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  border-bottom: none;\\n}\\n\\n.form-check[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.form-check-input[_ngcontent-%COMP%]:checked {\\n  background-color: #0d6efd;\\n  border-color: #0d6efd;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background-color: #0d6efd;\\n  border-color: #0d6efd;\\n}\\n\\n.btn-success[_ngcontent-%COMP%] {\\n  background-color: #198754;\\n  border-color: #198754;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n}\\n\\n.alert-info[_ngcontent-%COMP%] {\\n  background-color: #cff4fc;\\n  border-color: #b6effb;\\n  color: #055160;\\n}\\n\\n.alert-success[_ngcontent-%COMP%] {\\n  background-color: #d1e7dd;\\n  border-color: #badbcc;\\n  color: #0f5132;\\n}\\n\\n.list-group-item[_ngcontent-%COMP%] {\\n  border-color: #dee2e6;\\n}\\n\\n\\n@media (prefers-color-scheme: dark) {\\n  .wizard-steps[_ngcontent-%COMP%]::before {\\n    background: #495057;\\n  }\\n  \\n  .step-number[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    color: #e9ecef;\\n    border-color: #495057;\\n  }\\n  \\n  .step-title[_ngcontent-%COMP%] {\\n    color: #e9ecef;\\n  }\\n  \\n  .step.active[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n    background-color: #0d6efd;\\n    border-color: #0d6efd;\\n  }\\n  \\n  .step.active[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%] {\\n    color: #0d6efd;\\n  }\\n  \\n  .step.completed[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n    background-color: #198754;\\n    border-color: #198754;\\n  }\\n  \\n  .step.completed[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%] {\\n    color: #198754;\\n  }\\n  \\n  .card[_ngcontent-%COMP%] {\\n    background-color: #2b3035;\\n    border-color: #373b3e;\\n  }\\n  \\n  .form-control[_ngcontent-%COMP%], .form-select[_ngcontent-%COMP%] {\\n    background-color: #212529;\\n    border-color: #495057;\\n    color: #e9ecef;\\n  }\\n  \\n  .form-control[_ngcontent-%COMP%]:focus, .form-select[_ngcontent-%COMP%]:focus {\\n    background-color: #2b3035;\\n    color: #e9ecef;\\n  }\\n  \\n  .alert-info[_ngcontent-%COMP%] {\\n    background-color: #0d3b66;\\n    border-color: #0d3b66;\\n    color: #e9ecef;\\n  }\\n  \\n  .alert-success[_ngcontent-%COMP%] {\\n    background-color: #0f5132;\\n    border-color: #0f5132;\\n    color: #e9ecef;\\n  }\\n  \\n  .list-group-item[_ngcontent-%COMP%] {\\n    background-color: #2b3035;\\n    border-color: #373b3e;\\n    color: #e9ecef;\\n  }\\n  \\n  .text-muted[_ngcontent-%COMP%] {\\n    color: #adb5bd !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAOnE,SAASC,cAAc,QAAQ,0BAA0B;;;;;;;;;;;;ICFnDC,+BAAmD;IACjDA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,6CACF;;;;;IA8BQA,kCAAiE;IAAAA,YAAmB;IAAAA,iBAAS;;;;IAAlDA,uCAAqB;IAACA,eAAmB;IAAnBA,uCAAmB;;;;;IAEtFA,+BAAyH;IACvHA,6CACF;IAAAA,iBAAM;;;;;;;;;;IAXZA,2BAA+B;IACZA,2CAAiB;IAAAA,iBAAK;IACvCA,gCAAiC;IAEuBA,kCAAQ;IAAAA,iBAAQ;IACpEA,kCAAwL;IACrKA,iDAAuB;IAAAA,iBAAS;IACjDA,wFAA6F;IAC/FA,iBAAS;IACTA,kFAEM;IACRA,iBAAM;;;;;;IAVFA,eAA0B;IAA1BA,+CAA0B;IAG6CA,eAA8G;IAA9GA,kOAA8G;IAExJA,eAAY;IAAZA,0CAAY;IAErCA,eAAwF;IAAxFA,mMAAwF;;;;;IAe5FA,4BAA6D;IAAAA,2CAAsB;IAAAA,iBAAO;;;;;IAC1FA,4BAA8D;IAAAA,+FAA4C;IAAAA,iBAAO;;;;;IAFnHA,+BAA6G;IAC3GA,wFAA0F;IAC1FA,wFAAiH;IACnHA,iBAAM;;;;;;IAFGA,eAAoD;IAApDA,+IAAoD;IACpDA,eAAqD;IAArDA,gJAAqD;;;;;IAO5DA,4BAAkE;IAAAA,mCAAmB;IAAAA,iBAAO;;;;;IAC5FA,4BAAmE;IAAAA,uFAAyC;IAAAA,iBAAO;;;;;IAFrHA,+BAAuH;IACrHA,yFAA4F;IAC5FA,yFAAmH;IACrHA,iBAAM;;;;;;IAFGA,eAAyD;IAAzDA,oJAAyD;IACzDA,eAA0D;IAA1DA,qJAA0D;;;;;IAsBjEA,4BAA+D;IAAAA,yFAAsC;IAAAA,iBAAO;;;;;IAD9GA,+BAA+G;IAC7GA,yFAA4G;IAC9GA,iBAAM;;;;;IADGA,eAAsD;IAAtDA,iJAAsD;;;;;IAtCrEA,2BAA+B;IACZA,oCAAoB;IAAAA,iBAAK;IAC1CA,gCAAiC;IAEiBA,mCAAc;IAAAA,iBAAQ;IACpEA,4BAA4K;IAC5KA,gFAGM;IACRA,iBAAM;IACNA,+BAAkB;IACmCA,4BAAW;IAAAA,iBAAQ;IACtEA,6BAAgM;IAChMA,kFAGM;IACRA,iBAAM;IAENA,gCAAkB;IAC+BA,+BAAc;IAAAA,iBAAQ;IACrEA,6BAA6F;IAC/FA,iBAAM;IACNA,gCAAkB;IACuBA,uBAAM;IAAAA,iBAAQ;IACrDA,mCAAiE;IACvBA,6BAAO;IAAAA,iBAAS;IACxDA,mCAAuC;IAAAA,sBAAK;IAAAA,iBAAS;IACrDA,mCAA6C;IAAAA,iCAAM;IAAAA,iBAAS;IAC5DA,mCAAyC;IAAAA,+BAAS;IAAAA,iBAAS;IAC3DA,mCAAyC;IAAAA,gCAAU;IAAAA,iBAAS;IAGhEA,gCAAkB;IACsBA,8BAAQ;IAAAA,iBAAQ;IACtDA,gCAA2L;IAC3LA,kFAEM;IACRA,iBAAM;;;;;;;;;;IAtCFA,eAA0B;IAA1BA,+CAA0B;IAG6CA,eAAkG;IAAlGA,uNAAkG;IACrKA,eAA4E;IAA5EA,uLAA4E;IAOCA,eAA4G;IAA5GA,iOAA4G;IACzLA,eAAsF;IAAtFA,iMAAsF;IAalFA,eAA+B;IAA/BA,oDAA+B;IAC/BA,eAA8B;IAA9BA,mDAA8B;IAC9BA,eAAoC;IAApCA,yDAAoC;IACpCA,eAAgC;IAAhCA,qDAAgC;IAChCA,eAAgC;IAAhCA,qDAAgC;IAKiCA,eAAoG;IAApGA,6NAAoG;IACzKA,eAA8E;IAA9EA,6LAA8E;;;;;IAQ1FA,2BAA+B;IACZA,oCAAe;IAAAA,iBAAK;IACrCA,gCAAgC;IAE5BA,4BAAuG;IACvGA,iCAAsD;IAAAA,yBAAS;IAAAA,iBAAQ;IAEzEA,+BAA6B;IAC3BA,4BAAqH;IACrHA,kCAA6D;IAAAA,oEAAqB;IAAAA,iBAAQ;IAE5FA,gCAA6B;IAC3BA,6BAAiH;IACjHA,kCAA2D;IAAAA,8BAAa;IAAAA,iBAAQ;IAElFA,gCAA6B;IAC3BA,6BAAyG;IACzGA,kCAAuD;IAAAA,2BAAU;IAAAA,iBAAQ;IAE3EA,gCAA6B;IAC3BA,6BAA+G;IAC/GA,kCAA0D;IAAAA,4CAAY;IAAAA,iBAAQ;;;;IAnB5EA,eAAyB;IAAzBA,8CAAyB;;;;;IAyBjCA,2BAA+B;IACZA,+BAAU;IAAAA,iBAAK;IAChCA,gCAAoC;IAEhCA,4BAA+G;IAC/GA,iCAA0D;IAAAA,wDAAmC;IAAAA,iBAAQ;IAEvGA,+BAA8B;IAC5BA,wBAA2C;IAC3CA,sIACF;IAAAA,iBAAM;;;;IARFA,eAA6B;IAA7BA,kDAA6B;;;;;;IAcnCA,kCAAwG;IAAtEA;MAAAA;MAAA;MAAA,OAASA,qCAAc;IAAA,EAAC;IACxDA,wBAAqC;IAAAA,0BACvC;IAAAA,iBAAS;;;;IAF0EA,yCAAoB;;;;;;IAIrGA,kCAAgH;IAA3EA;MAAAA;MAAA;MAAA,OAASA,iCAAU;IAAA,EAAC;IACvDA,gCAAK;IAAAA,wBAAsC;IAC7CA,iBAAS;;;;IAFkFA,yCAAoB;;;;;IAK7GA,2BAA4G;;;;;;IAF9GA,kCAAmH;IAAnFA;MAAAA;MAAA;MAAA,OAASA,uCAAgB;IAAA,EAAC;IACxDA,wBAA4C;IAAAA,uCAC5C;IAAAA,qFAA4G;IAC9GA,iBAAS;;;;IAHqFA,yCAAoB;IAEzGA,eAAa;IAAbA,qCAAa;;;;;;IAuBxBA,+BAA+C;IAE3CA,wBAA2C;IAC3CA,2JACF;IAAAA,iBAAM;IACNA,kCAAiE;IAAjCA;MAAAA;MAAA;MAAA,OAASA,6CAAsB;IAAA,EAAC;IAC9DA,wBAAsC;IAAAA,yCACxC;IAAAA,iBAAS;;;ADhKnB,OAAM,MAAOC,uBAAuB;EAmBlCC,YACUC,EAAe,EACfC,MAAc,EACdC,eAAgC,EAChCC,eAAgC,EAChCC,kBAAsC,EACtCC,YAA0B;IAL1B,OAAE,GAAFL,EAAE;IACF,WAAM,GAANC,MAAM;IACN,oBAAe,GAAfC,eAAe;IACf,oBAAe,GAAfC,eAAe;IACf,uBAAkB,GAAlBC,kBAAkB;IAClB,iBAAY,GAAZC,YAAY;IAxBtB,gBAAW,GAAW,CAAC;IACvB,eAAU,GAAW,CAAC;IAEtB,cAAS,GAAe,EAAE;IAC1B,YAAO,GAAY,KAAK;IACxB,UAAK,GAAkB,IAAI;IAE3B;IACA,mBAAc,GAAGT,cAAc;IAO/B,sBAAiB,GAAkB,IAAI;IACvC,yBAAoB,GAAQ,IAAI;IAU9B,IAAI,CAACU,YAAY,GAAG,IAAI,CAACN,EAAE,CAACO,KAAK,CAAC;MAChCC,UAAU,EAAE,CAAC,EAAE,EAAEb,UAAU,CAACc,QAAQ;KACrC,CAAC;IAEF,IAAI,CAACC,YAAY,GAAG,IAAI,CAACV,EAAE,CAACO,KAAK,CAAC;MAChCI,IAAI,EAAE,CAAC,EAAE,EAAE,CAAChB,UAAU,CAACc,QAAQ,EAAEd,UAAU,CAACiB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC5DC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAClB,UAAU,CAACc,QAAQ,EAAEd,UAAU,CAACiB,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MACjEE,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,MAAM,EAAE,CAACnB,cAAc,CAACoB,MAAM,EAAErB,UAAU,CAACc,QAAQ,CAAC;MACpDQ,KAAK,EAAE,CAAC,EAAE,EAAEtB,UAAU,CAACiB,SAAS,CAAC,GAAG,CAAC;KACtC,CAAC;IAEF,IAAI,CAACM,WAAW,GAAG,IAAI,CAAClB,EAAE,CAACO,KAAK,CAAC;MAC/BY,eAAe,EAAE,CAAC,IAAI,CAAC;MACvBC,sBAAsB,EAAE,CAAC,KAAK,CAAC;MAC/BC,oBAAoB,EAAE,CAAC,KAAK,CAAC;MAC7BC,gBAAgB,EAAE,CAAC,KAAK,CAAC;MACzBC,mBAAmB,EAAE,CAAC,KAAK;KAC5B,CAAC;IAEF,IAAI,CAACC,eAAe,GAAG,IAAI,CAACxB,EAAE,CAACO,KAAK,CAAC;MACnCkB,mBAAmB,EAAE,CAAC,IAAI;KAC3B,CAAC;EACJ;EAEAC,QAAQ;IACN,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAA,aAAa;IACX,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC1B,eAAe,CAAC2B,YAAY,EAAE,CAACC,SAAS,CAAC;MAC5CC,IAAI,EAAGC,SAAS,IAAI;QAClB,IAAI,CAACA,SAAS,GAAGA,SAAS;QAC1B,IAAI,CAACJ,OAAO,GAAG,KAAK;MACtB,CAAC;MACDK,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,8BAA8B,EAAEC,GAAG,CAAC;QAClD,IAAI,CAACD,KAAK,GAAG,8BAA8B;QAC3C,IAAI,CAACL,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAQ,QAAQ;IACN,IAAI,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,UAAU,EAAE;MACtC;MACA,IAAI,IAAI,CAACD,WAAW,KAAK,CAAC,IAAI,IAAI,CAAC/B,YAAY,CAACiC,OAAO,EAAE;QACvD,IAAI,CAACjC,YAAY,CAACkC,gBAAgB,EAAE;QACpC;;MAGF,IAAI,IAAI,CAACH,WAAW,KAAK,CAAC,IAAI,IAAI,CAAC3B,YAAY,CAAC6B,OAAO,EAAE;QACvD,IAAI,CAAC7B,YAAY,CAAC8B,gBAAgB,EAAE;QACpC;;MAGF,IAAI,CAACH,WAAW,EAAE;;EAEtB;EAEAI,YAAY;IACV,IAAI,IAAI,CAACJ,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;;EAEtB;EAEAK,cAAc;IACZ,IAAI,IAAI,CAACpC,YAAY,CAACiC,OAAO,IAAI,IAAI,CAAC7B,YAAY,CAAC6B,OAAO,IAAI,IAAI,CAACrB,WAAW,CAACqB,OAAO,EAAE;MACtF;;IAGF,IAAI,CAACX,OAAO,GAAG,IAAI;IACnB,IAAI,CAACK,KAAK,GAAG,IAAI;IAEjB,MAAMzB,UAAU,GAAG,IAAI,CAACF,YAAY,CAACqC,KAAK,CAACnC,UAAU;IACrD,MAAMoC,iBAAiB,GAAG,IAAI,CAAClC,YAAY,CAACiC,KAAK;IAEjD;IACA,MAAME,YAAY,GAAG;MACnB,GAAGD,iBAAiB;MACpB,GAAG,IAAI,CAAC1B,WAAW,CAACyB,KAAK;MACzBnC,UAAU;MACVM,cAAc,EAAE8B,iBAAiB,CAAC9B,cAAc,IAAI8B,iBAAiB,CAAC9B,cAAc,CAACgC,IAAI,EAAE,KAAK,EAAE,GAC9F,IAAIC,IAAI,CAACH,iBAAiB,CAAC9B,cAAc,CAAC,GAC1C,IAAI;MACRC,MAAM,EAAEiC,MAAM,CAACJ,iBAAiB,CAAC7B,MAAM,CAAC,CAAC;KAC1C;;IAED,IAAI,CAACZ,eAAe,CAACuC,cAAc,CAACG,YAAY,CAAC,CAACf,SAAS,CAAC;MAC1DC,IAAI,EAAGkB,QAAQ,IAAI;QACjB,IAAI,CAACC,iBAAiB,GAAGD,QAAQ,CAACE,EAAE;QAEpC,IAAI,IAAI,CAAC3B,eAAe,CAACmB,KAAK,CAAClB,mBAAmB,IAAI,IAAI,CAACyB,iBAAiB,EAAE;UAC5E,IAAI,CAACzB,mBAAmB,CAAC,IAAI,CAACyB,iBAAiB,CAAC;SACjD,MAAM;UACL,IAAI,CAACtB,OAAO,GAAG,KAAK;UACpB,IAAI,CAACwB,gBAAgB,EAAE;;MAE3B,CAAC;MACDnB,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,oDAAoD,EAAE;UAClEA,KAAK,EAAEC,GAAG;UACVnB,MAAM,EAAEmB,GAAG,CAACnB,MAAM;UAClBsC,UAAU,EAAEnB,GAAG,CAACmB,UAAU;UAC1BC,OAAO,EAAEpB,GAAG,CAACD,KAAK,EAAEqB,OAAO,IAAIpB,GAAG,CAACoB,OAAO;UAC1CC,GAAG,EAAErB,GAAG,CAACqB,GAAG;UACZV,YAAY,EAAEA,YAAY;UAC1BW,SAAS,EAAE,IAAIT,IAAI,EAAE,CAACU,WAAW;SAClC,CAAC;QAEF;QACA,IAAIC,YAAY,GAAG,8BAA8B;QAEjD,IAAIxB,GAAG,CAACD,KAAK,EAAEqB,OAAO,EAAE;UACtBI,YAAY,GAAGxB,GAAG,CAACD,KAAK,CAACqB,OAAO;UAEhC;UACA,IAAIpB,GAAG,CAACD,KAAK,CAAC0B,MAAM,EAAE;YACpB,MAAMC,gBAAgB,GAAGC,MAAM,CAACC,IAAI,CAAC5B,GAAG,CAACD,KAAK,CAAC0B,MAAM,CAAC,CACnDI,GAAG,CAACC,GAAG,IAAI9B,GAAG,CAACD,KAAK,CAAC0B,MAAM,CAACK,GAAG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC5CA,IAAI,CAAC,IAAI,CAAC;YACbP,YAAY,IAAI,IAAI,GAAGE,gBAAgB;;SAE1C,MAAM,IAAI1B,GAAG,CAACnB,MAAM,KAAK,CAAC,EAAE;UAC3B2C,YAAY,GAAG,2BAA2B;SAC3C,MAAM,IAAIxB,GAAG,CAACnB,MAAM,IAAI,GAAG,EAAE;UAC5B2C,YAAY,GAAG,sCAAsC;SACtD,MAAM,IAAIxB,GAAG,CAACnB,MAAM,KAAK,GAAG,EAAE;UAC7B2C,YAAY,GAAG,sCAAsC;SACtD,MAAM,IAAIxB,GAAG,CAACnB,MAAM,KAAK,GAAG,EAAE;UAC7B2C,YAAY,GAAG,wBAAwB;;QAGzC,IAAI,CAACzB,KAAK,GAAGyB,YAAY;QACzB,IAAI,CAAC9B,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAH,mBAAmB,CAACyC,UAAkB;IACpC,IAAI,CAAC9D,kBAAkB,CAACqB,mBAAmB,CAACyC,UAAU,CAAC,CAACpC,SAAS,CAAC;MAChEC,IAAI,EAAGkB,QAAQ,IAAI;QACjB;QACA;QACA,MAAMkB,mBAAmB,GAAGlB,QAAQ,CAACmB,QAAQ,IAAI,UAAU;QAE3D,IAAI,CAACC,oBAAoB,GAAG;UAC1BC,WAAW,EAAErB,QAAQ,CAACqB,WAAW;UACjCC,UAAU,EAAEtB,QAAQ,CAACsB,UAAU;UAC/BC,UAAU,EAAEvB,QAAQ,CAACuB,UAAU;UAC/B1D,cAAc,EAAEmC,QAAQ,CAACnC,cAAc;UACvCsD,QAAQ,EAAED,mBAAmB;UAC7BA,mBAAmB,EAAEA;SACtB;QAED;QACAM,UAAU,CAAC,MAAK;UACd,MAAMC,eAAe,GAAGC,QAAQ,CAACC,cAAc,CAAC,2BAA2B,CAAC;UAC5E,IAAIF,eAAe,IAAI,IAAI,CAACL,oBAAoB,EAAE;YAChDK,eAAe,CAACG,WAAW,GAAG,IAAI,CAACR,oBAAoB,CAACD,QAAQ,IAAI,wBAAwB;;QAEhG,CAAC,EAAE,GAAG,CAAC;QAEP,IAAI,CAACxC,OAAO,GAAG,KAAK;QACpB,IAAI,CAACwB,gBAAgB,EAAE;MACzB,CAAC;MACDnB,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,kCAAkC,EAAEC,GAAG,CAAC;QACtD,IAAI,CAACD,KAAK,GAAG,uEAAuE;QACpF,IAAI,CAACL,OAAO,GAAG,KAAK;QACpB,IAAI,CAACwB,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEAA,gBAAgB;IACd,IAAI,CAAC/C,YAAY,CAACyE,IAAI,CAAC,cAAc,CAAC;EACxC;EAEAC,iBAAiB;IACf,IAAI,CAAC1E,YAAY,CAAC2E,KAAK,CAAC,cAAc,CAAC;EACzC;EAEAC,qBAAqB;IACnB,IAAI,CAAC5E,YAAY,CAAC2E,KAAK,CAAC,sBAAsB,CAAC;EACjD;EAEAE,oBAAoB;IAClB,IAAI,CAAC7E,YAAY,CAACyE,IAAI,CAAC,sBAAsB,CAAC;EAChD;EAEAK,mBAAmB;IACjB,IAAI,CAAC,IAAI,CAACd,oBAAoB,EAAE;MAC9B;;IAGF;IACA,MAAMe,IAAI,GAAG,IAAI,CAACC,YAAY,CAAC,IAAI,CAAChB,oBAAoB,CAACE,UAAU,EAAE,sBAAsB,CAAC;IAC5F,MAAMhB,GAAG,GAAG+B,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IAC5C,MAAMK,CAAC,GAAGd,QAAQ,CAACe,aAAa,CAAC,GAAG,CAAC;IACrCD,CAAC,CAACE,IAAI,GAAGpC,GAAG;IACZkC,CAAC,CAACG,QAAQ,GAAG,eAAe,IAAI,CAAC1C,iBAAiB,MAAM;IACxDyB,QAAQ,CAACkB,IAAI,CAACC,WAAW,CAACL,CAAC,CAAC;IAC5BA,CAAC,CAACM,KAAK,EAAE;IACTT,MAAM,CAACC,GAAG,CAACS,eAAe,CAACzC,GAAG,CAAC;IAC/BoB,QAAQ,CAACkB,IAAI,CAACI,WAAW,CAACR,CAAC,CAAC;EAC9B;EAEA;;;EAGQJ,YAAY,CAACa,MAAc,EAAEC,WAAmB;IACtD,MAAMC,cAAc,GAAGC,IAAI,CAACH,MAAM,CAAC;IACnC,MAAMI,UAAU,GAAG,EAAE;IAErB,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGH,cAAc,CAACI,MAAM,EAAED,MAAM,IAAI,GAAG,EAAE;MAClE,MAAME,KAAK,GAAGL,cAAc,CAACK,KAAK,CAACF,MAAM,EAAEA,MAAM,GAAG,GAAG,CAAC;MACxD,MAAMG,WAAW,GAAG,IAAIC,KAAK,CAACF,KAAK,CAACD,MAAM,CAAC;MAE3C,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACD,MAAM,EAAEI,CAAC,EAAE,EAAE;QACrCF,WAAW,CAACE,CAAC,CAAC,GAAGH,KAAK,CAACI,UAAU,CAACD,CAAC,CAAC;;MAGtC,MAAME,SAAS,GAAG,IAAIC,UAAU,CAACL,WAAW,CAAC;MAC7CJ,UAAU,CAACU,IAAI,CAACF,SAAS,CAAC;;IAG5B,OAAO,IAAIG,IAAI,CAACX,UAAU,EAAE;MAAEY,IAAI,EAAEf;IAAW,CAAE,CAAC;EACpD;EAEAgB,aAAa;IACX,IAAI,CAACpC,iBAAiB,EAAE;IACxB,IAAI,CAAC9E,MAAM,CAACmH,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEAC,YAAY,CAACC,IAAY;IACvB,IAAIA,IAAI,KAAK,IAAI,CAACjF,WAAW,EAAE;MAC7B,OAAO,QAAQ;KAChB,MAAM,IAAIiF,IAAI,GAAG,IAAI,CAACjF,WAAW,EAAE;MAClC,OAAO,WAAW;KACnB,MAAM;MACL,OAAO,EAAE;;EAEb;;;uBA/QWvC,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAyH;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCfpC9H,8BAA4B;UAGLA,+DAAgC;UAAAA,iBAAK;UAExDA,8BAAuB;UACrBA,wEAEM;UAGNA,8BAA+B;UAEFA,kBAAC;UAAAA,iBAAM;UAChCA,+BAAwB;UAAAA,mCAAQ;UAAAA,iBAAM;UAExCA,+BAA8C;UACnBA,kBAAC;UAAAA,iBAAM;UAChCA,+BAAwB;UAAAA,yBAAQ;UAAAA,iBAAM;UAExCA,+BAA8C;UACnBA,kBAAC;UAAAA,iBAAM;UAChCA,+BAAwB;UAAAA,uBAAM;UAAAA,iBAAM;UAEtCA,+BAA8C;UACnBA,kBAAC;UAAAA,iBAAM;UAChCA,+BAAwB;UAAAA,gCAAU;UAAAA,iBAAM;UAK5CA,4EAcM;UAGNA,6EA0CM;UAGNA,4EAwBM;UAGNA,4EAYM;UAGNA,gCAAiD;UAC/CA,iFAES;UACTA,4BAAK;UACHA,iFAES;UACTA,iFAGS;UACXA,iBAAM;UAOdA,gCAA+G;UAIxDA,wCAAkB;UAAAA,iBAAK;UACtEA,mCAA0G;UAAlDA;YAAA,OAAS+H,uBAAmB;UAAA,EAAC;UAAqB/H,iBAAS;UAErHA,gCAAwB;UAEpBA,yBAA4C;UAC5CA,4EACF;UAAAA,iBAAM;UAGNA,2EAQM;UACRA,iBAAM;UACNA,gCAA0B;UACwBA;YAAA,OAAS+H,uBAAmB;UAAA,EAAC;UAAC/H,iCAAM;UAAAA,iBAAS;UAC7FA,mCAAwE;UAA1BA;YAAA,OAAS+H,mBAAe;UAAA,EAAC;UACrE/H,sEACF;UAAAA,iBAAS;UAOjBA,kDAKqC;UADnCA;YAAA,OAAS+H,2BAAuB;UAAA,EAAC;YAAA,OACrBA,yBAAqB;UAAA,EADA;UAEnC/H,iBAAwB;;;UA7LZA,eAAW;UAAXA,gCAAW;UAMGA,eAA2B;UAA3BA,6CAA2B;UAI3BA,eAA2B;UAA3BA,6CAA2B;UAI3BA,eAA2B;UAA3BA,6CAA2B;UAI3BA,eAA2B;UAA3BA,6CAA2B;UAOzCA,eAAuB;UAAvBA,4CAAuB;UAiBvBA,eAAuB;UAAvBA,4CAAuB;UA6CvBA,eAAuB;UAAvBA,4CAAuB;UA2BvBA,eAAuB;UAAvBA,4CAAuB;UAgBiCA,eAAqB;UAArBA,0CAAqB;UAIpBA,eAA8B;UAA9BA,uDAA8B;UAG7BA,eAAgC;UAAhCA,yDAAgC;UAyBxFA,gBAA0B;UAA1BA,+CAA0B;UAsBtCA,eAAoC;UAApCA,sDAAoC", "names": ["Validators", "InstanceStatus", "i0", "InstanceWizardComponent", "constructor", "fb", "router", "customerService", "instanceService", "certificateService", "modalService", "customerForm", "group", "customerId", "required", "instanceForm", "name", "max<PERSON><PERSON><PERSON>", "serverUrl", "expirationDate", "status", "Active", "notes", "modulesForm", "moduleReporting", "moduleAdvancedSecurity", "moduleApiIntegration", "moduleDataExport", "moduleCustomization", "certificateForm", "generateCertificate", "ngOnInit", "loadCustomers", "loading", "getCustomers", "subscribe", "next", "customers", "error", "err", "console", "nextStep", "currentStep", "totalSteps", "invalid", "mark<PERSON>llAsTouched", "previousStep", "createInstance", "value", "instanceFormValue", "instanceData", "trim", "Date", "Number", "response", "createdInstanceId", "id", "showSuccessModal", "statusText", "message", "url", "timestamp", "toISOString", "errorMessage", "errors", "validationErrors", "Object", "keys", "map", "key", "join", "instanceId", "certificatePassword", "password", "generatedCertificate", "certificate", "privateKey", "thumbprint", "setTimeout", "passwordElement", "document", "getElementById", "textContent", "open", "closeSuccessModal", "close", "closeCertificateModal", "showCertificateModal", "downloadCertificate", "blob", "base64ToBlob", "window", "URL", "createObjectURL", "a", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "base64", "contentType", "byteCharacters", "atob", "byteArrays", "offset", "length", "slice", "byteNumbers", "Array", "i", "charCodeAt", "byteArray", "Uint8Array", "push", "Blob", "type", "goToCustomers", "navigate", "getStepClass", "step", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\instance-wizard\\instance-wizard.component.ts", "C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\instance-wizard\\instance-wizard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { CustomerService } from '../services/customer.service';\nimport { InstanceService } from '../services/instance.service';\nimport { CertificateService } from '../services/certificate.service';\nimport { ModalService } from '../services/modal.service';\nimport { Customer } from '../models/customer.model';\nimport { InstanceStatus } from '../models/instance.model';\n\n@Component({\n  selector: 'app-instance-wizard',\n  templateUrl: './instance-wizard.component.html',\n  styleUrls: ['./instance-wizard.component.css']\n})\nexport class InstanceWizardComponent implements OnInit {\n  currentStep: number = 1;\n  totalSteps: number = 4;\n\n  customers: Customer[] = [];\n  loading: boolean = false;\n  error: string | null = null;\n\n  // Export enum pro použití v template\n  InstanceStatus = InstanceStatus;\n\n  customerForm: FormGroup;\n  instanceForm: FormGroup;\n  modulesForm: FormGroup;\n  certificateForm: FormGroup;\n\n  createdInstanceId: number | null = null;\n  generatedCertificate: any = null;\n\n  constructor(\n    private fb: FormBuilder,\n    private router: Router,\n    private customerService: CustomerService,\n    private instanceService: InstanceService,\n    private certificateService: CertificateService,\n    private modalService: ModalService\n  ) {\n    this.customerForm = this.fb.group({\n      customerId: ['', Validators.required]\n    });\n\n    this.instanceForm = this.fb.group({\n      name: ['', [Validators.required, Validators.maxLength(200)]],\n      serverUrl: ['', [Validators.required, Validators.maxLength(255)]],\n      expirationDate: [''],\n      status: [InstanceStatus.Active, Validators.required],\n      notes: ['', Validators.maxLength(500)]\n    });\n\n    this.modulesForm = this.fb.group({\n      moduleReporting: [true],\n      moduleAdvancedSecurity: [false],\n      moduleApiIntegration: [false],\n      moduleDataExport: [false],\n      moduleCustomization: [false]\n    });\n\n    this.certificateForm = this.fb.group({\n      generateCertificate: [true]\n    });\n  }\n\n  ngOnInit(): void {\n    this.loadCustomers();\n  }\n\n  loadCustomers(): void {\n    this.loading = true;\n    this.customerService.getCustomers().subscribe({\n      next: (customers) => {\n        this.customers = customers;\n        this.loading = false;\n      },\n      error: (err) => {\n        console.error('Chyba při načítání zákazníků', err);\n        this.error = 'Chyba při načítání zákazníků';\n        this.loading = false;\n      }\n    });\n  }\n\n  nextStep(): void {\n    if (this.currentStep < this.totalSteps) {\n      // Validace aktuálního kroku\n      if (this.currentStep === 1 && this.customerForm.invalid) {\n        this.customerForm.markAllAsTouched();\n        return;\n      }\n\n      if (this.currentStep === 2 && this.instanceForm.invalid) {\n        this.instanceForm.markAllAsTouched();\n        return;\n      }\n\n      this.currentStep++;\n    }\n  }\n\n  previousStep(): void {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n\n  createInstance(): void {\n    if (this.customerForm.invalid || this.instanceForm.invalid || this.modulesForm.invalid) {\n      return;\n    }\n\n    this.loading = true;\n    this.error = null;\n\n    const customerId = this.customerForm.value.customerId;\n    const instanceFormValue = this.instanceForm.value;\n\n    // Převod prázdného stringu na null pro expirationDate a správný převod status\n    const instanceData = {\n      ...instanceFormValue,\n      ...this.modulesForm.value,\n      customerId,\n      expirationDate: instanceFormValue.expirationDate && instanceFormValue.expirationDate.trim() !== ''\n        ? new Date(instanceFormValue.expirationDate)\n        : null,\n      status: Number(instanceFormValue.status) // Zajistíme, že status je číslo\n    };\n\n    this.instanceService.createInstance(instanceData).subscribe({\n      next: (response) => {\n        this.createdInstanceId = response.id;\n\n        if (this.certificateForm.value.generateCertificate && this.createdInstanceId) {\n          this.generateCertificate(this.createdInstanceId);\n        } else {\n          this.loading = false;\n          this.showSuccessModal();\n        }\n      },\n      error: (err) => {\n        console.error('Chyba při vytváření instance - detailní informace:', {\n          error: err,\n          status: err.status,\n          statusText: err.statusText,\n          message: err.error?.message || err.message,\n          url: err.url,\n          instanceData: instanceData,\n          timestamp: new Date().toISOString()\n        });\n\n        // Zobrazení detailnější chybové zprávy uživateli\n        let errorMessage = 'Chyba při vytváření instance';\n\n        if (err.error?.message) {\n          errorMessage = err.error.message;\n\n          // Pokud jsou k dispozici validační chyby, zobrazíme je\n          if (err.error.errors) {\n            const validationErrors = Object.keys(err.error.errors)\n              .map(key => err.error.errors[key].join(', '))\n              .join('; ');\n            errorMessage += ': ' + validationErrors;\n          }\n        } else if (err.status === 0) {\n          errorMessage = 'Chyba připojení k serveru';\n        } else if (err.status >= 500) {\n          errorMessage = 'Chyba serveru při vytváření instance';\n        } else if (err.status === 400) {\n          errorMessage = 'Neplatná data pro vytvoření instance';\n        } else if (err.status === 404) {\n          errorMessage = 'Zákazník nebyl nalezen';\n        }\n\n        this.error = errorMessage;\n        this.loading = false;\n      }\n    });\n  }\n\n  generateCertificate(instanceId: number): void {\n    this.certificateService.generateCertificate(instanceId).subscribe({\n      next: (response) => {\n        // Uložení vygenerovaného certifikátu - vytvoříme kopii, aby se zajistilo, že se data nepřepíší\n        // Zkontrolujeme, zda heslo existuje v odpovědi, jinak použijeme výchozí heslo\n        const certificatePassword = response.password || 'password';\n\n        this.generatedCertificate = {\n          certificate: response.certificate,\n          privateKey: response.privateKey,\n          thumbprint: response.thumbprint,\n          expirationDate: response.expirationDate,\n          password: certificatePassword,\n          certificatePassword: certificatePassword\n        };\n\n        // Explicitní nastavení hesla pro zobrazení v modálním okně\n        setTimeout(() => {\n          const passwordElement = document.getElementById('wizardCertificatePassword');\n          if (passwordElement && this.generatedCertificate) {\n            passwordElement.textContent = this.generatedCertificate.password || 'Heslo není k dispozici';\n          }\n        }, 100);\n\n        this.loading = false;\n        this.showSuccessModal();\n      },\n      error: (err) => {\n        console.error('Chyba při generování certifikátu', err);\n        this.error = 'Instance byla vytvořena, ale došlo k chybě při generování certifikátu';\n        this.loading = false;\n        this.showSuccessModal();\n      }\n    });\n  }\n\n  showSuccessModal(): void {\n    this.modalService.open('successModal');\n  }\n\n  closeSuccessModal(): void {\n    this.modalService.close('successModal');\n  }\n\n  closeCertificateModal(): void {\n    this.modalService.close('certificateInfoModal');\n  }\n\n  showCertificateModal(): void {\n    this.modalService.open('certificateInfoModal');\n  }\n\n  downloadCertificate(): void {\n    if (!this.generatedCertificate) {\n      return;\n    }\n\n    // Vytvoření a stažení souboru .pfx\n    const blob = this.base64ToBlob(this.generatedCertificate.privateKey, 'application/x-pkcs12');\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `certificate-${this.createdInstanceId}.pfx`;\n    document.body.appendChild(a);\n    a.click();\n    window.URL.revokeObjectURL(url);\n    document.body.removeChild(a);\n  }\n\n  /**\n   * Pomocná metoda pro konverzi Base64 na Blob\n   */\n  private base64ToBlob(base64: string, contentType: string): Blob {\n    const byteCharacters = atob(base64);\n    const byteArrays = [];\n\n    for (let offset = 0; offset < byteCharacters.length; offset += 512) {\n      const slice = byteCharacters.slice(offset, offset + 512);\n      const byteNumbers = new Array(slice.length);\n\n      for (let i = 0; i < slice.length; i++) {\n        byteNumbers[i] = slice.charCodeAt(i);\n      }\n\n      const byteArray = new Uint8Array(byteNumbers);\n      byteArrays.push(byteArray);\n    }\n\n    return new Blob(byteArrays, { type: contentType });\n  }\n\n  goToCustomers(): void {\n    this.closeSuccessModal();\n    this.router.navigate(['/customers']);\n  }\n\n  getStepClass(step: number): string {\n    if (step === this.currentStep) {\n      return 'active';\n    } else if (step < this.currentStep) {\n      return 'completed';\n    } else {\n      return '';\n    }\n  }\n}\n", "<div class=\"container mt-4\">\n  <div class=\"card\">\n    <div class=\"card-header bg-primary text-white\">\n      <h4 class=\"mb-0\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vytvořením instance DIS</h4>\n    </div>\n    <div class=\"card-body\">\n      <div *ngIf=\"error\" class=\"alert alert-danger mb-4\">\n        {{ error }}\n      </div>\n\n      <!-- <PERSON><PERSON>y průvodce -->\n      <div class=\"wizard-steps mb-4\">\n        <div class=\"step\" [ngClass]=\"getStepClass(1)\">\n          <div class=\"step-number\">1</div>\n          <div class=\"step-title\">Zákazník</div>\n        </div>\n        <div class=\"step\" [ngClass]=\"getStepClass(2)\">\n          <div class=\"step-number\">2</div>\n          <div class=\"step-title\">Instance</div>\n        </div>\n        <div class=\"step\" [ngClass]=\"getStepClass(3)\">\n          <div class=\"step-number\">3</div>\n          <div class=\"step-title\">Moduly</div>\n        </div>\n        <div class=\"step\" [ngClass]=\"getStepClass(4)\">\n          <div class=\"step-number\">4</div>\n          <div class=\"step-title\">Certifikát</div>\n        </div>\n      </div>\n\n      <!-- Krok 1: Výběr zákazníka -->\n      <div *ngIf=\"currentStep === 1\">\n        <h5 class=\"mb-3\">Vyberte zákazníka</h5>\n        <form [formGroup]=\"customerForm\">\n          <div class=\"mb-3\">\n            <label for=\"customerId\" class=\"form-label required\">Zákazník</label>\n            <select id=\"customerId\" formControlName=\"customerId\" class=\"form-select\" [ngClass]=\"{'is-invalid': customerForm.get('customerId')?.invalid && customerForm.get('customerId')?.touched}\">\n              <option value=\"\">-- Vyberte zákazníka --</option>\n              <option *ngFor=\"let customer of customers\" [value]=\"customer.id\">{{ customer.name }}</option>\n            </select>\n            <div *ngIf=\"customerForm.get('customerId')?.invalid && customerForm.get('customerId')?.touched\" class=\"invalid-feedback\">\n              Vyberte zákazníka\n            </div>\n          </div>\n        </form>\n      </div>\n\n      <!-- Krok 2: Informace o instanci -->\n      <div *ngIf=\"currentStep === 2\">\n        <h5 class=\"mb-3\">Informace o instanci</h5>\n        <form [formGroup]=\"instanceForm\">\n          <div class=\"mb-3\">\n            <label for=\"name\" class=\"form-label required\">Název instance</label>\n            <input type=\"text\" id=\"name\" formControlName=\"name\" class=\"form-control\" [ngClass]=\"{'is-invalid': instanceForm.get('name')?.invalid && instanceForm.get('name')?.touched}\">\n            <div *ngIf=\"instanceForm.get('name')?.invalid && instanceForm.get('name')?.touched\" class=\"invalid-feedback\">\n              <span *ngIf=\"instanceForm.get('name')?.errors?.['required']\">Zadejte název instance</span>\n              <span *ngIf=\"instanceForm.get('name')?.errors?.['maxlength']\">Název instance nesmí být delší než 200 znaků</span>\n            </div>\n          </div>\n          <div class=\"mb-3\">\n            <label for=\"serverUrl\" class=\"form-label required\">URL serveru</label>\n            <input type=\"text\" id=\"serverUrl\" formControlName=\"serverUrl\" class=\"form-control\" [ngClass]=\"{'is-invalid': instanceForm.get('serverUrl')?.invalid && instanceForm.get('serverUrl')?.touched}\">\n            <div *ngIf=\"instanceForm.get('serverUrl')?.invalid && instanceForm.get('serverUrl')?.touched\" class=\"invalid-feedback\">\n              <span *ngIf=\"instanceForm.get('serverUrl')?.errors?.['required']\">Zadejte URL serveru</span>\n              <span *ngIf=\"instanceForm.get('serverUrl')?.errors?.['maxlength']\">URL serveru nesmí být delší než 255 znaků</span>\n            </div>\n          </div>\n\n          <div class=\"mb-3\">\n            <label for=\"expirationDate\" class=\"form-label\">Datum expirace</label>\n            <input type=\"date\" id=\"expirationDate\" formControlName=\"expirationDate\" class=\"form-control\">\n          </div>\n          <div class=\"mb-3\">\n            <label for=\"status\" class=\"form-label\">Status</label>\n            <select id=\"status\" formControlName=\"status\" class=\"form-select\">\n              <option [value]=\"InstanceStatus.Active\">Aktivní</option>\n              <option [value]=\"InstanceStatus.Trial\">Trial</option>\n              <option [value]=\"InstanceStatus.Maintenance\">Údržba</option>\n              <option [value]=\"InstanceStatus.Blocked\">Blokováno</option>\n              <option [value]=\"InstanceStatus.Expired\">Expirováno</option>\n            </select>\n          </div>\n          <div class=\"mb-3\">\n            <label for=\"notes\" class=\"form-label\">Poznámky</label>\n            <textarea id=\"notes\" formControlName=\"notes\" class=\"form-control\" rows=\"3\" [ngClass]=\"{'is-invalid': instanceForm.get('notes')?.invalid && instanceForm.get('notes')?.touched}\"></textarea>\n            <div *ngIf=\"instanceForm.get('notes')?.invalid && instanceForm.get('notes')?.touched\" class=\"invalid-feedback\">\n              <span *ngIf=\"instanceForm.get('notes')?.errors?.['maxlength']\">Poznámky nesmí být delší než 500 znaků</span>\n            </div>\n          </div>\n        </form>\n      </div>\n\n      <!-- Krok 3: Moduly -->\n      <div *ngIf=\"currentStep === 3\">\n        <h5 class=\"mb-3\">Povolené moduly</h5>\n        <form [formGroup]=\"modulesForm\">\n          <div class=\"mb-3 form-check\">\n            <input type=\"checkbox\" id=\"moduleReporting\" formControlName=\"moduleReporting\" class=\"form-check-input\">\n            <label for=\"moduleReporting\" class=\"form-check-label\">Reporting</label>\n          </div>\n          <div class=\"mb-3 form-check\">\n            <input type=\"checkbox\" id=\"moduleAdvancedSecurity\" formControlName=\"moduleAdvancedSecurity\" class=\"form-check-input\">\n            <label for=\"moduleAdvancedSecurity\" class=\"form-check-label\">Rozšířené zabezpečení</label>\n          </div>\n          <div class=\"mb-3 form-check\">\n            <input type=\"checkbox\" id=\"moduleApiIntegration\" formControlName=\"moduleApiIntegration\" class=\"form-check-input\">\n            <label for=\"moduleApiIntegration\" class=\"form-check-label\">API integrace</label>\n          </div>\n          <div class=\"mb-3 form-check\">\n            <input type=\"checkbox\" id=\"moduleDataExport\" formControlName=\"moduleDataExport\" class=\"form-check-input\">\n            <label for=\"moduleDataExport\" class=\"form-check-label\">Export dat</label>\n          </div>\n          <div class=\"mb-3 form-check\">\n            <input type=\"checkbox\" id=\"moduleCustomization\" formControlName=\"moduleCustomization\" class=\"form-check-input\">\n            <label for=\"moduleCustomization\" class=\"form-check-label\">Přizpůsobení</label>\n          </div>\n        </form>\n      </div>\n\n      <!-- Krok 4: Certifikát -->\n      <div *ngIf=\"currentStep === 4\">\n        <h5 class=\"mb-3\">Certifikát</h5>\n        <form [formGroup]=\"certificateForm\">\n          <div class=\"mb-3 form-check\">\n            <input type=\"checkbox\" id=\"generateCertificate\" formControlName=\"generateCertificate\" class=\"form-check-input\">\n            <label for=\"generateCertificate\" class=\"form-check-label\">Vygenerovat certifikát pro instanci</label>\n          </div>\n          <div class=\"alert alert-info\">\n            <i class=\"bi bi-info-circle-fill me-2\"></i>\n            Certifikát bude vygenerován automaticky po vytvoření instance. Bude platný po dobu 1 roku.\n          </div>\n        </form>\n      </div>\n\n      <!-- Navigační tlačítka -->\n      <div class=\"d-flex justify-content-between mt-4\">\n        <button class=\"btn btn-secondary\" (click)=\"previousStep()\" *ngIf=\"currentStep > 1\" [disabled]=\"loading\">\n          <i class=\"bi bi-arrow-left me-2\"></i>Zpět\n        </button>\n        <div>\n          <button class=\"btn btn-primary me-2\" (click)=\"nextStep()\" *ngIf=\"currentStep < totalSteps\" [disabled]=\"loading\">\n            Další<i class=\"bi bi-arrow-right ms-2\"></i>\n          </button>\n          <button class=\"btn btn-success\" (click)=\"createInstance()\" *ngIf=\"currentStep === totalSteps\" [disabled]=\"loading\">\n            <i class=\"bi bi-check-circle-fill me-2\"></i>Vytvořit instanci\n            <span *ngIf=\"loading\" class=\"spinner-border spinner-border-sm ms-2\" role=\"status\" aria-hidden=\"true\"></span>\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal s potvrzením úspěšného vytvoření -->\n<div class=\"modal fade\" id=\"successModal\" tabindex=\"-1\" aria-labelledby=\"successModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header bg-success text-white\">\n        <h5 class=\"modal-title\" id=\"successModalLabel\">Instance vytvořena</h5>\n        <button type=\"button\" class=\"btn-close btn-close-white\" (click)=\"closeSuccessModal()\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <div class=\"alert alert-success\">\n          <i class=\"bi bi-check-circle-fill me-2\"></i>\n          Instance byla úspěšně vytvořena.\n        </div>\n\n        <!-- Odkaz na certifikát -->\n        <div *ngIf=\"generatedCertificate\" class=\"mt-3\">\n          <div class=\"alert alert-info\">\n            <i class=\"bi bi-info-circle-fill me-2\"></i>\n            Pro instanci byl vygenerován certifikát. Můžete ho stáhnout kliknutím na tlačítko níže.\n          </div>\n          <button class=\"btn btn-primary\" (click)=\"showCertificateModal()\">\n            <i class=\"bi bi-info-circle me-2\"></i>Zobrazit certifikát\n          </button>\n        </div>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btn btn-secondary\" (click)=\"closeSuccessModal()\">Zavřít</button>\n        <button type=\"button\" class=\"btn btn-primary\" (click)=\"goToCustomers()\">\n          Přejít na seznam zákazníků\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Sdílená komponenta pro zobrazení vygenerovaného certifikátu -->\n<app-certificate-modal\n  [certificate]=\"generatedCertificate\"\n  [instanceName]=\"instanceForm.value.name || ''\"\n  modalId=\"certificateInfoModal\"\n  (close)=\"closeCertificateModal()\"\n  (download)=\"downloadCertificate()\">\n</app-certificate-modal>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}