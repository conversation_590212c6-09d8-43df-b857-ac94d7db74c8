{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"ngx-toastr\";\nimport * as i2 from \"@angular/common\";\nfunction CertificateModalComponent_div_11_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"i\", 27);\n    i0.ɵɵtext(2, \" Heslo bylo zkop\\u00EDrov\\u00E1no do schr\\u00E1nky. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CertificateModalComponent_div_11_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"i\", 29);\n    i0.ɵɵtext(2, \" Pou\\u017E\\u00EDv\\u00E1 se v\\u00FDchoz\\u00ED heslo \\\"password\\\". Pro v\\u011Bt\\u0161\\u00ED bezpe\\u010Dnost doporu\\u010Dujeme po instalaci certifik\\u00E1tu zm\\u011Bnit heslo. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CertificateModalComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"h6\", 12);\n    i0.ɵɵtext(2, \"Informace o certifik\\u00E1tu:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 13)(4, \"label\", 14);\n    i0.ɵɵtext(5, \"Thumbprint:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 15);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 13)(9, \"label\", 14);\n    i0.ɵɵtext(10, \"Platnost do:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 15);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 13)(15, \"label\", 14);\n    i0.ɵɵtext(16, \"Heslo k certifik\\u00E1tu:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 16)(18, \"div\", 17);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function CertificateModalComponent_div_11_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.copyPasswordToClipboard());\n    });\n    i0.ɵɵelement(21, \"i\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, CertificateModalComponent_div_11_div_22_Template, 3, 0, \"div\", 20);\n    i0.ɵɵtemplate(23, CertificateModalComponent_div_11_div_23_Template, 3, 0, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 22);\n    i0.ɵɵelement(25, \"i\", 23);\n    i0.ɵɵelementStart(26, \"strong\");\n    i0.ɵɵtext(27, \"D\\u016Fle\\u017Eit\\u00E9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28, \" Toto heslo si poznamenejte. Budete ho pot\\u0159ebovat p\\u0159i instalaci certifik\\u00E1tu. Z bezpe\\u010Dnostn\\u00EDch d\\u016Fvod\\u016F nen\\u00ED heslo nikde ulo\\u017Eeno a nebude mo\\u017En\\u00E9 ho pozd\\u011Bji zobrazit. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 12)(30, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function CertificateModalComponent_div_11_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.downloadCertificate());\n    });\n    i0.ɵɵelement(31, \"i\", 25);\n    i0.ɵɵtext(32, \"St\\u00E1hnout certifik\\u00E1t \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.certificate.thumbprint);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(13, 6, ctx_r0.certificate.expirationDate, \"dd.MM.yyyy HH:mm\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.certificate.password, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.passwordCopied ? \"bi-check-lg\" : \"bi-clipboard\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.passwordCopied);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.certificate.password === \"password\");\n  }\n}\nexport class CertificateModalComponent {\n  constructor(toastr) {\n    this.toastr = toastr;\n    this.certificate = null;\n    this.instanceName = '';\n    this.modalId = 'certificateModal';\n    this.close = new EventEmitter();\n    this.download = new EventEmitter();\n    // Příznak pro zobrazení potvrzení o zkopírování\n    this.passwordCopied = false;\n  }\n  /**\r\n   * Zavření modálního okna\r\n   */\n  closeModal() {\n    this.close.emit();\n  }\n  /**\r\n   * Stažení certifikátu\r\n   */\n  downloadCertificate() {\n    this.download.emit();\n  }\n  /**\r\n   * Zkopírování hesla certifikátu do schránky\r\n   */\n  copyPasswordToClipboard() {\n    if (this.certificate && this.certificate.password) {\n      navigator.clipboard.writeText(this.certificate.password).then(() => {\n        // Nastavení příznaku pro zobrazení potvrzení\n        this.passwordCopied = true;\n        // Po 2 sekundách skryjeme potvrzení\n        setTimeout(() => {\n          this.passwordCopied = false;\n        }, 2000);\n      }).catch(err => {\n        console.error('Nepodařilo se zkopírovat heslo do schránky:', err);\n      });\n    }\n  }\n  static {\n    this.ɵfac = function CertificateModalComponent_Factory(t) {\n      return new (t || CertificateModalComponent)(i0.ɵɵdirectiveInject(i1.ToastrService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CertificateModalComponent,\n      selectors: [[\"app-certificate-modal\"]],\n      inputs: {\n        certificate: \"certificate\",\n        instanceName: \"instanceName\",\n        modalId: \"modalId\"\n      },\n      outputs: {\n        close: \"close\",\n        download: \"download\"\n      },\n      decls: 15,\n      vars: 5,\n      consts: [[\"tabindex\", \"-1\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\", 3, \"id\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\", \"bg-success\", \"text-white\"], [1, \"modal-title\", 3, \"id\"], [\"type\", \"button\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\", \"btn-close-white\", 3, \"click\"], [1, \"modal-body\"], [1, \"alert\", \"alert-success\", \"mb-4\"], [1, \"bi\", \"bi-check-circle-fill\", \"me-2\"], [4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"mt-3\"], [1, \"mb-3\"], [1, \"form-label\"], [1, \"form-control\", \"bg-light\"], [1, \"input-group\"], [\"id\", \"certificatePassword\", 1, \"form-control\", \"bg-light\", \"font-monospace\", \"fw-bold\"], [\"type\", \"button\", \"title\", \"Zkop\\u00EDrovat heslo do schr\\u00E1nky\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"bi\", 3, \"ngClass\"], [\"class\", \"mt-1 small text-success\", 4, \"ngIf\"], [\"class\", \"mt-2 small text-info\", 4, \"ngIf\"], [1, \"alert\", \"alert-warning\", \"mt-3\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"me-2\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-download\", \"me-2\"], [1, \"mt-1\", \"small\", \"text-success\"], [1, \"bi\", \"bi-check-circle\", \"me-1\"], [1, \"mt-2\", \"small\", \"text-info\"], [1, \"bi\", \"bi-info-circle\", \"me-1\"]],\n      template: function CertificateModalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h5\", 4);\n          i0.ɵɵtext(5, \"Certifik\\u00E1t byl vygenerov\\u00E1n\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function CertificateModalComponent_Template_button_click_6_listener() {\n            return ctx.closeModal();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7);\n          i0.ɵɵelement(9, \"i\", 8);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, CertificateModalComponent_div_11_Template, 33, 9, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function CertificateModalComponent_Template_button_click_13_listener() {\n            return ctx.closeModal();\n          });\n          i0.ɵɵtext(14, \"Zav\\u0159\\u00EDt\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"id\", ctx.modalId);\n          i0.ɵɵattribute(\"aria-labelledby\", ctx.modalId + \"Label\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"id\", ctx.modalId + \"Label\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" Certifik\\u00E1t byl \\u00FAsp\\u011B\\u0161n\\u011B vygenerov\\u00E1n pro instanci \", ctx.instanceName, \". \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.certificate);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgIf, i2.DatePipe],\n      styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NlcnRpZmljYXRlLW1vZGFsL2NlcnRpZmljYXRlLW1vZGFsLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEseUNBQXlDIiwic291cmNlc0NvbnRlbnQiOlsiLyogw4XCvcODwqFkbsODwqkgc3BlY2nDg8KhbG7Dg8KtIHN0eWx5IG5lanNvdSBwb3TDhcKZZWJhICovXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAAA,SAAmCA,YAAY,QAAQ,eAAe;;;;;;ICiC1DC,+BAA4D;IAC1DA,wBAAuC;IACvCA,oEACF;IAAAA,iBAAM;;;;;IACNA,+BAA8E;IAC5EA,wBAAsC;IACtCA,6LACF;IAAAA,iBAAM;;;;;;IA3BVA,2BAAyB;IACNA,6CAAwB;IAAAA,iBAAK;IAC9CA,+BAAkB;IACUA,2BAAW;IAAAA,iBAAQ;IAC7CA,+BAAmC;IAAAA,YAA4B;IAAAA,iBAAM;IAEvEA,+BAAkB;IACUA,6BAAY;IAAAA,iBAAQ;IAC9CA,gCAAmC;IAAAA,aAA0D;;IAAAA,iBAAM;IAErGA,gCAAkB;IACUA,0CAAoB;IAAAA,iBAAQ;IACtDA,gCAAyB;IAErBA,aACF;IAAAA,iBAAM;IACNA,mCAAiI;IAAzEA;MAAAA;MAAA;MAAA,OAASA,+CAAyB;IAAA,EAAC;IACzFA,yBAA8E;IAChFA,iBAAS;IAEXA,oFAGM;IACNA,oFAGM;IACRA,iBAAM;IAENA,gCAAsC;IACpCA,yBAAoD;IACpDA,+BAAQ;IAAAA,yCAAS;IAAAA,iBAAS;IAACA,+OAE7B;IAAAA,iBAAM;IAENA,gCAAkB;IACgBA;MAAAA;MAAA;MAAA,OAASA,2CAAqB;IAAA,EAAC;IAC7DA,yBAAmC;IAAAA,+CACrC;IAAAA,iBAAS;;;;IAnC0BA,eAA4B;IAA5BA,mDAA4B;IAI5BA,eAA0D;IAA1DA,kGAA0D;IAMzFA,eACF;IADEA,4DACF;IAEgBA,eAA2D;IAA3DA,gFAA2D;IAGvEA,eAAoB;IAApBA,4CAAoB;IAIpBA,eAAyC;IAAzCA,iEAAyC;;;AD5B3D,OAAM,MAAOC,yBAAyB;EAUpCC,YAAoBC,MAAqB;IAArB,WAAM,GAANA,MAAM;IATjB,gBAAW,GAAyC,IAAI;IACxD,iBAAY,GAAW,EAAE;IACzB,YAAO,GAAW,kBAAkB;IACnC,UAAK,GAAG,IAAIJ,YAAY,EAAQ;IAChC,aAAQ,GAAG,IAAIA,YAAY,EAAQ;IAE7C;IACA,mBAAc,GAAY,KAAK;EAEa;EAE5C;;;EAGAK,UAAU;IACR,IAAI,CAACC,KAAK,CAACC,IAAI,EAAE;EACnB;EAEA;;;EAGAC,mBAAmB;IACjB,IAAI,CAACC,QAAQ,CAACF,IAAI,EAAE;EACtB;EAEA;;;EAGAG,uBAAuB;IACrB,IAAI,IAAI,CAACC,WAAW,IAAI,IAAI,CAACA,WAAW,CAACC,QAAQ,EAAE;MACjDC,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,CAACJ,WAAW,CAACC,QAAQ,CAAC,CACrDI,IAAI,CAAC,MAAK;QACT;QACA,IAAI,CAACC,cAAc,GAAG,IAAI;QAE1B;QACAC,UAAU,CAAC,MAAK;UACd,IAAI,CAACD,cAAc,GAAG,KAAK;QAC7B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC,CACDE,KAAK,CAACC,GAAG,IAAG;QACXC,OAAO,CAACC,KAAK,CAAC,6CAA6C,EAAEF,GAAG,CAAC;MACnE,CAAC,CAAC;;EAER;;;uBA7CWlB,yBAAyB;IAAA;EAAA;;;YAAzBA,yBAAyB;MAAAqB;MAAAC;QAAAb;QAAAc;QAAAC;MAAA;MAAAC;QAAArB;QAAAG;MAAA;MAAAmB;MAAAC;MAAAC;MAAAC;QAAA;UCTtC9B,8BAAmH;UAI1DA,oDAA0B;UAAAA,iBAAK;UAChFA,iCAAmG;UAA3CA;YAAA,OAAS+B,gBAAY;UAAA,EAAC;UAAqB/B,iBAAS;UAE9GA,8BAAwB;UAEpBA,uBAA4C;UAC5CA,aACF;UAAAA,iBAAM;UAENA,6EAyCM;UACRA,iBAAM;UACNA,gCAA0B;UACwBA;YAAA,OAAS+B,gBAAY;UAAA,EAAC;UAAC/B,iCAAM;UAAAA,iBAAS;;;UAzDtEA,gCAAc;UAAeA,wDAA0C;UAI/DA,eAAwB;UAAxBA,0CAAwB;UAM9CA,eACF;UADEA,gIACF;UAEMA,eAAiB;UAAjBA,sCAAiB", "names": ["EventEmitter", "i0", "CertificateModalComponent", "constructor", "toastr", "closeModal", "close", "emit", "downloadCertificate", "download", "copyPasswordToClipboard", "certificate", "password", "navigator", "clipboard", "writeText", "then", "passwordCopied", "setTimeout", "catch", "err", "console", "error", "selectors", "inputs", "instanceName", "modalId", "outputs", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\shared\\certificate-modal\\certificate-modal.component.ts", "C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\shared\\certificate-modal\\certificate-modal.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter } from '@angular/core';\nimport { CertificateGenerationResponse } from '../../models/certificate.model';\nimport { ToastrService } from 'ngx-toastr';\n\n@Component({\n  selector: 'app-certificate-modal',\n  templateUrl: './certificate-modal.component.html',\n  styleUrls: ['./certificate-modal.component.css']\n})\nexport class CertificateModalComponent {\n  @Input() certificate: CertificateGenerationResponse | null = null;\n  @Input() instanceName: string = '';\n  @Input() modalId: string = 'certificateModal';\n  @Output() close = new EventEmitter<void>();\n  @Output() download = new EventEmitter<void>();\n\n  // Příznak pro zobrazení potvrzení o zkopírování\n  passwordCopied: boolean = false;\n\n  constructor(private toastr: ToastrService) {}\n\n  /**\n   * Zavření modálního okna\n   */\n  closeModal(): void {\n    this.close.emit();\n  }\n\n  /**\n   * Stažení certifikátu\n   */\n  downloadCertificate(): void {\n    this.download.emit();\n  }\n\n  /**\n   * Zkopírování hesla certifikátu do schránky\n   */\n  copyPasswordToClipboard(): void {\n    if (this.certificate && this.certificate.password) {\n      navigator.clipboard.writeText(this.certificate.password)\n        .then(() => {\n          // Nastavení příznaku pro zobrazení potvrzení\n          this.passwordCopied = true;\n\n          // Po 2 sekundách skryjeme potvrzení\n          setTimeout(() => {\n            this.passwordCopied = false;\n          }, 2000);\n        })\n        .catch(err => {\n          console.error('Nepodařilo se zkopírovat heslo do schránky:', err);\n        });\n    }\n  }\n}\n", "<div class=\"modal fade\" [id]=\"modalId\" tabindex=\"-1\" [attr.aria-labelledby]=\"modalId + 'Label'\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header bg-success text-white\">\n        <h5 class=\"modal-title\" [id]=\"modalId + 'Label'\">Certifikát byl vygenerován</h5>\n        <button type=\"button\" class=\"btn-close btn-close-white\" (click)=\"closeModal()\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <div class=\"alert alert-success mb-4\">\n          <i class=\"bi bi-check-circle-fill me-2\"></i>\n          Certifikát byl úspěšně vygenerován pro instanci {{ instanceName }}.\n        </div>\n\n        <div *ngIf=\"certificate\">\n          <h6 class=\"mt-3\">Informace o certifikátu:</h6>\n          <div class=\"mb-3\">\n            <label class=\"form-label\">Thumbprint:</label>\n            <div class=\"form-control bg-light\">{{ certificate.thumbprint }}</div>\n          </div>\n          <div class=\"mb-3\">\n            <label class=\"form-label\">Platnost do:</label>\n            <div class=\"form-control bg-light\">{{ certificate.expirationDate | date:'dd.MM.yyyy HH:mm' }}</div>\n          </div>\n          <div class=\"mb-3\">\n            <label class=\"form-label\">Heslo k certifikátu:</label>\n            <div class=\"input-group\">\n              <div id=\"certificatePassword\" class=\"form-control bg-light font-monospace fw-bold\">\n                {{ certificate.password }}\n              </div>\n              <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"copyPasswordToClipboard()\" title=\"Zkopírovat heslo do schránky\">\n                <i class=\"bi\" [ngClass]=\"passwordCopied ? 'bi-check-lg' : 'bi-clipboard'\"></i>\n              </button>\n            </div>\n            <div *ngIf=\"passwordCopied\" class=\"mt-1 small text-success\">\n              <i class=\"bi bi-check-circle me-1\"></i>\n              Heslo bylo zkopírováno do schránky.\n            </div>\n            <div *ngIf=\"certificate.password === 'password'\" class=\"mt-2 small text-info\">\n              <i class=\"bi bi-info-circle me-1\"></i>\n              Používá se výchozí heslo \"password\". Pro větší bezpečnost doporučujeme po instalaci certifikátu změnit heslo.\n            </div>\n          </div>\n\n          <div class=\"alert alert-warning mt-3\">\n            <i class=\"bi bi-exclamation-triangle-fill me-2\"></i>\n            <strong>Důležité:</strong> Toto heslo si poznamenejte. Budete ho potřebovat při instalaci certifikátu.\n            Z bezpečnostních důvodů není heslo nikde uloženo a nebude možné ho později zobrazit.\n          </div>\n\n          <div class=\"mt-3\">\n            <button class=\"btn btn-primary\" (click)=\"downloadCertificate()\">\n              <i class=\"bi bi-download me-2\"></i>Stáhnout certifikát\n            </button>\n          </div>\n        </div>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btn btn-secondary\" (click)=\"closeModal()\">Zavřít</button>\n      </div>\n    </div>\n  </div>\n</div>\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}