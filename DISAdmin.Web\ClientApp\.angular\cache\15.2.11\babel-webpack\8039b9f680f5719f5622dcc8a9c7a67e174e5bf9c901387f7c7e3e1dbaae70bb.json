{"ast": null, "code": "import { ApplicationLogLevelHelper } from '../../models/logs.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/logs.service\";\nimport * as i2 from \"../../services/modal.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"../log-filter/log-filter.component\";\nfunction ErrorLogsComponent_tr_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 16)(2, \"div\", 17)(3, \"span\", 18);\n    i0.ɵɵtext(4, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction ErrorLogsComponent_tr_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 19);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction ErrorLogsComponent_tr_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 16);\n    i0.ɵɵtext(2, \" Nebyly nalezeny \\u017E\\u00E1dn\\u00E9 logy aplikace \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ErrorLogsComponent_tr_28_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const log_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"badge rounded-pill \", ctx_r7.getStatusCodeClass(log_r5.statusCode), \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", log_r5.statusCode, \" \");\n  }\n}\nfunction ErrorLogsComponent_tr_28_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ErrorLogsComponent_tr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 20);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_td_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.showLogDetail(log_r5));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\", 20);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_td_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.showLogDetail(log_r5));\n    });\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 21);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_td_click_7_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.showLogDetail(log_r5));\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 22);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_td_click_9_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.showLogDetail(log_r5));\n    });\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 20);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_td_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.showLogDetail(log_r5));\n    });\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"td\", 22);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_td_click_14_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.showLogDetail(log_r5));\n    });\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 20);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_td_click_16_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.showLogDetail(log_r5));\n    });\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 20);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_td_click_18_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.showLogDetail(log_r5));\n    });\n    i0.ɵɵtemplate(19, ErrorLogsComponent_tr_28_span_19_Template, 2, 4, \"span\", 23);\n    i0.ɵɵtemplate(20, ErrorLogsComponent_tr_28_span_20_Template, 2, 0, \"span\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 20);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_td_click_21_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.showLogDetail(log_r5));\n    });\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\")(24, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_button_click_24_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.showLogDetail(log_r5));\n    });\n    i0.ɵɵelement(25, \"i\", 25);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const log_r5 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(3, 16, log_r5.timestamp, \"dd.MM.yyyy HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMapInterpolate1(\"badge rounded-pill \", ctx_r3.getLogLevelClass(log_r5.logLevel), \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getLocalizedLogLevel(log_r5.logLevel), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(log_r5.message);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(log_r5.category || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMapInterpolate1(\"badge rounded-pill \", ctx_r3.getSourceClass(log_r5.source), \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getLocalizedSource(log_r5.source), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(log_r5.requestPath || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(log_r5.requestMethod || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", log_r5.statusCode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !log_r5.statusCode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(log_r5.username || \"Syst\\u00E9m\");\n  }\n}\nfunction ErrorLogsComponent_div_36_span_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMapInterpolate1(\"badge rounded-pill \", ctx_r21.getStatusCodeClass(ctx_r21.selectedLog.statusCode), \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r21.selectedLog.statusCode, \" \");\n  }\n}\nfunction ErrorLogsComponent_div_36_span_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ErrorLogsComponent_div_36_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 30)(2, \"h6\");\n    i0.ɵɵtext(3, \"Stack Trace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 31)(5, \"div\", 32)(6, \"pre\", 35);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r23.selectedLog.stackTrace);\n  }\n}\nfunction ErrorLogsComponent_div_36_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 36)(2, \"h6\");\n    i0.ɵɵtext(3, \"Dodate\\u010Dn\\u00E9 informace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 31)(5, \"div\", 32)(6, \"pre\", 33);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r24.selectedLog.additionalInfo);\n  }\n}\nfunction ErrorLogsComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28)(3, \"h6\");\n    i0.ɵɵtext(4, \"Z\\u00E1kladn\\u00ED informace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"table\", 29)(6, \"tr\")(7, \"th\");\n    i0.ɵɵtext(8, \"ID:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"tr\")(12, \"th\");\n    i0.ɵɵtext(13, \"\\u010Cas:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"tr\")(18, \"th\");\n    i0.ɵɵtext(19, \"U\\u017Eivatel:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"tr\")(23, \"th\");\n    i0.ɵɵtext(24, \"IP adresa:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"td\");\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"tr\")(28, \"th\");\n    i0.ɵɵtext(29, \"Zdroj:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"td\")(31, \"span\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(33, \"div\", 28)(34, \"h6\");\n    i0.ɵɵtext(35, \"Detaily po\\u017Eadavku\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"table\", 29)(37, \"tr\")(38, \"th\");\n    i0.ɵɵtext(39, \"Cesta:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"td\");\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"tr\")(43, \"th\");\n    i0.ɵɵtext(44, \"Metoda:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"td\");\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"tr\")(48, \"th\");\n    i0.ɵɵtext(49, \"Stavov\\u00FD k\\u00F3d:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"td\");\n    i0.ɵɵtemplate(51, ErrorLogsComponent_div_36_span_51_Template, 2, 4, \"span\", 23);\n    i0.ɵɵtemplate(52, ErrorLogsComponent_div_36_span_52_Template, 2, 0, \"span\", 5);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(53, \"div\", 27)(54, \"div\", 30)(55, \"h6\");\n    i0.ɵɵtext(56, \"Zpr\\u00E1va chyby\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"div\", 31)(58, \"div\", 32)(59, \"p\", 33);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵtemplate(61, ErrorLogsComponent_div_36_div_61_Template, 8, 1, \"div\", 34);\n    i0.ɵɵtemplate(62, ErrorLogsComponent_div_36_div_62_Template, 8, 1, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedLog.id);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(16, 15, ctx_r4.selectedLog.timestamp, \"dd.MM.yyyy HH:mm:ss\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedLog.username || \"Syst\\u00E9m\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedLog.ipAddress);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassMapInterpolate1(\"badge rounded-pill \", ctx_r4.getSourceClass(ctx_r4.selectedLog.source), \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getLocalizedSource(ctx_r4.selectedLog.source), \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedLog.requestPath || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedLog.requestMethod || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedLog.statusCode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.selectedLog.statusCode);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedLog.message);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedLog.stackTrace);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedLog.additionalInfo);\n  }\n}\nexport let ErrorLogsComponent = /*#__PURE__*/(() => {\n  class ErrorLogsComponent {\n    constructor(logsService, modalService) {\n      this.logsService = logsService;\n      this.modalService = modalService;\n      this.logs = [];\n      this.currentFilter = {\n        maxResults: 100\n        // Pro logy chyb neomezujeme zdroj, chceme vidět chyby ze všech zdrojů\n      };\n\n      this.isLoading = false;\n      this.error = null;\n      this.selectedLog = null;\n    }\n    ngOnInit() {\n      // Načtení aktuálního stavu filtru z localStorage\n      this.loadCurrentFilterState();\n    }\n    ngAfterViewInit() {\n      // Načtení logů až po inicializaci komponenty\n      setTimeout(() => {\n        this.loadLogs();\n      }, 0);\n    }\n    /**\r\n     * Načtení aktuálního stavu filtru z localStorage\r\n     */\n    loadCurrentFilterState() {\n      try {\n        const filterKey = 'current_filter_error';\n        const filterJson = localStorage.getItem(filterKey);\n        if (filterJson) {\n          const savedFilter = JSON.parse(filterJson);\n          // Převod řetězcových hodnot na objekty Date\n          if (savedFilter.fromDate) {\n            savedFilter.fromDate = new Date(savedFilter.fromDate);\n          }\n          if (savedFilter.toDate) {\n            savedFilter.toDate = new Date(savedFilter.toDate);\n          }\n          // Nastavení hodnot filtru\n          this.currentFilter = {\n            ...this.currentFilter,\n            ...savedFilter\n          };\n          console.log('Načten filtr pro logy chyb:', this.currentFilter);\n        }\n      } catch (error) {\n        console.error('Chyba při načítání stavu filtru pro logy chyb', error);\n      }\n    }\n    /**\r\n     * Načtení logů podle aktuálního filtru\r\n     */\n    loadLogs() {\n      this.isLoading = true;\n      this.error = null;\n      this.logsService.getErrorLogs(this.currentFilter).subscribe({\n        next: logs => {\n          this.logs = logs;\n          this.isLoading = false;\n        },\n        error: err => {\n          console.error('Chyba při načítání logů chyb', err);\n          this.error = 'Nepodařilo se načíst logy chyb';\n          this.isLoading = false;\n        }\n      });\n    }\n    /**\r\n     * Aktualizace filtru a načtení logů\r\n     */\n    onFilterChange(filter) {\n      this.currentFilter = filter;\n      this.loadLogs();\n    }\n    /**\r\n     * Zobrazení detailu logu\r\n     */\n    showLogDetail(log) {\n      this.selectedLog = log;\n      // Otevření modálního okna\n      this.modalService.open('errorLogDetailModal');\n    }\n    /**\r\n     * Zavření detailu logu\r\n     */\n    closeLogDetail() {\n      this.selectedLog = null;\n      // Zavření modálního okna\n      this.modalService.close('errorLogDetailModal');\n    }\n    /**\r\n     * Získání lokalizovaného názvu zdroje logu\r\n     */\n    getLocalizedSource(source) {\n      switch (source) {\n        case 'DISAdmin':\n          return 'DISAdmin';\n        case 'DISApi':\n          return 'DIS API';\n        default:\n          return source;\n      }\n    }\n    /**\r\n     * Získání CSS třídy pro zdroj logu\r\n     */\n    getSourceClass(source) {\n      switch (source) {\n        case 'DISAdmin':\n          return 'bg-primary';\n        case 'DISApi':\n          return 'bg-info';\n        default:\n          return 'bg-secondary';\n      }\n    }\n    /**\r\n     * Získání CSS třídy pro stavový kód\r\n     */\n    getStatusCodeClass(statusCode) {\n      if (!statusCode) return 'bg-secondary';\n      if (statusCode >= 500) {\n        return 'bg-danger';\n      } else if (statusCode >= 400) {\n        return 'bg-warning';\n      } else if (statusCode >= 300) {\n        return 'bg-info';\n      } else if (statusCode >= 200) {\n        return 'bg-success';\n      } else {\n        return 'bg-secondary';\n      }\n    }\n    /**\r\n     * Získání lokalizovaného názvu úrovně logování\r\n     */\n    getLocalizedLogLevel(logLevel) {\n      return ApplicationLogLevelHelper.getLocalizedName(logLevel);\n    }\n    /**\r\n     * Získání CSS třídy pro úroveň logování\r\n     */\n    getLogLevelClass(logLevel) {\n      return ApplicationLogLevelHelper.getLogLevelClass(logLevel);\n    }\n    static {\n      this.ɵfac = function ErrorLogsComponent_Factory(t) {\n        return new (t || ErrorLogsComponent)(i0.ɵɵdirectiveInject(i1.LogsService), i0.ɵɵdirectiveInject(i2.ModalService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ErrorLogsComponent,\n        selectors: [[\"app-error-logs\"]],\n        decls: 40,\n        vars: 7,\n        consts: [[\"logType\", \"error\", 3, \"showLogLevelFilter\", \"showCategoryFilter\", \"filterChange\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\", \"mb-0\"], [1, \"table-header-override\", \"dark-header\"], [1, \"dark-header-row\"], [4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [\"id\", \"errorLogDetailModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"errorLogDetailModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"errorLogDetailModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\", 3, \"click\"], [\"class\", \"modal-body\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"colspan\", \"10\", 1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [\"colspan\", \"10\", 1, \"text-center\", \"text-danger\", \"py-4\"], [1, \"cursor-pointer\", 3, \"click\"], [1, \"text-truncate\", \"cursor-pointer\", 2, \"max-width\", \"250px\", 3, \"click\"], [1, \"text-truncate\", \"cursor-pointer\", 2, \"max-width\", \"150px\", 3, \"click\"], [3, \"class\", 4, \"ngIf\"], [\"title\", \"Zobrazit detail\", 1, \"btn\", \"btn-sm\", \"btn-outline-info\", 3, \"click\"], [1, \"bi\", \"bi-info-circle\"], [1, \"modal-body\"], [1, \"row\"], [1, \"col-md-6\", \"mb-3\"], [1, \"table\", \"table-sm\"], [1, \"col-12\", \"mb-3\"], [1, \"card\"], [1, \"card-body\"], [1, \"mb-0\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"mb-0\", \"stack-trace\"], [1, \"col-12\"]],\n        template: function ErrorLogsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"app-log-filter\", 0);\n            i0.ɵɵlistener(\"filterChange\", function ErrorLogsComponent_Template_app_log_filter_filterChange_0_listener($event) {\n              return ctx.onFilterChange($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(1, \"div\", 1)(2, \"table\", 2)(3, \"thead\", 3)(4, \"tr\", 4)(5, \"th\");\n            i0.ɵɵtext(6, \"\\u010Cas\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"th\");\n            i0.ɵɵtext(8, \"\\u00DArove\\u0148\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"th\");\n            i0.ɵɵtext(10, \"Zpr\\u00E1va\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"th\");\n            i0.ɵɵtext(12, \"Kategorie\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"th\");\n            i0.ɵɵtext(14, \"Zdroj\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"th\");\n            i0.ɵɵtext(16, \"Cesta\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"th\");\n            i0.ɵɵtext(18, \"Metoda\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"th\");\n            i0.ɵɵtext(20, \"K\\u00F3d\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"th\");\n            i0.ɵɵtext(22, \"U\\u017Eivatel\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(23, \"th\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"tbody\");\n            i0.ɵɵtemplate(25, ErrorLogsComponent_tr_25_Template, 5, 0, \"tr\", 5);\n            i0.ɵɵtemplate(26, ErrorLogsComponent_tr_26_Template, 3, 1, \"tr\", 5);\n            i0.ɵɵtemplate(27, ErrorLogsComponent_tr_27_Template, 3, 0, \"tr\", 5);\n            i0.ɵɵtemplate(28, ErrorLogsComponent_tr_28_Template, 26, 19, \"tr\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(29, \"div\", 7)(30, \"div\", 8)(31, \"div\", 9)(32, \"div\", 10)(33, \"h5\", 11);\n            i0.ɵɵtext(34, \"Detail logu chyby\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function ErrorLogsComponent_Template_button_click_35_listener() {\n              return ctx.closeLogDetail();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(36, ErrorLogsComponent_div_36_Template, 63, 18, \"div\", 13);\n            i0.ɵɵelementStart(37, \"div\", 14)(38, \"button\", 15);\n            i0.ɵɵlistener(\"click\", function ErrorLogsComponent_Template_button_click_38_listener() {\n              return ctx.closeLogDetail();\n            });\n            i0.ɵɵtext(39, \"Zav\\u0159\\u00EDt\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"showLogLevelFilter\", true)(\"showCategoryFilter\", true);\n            i0.ɵɵadvance(25);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.logs.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngForOf\", ctx.logs);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedLog);\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i4.LogFilterComponent, i3.DatePipe],\n        styles: [\".cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{font-weight:500}.table-responsive[_ngcontent-%COMP%]{border-radius:6px!important;overflow:hidden!important;box-shadow:0 2px 4px #0000001a!important}.table[_ngcontent-%COMP%]{margin:0!important;border-collapse:collapse!important}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child, .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child{padding-left:1rem!important}.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child{padding-left:1rem!important;border-left:none!important}.badge[_ngcontent-%COMP%]{font-weight:500;padding:.35em .65em}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{border-left:none!important}.table-hover[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#007bff13!important}body.dark-theme[_ngcontent-%COMP%]   .table-hover[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#1a2530!important}.stack-trace[_ngcontent-%COMP%]{white-space:pre-wrap;font-size:.85rem;max-height:300px;overflow-y:auto}.bg-success[_ngcontent-%COMP%]{background-color:#28a745!important}.bg-primary[_ngcontent-%COMP%]{background-color:#007bff!important}.bg-info[_ngcontent-%COMP%]{background-color:#17a2b8!important}.bg-warning[_ngcontent-%COMP%]{background-color:#ffc107!important;color:#212529}.bg-danger[_ngcontent-%COMP%]{background-color:#dc3545!important}.bg-secondary[_ngcontent-%COMP%]{background-color:#6c757d!important}@media (prefers-color-scheme: dark){.table[_ngcontent-%COMP%]{color:var(--bs-light)}.bg-warning[_ngcontent-%COMP%]{color:#212529}pre[_ngcontent-%COMP%]{color:var(--bs-light)}}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th:first-child{border-top-left-radius:6px!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th:last-child{border-top-right-radius:6px!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead{background-color:var(--bs-table-bg, #111921)!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr{background-color:var(--bs-table-bg, #111921)!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th{background-color:var(--bs-table-bg, #111921)!important;color:#fff!important;border-color:var(--bs-table-bg, #111921)!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table{min-width:100%}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>tbody>tr>td>.badge{padding:.35em .65em;font-size:.875rem}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>tbody>tr>td:nth-child(5), [_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th:nth-child(5){max-width:250px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th{padding:.5rem;font-size:1rem}\"]\n      });\n    }\n  }\n  return ErrorLogsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}