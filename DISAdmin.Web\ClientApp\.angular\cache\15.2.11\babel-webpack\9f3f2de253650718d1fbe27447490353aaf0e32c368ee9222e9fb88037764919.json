{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"../shared/tab-navigation/tab-navigation.component\";\nimport * as i4 from \"./activity-logs/activity-logs.component\";\nimport * as i5 from \"./error-logs/error-logs.component\";\nimport * as i6 from \"./api-logs/api-logs.component\";\nfunction LogsComponent_app_activity_logs_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-activity-logs\");\n  }\n}\nfunction LogsComponent_app_error_logs_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-error-logs\");\n  }\n}\nfunction LogsComponent_app_api_logs_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-api-logs\");\n  }\n}\nexport class LogsComponent {\n  constructor(router, route) {\n    this.router = router;\n    this.route = route;\n    this.activeTab = 'activity';\n    // Definice záložek\n    this.tabs = [{\n      id: 'activity',\n      label: 'Auditní logy',\n      icon: 'activity'\n    }, {\n      id: 'error',\n      label: 'Logy aplikace',\n      icon: 'exclamation-triangle'\n    }, {\n      id: 'api',\n      label: 'Logy DIS API',\n      icon: 'hdd-network'\n    }];\n    // Ukládání stavu filtrů pro jednotlivé záložky\n    this.activityFilterState = null;\n    this.errorFilterState = null;\n    this.apiFilterState = null;\n  }\n  ngOnInit() {\n    // Získání aktivní záložky z URL parametru nebo z localStorage\n    this.route.queryParams.subscribe(params => {\n      if (params['tab']) {\n        // Priorita: URL parametr\n        this.activeTab = params['tab'];\n        // Uložení do localStorage pro budoucí použití\n        this.saveActiveTabToLocalStorage(params['tab']);\n      } else {\n        // Pokud není v URL, zkusíme načíst z localStorage\n        const lastActiveTab = this.loadActiveTabFromLocalStorage();\n        if (lastActiveTab) {\n          this.activeTab = lastActiveTab;\n          // Aktualizace URL parametru\n          this.router.navigate([], {\n            relativeTo: this.route,\n            queryParams: {\n              tab: lastActiveTab\n            },\n            queryParamsHandling: 'merge'\n          });\n        }\n      }\n    });\n  }\n  /**\r\n   * Změna aktivní záložky\r\n   */\n  changeTab(tab) {\n    // Uložení stavu aktuálního filtru před přepnutím záložky\n    this.saveCurrentFilterState();\n    // Převod parametru na string, pokud není\n    const tabId = typeof tab === 'string' ? tab : String(tab);\n    this.activeTab = tabId;\n    // Uložení aktivní záložky do localStorage\n    this.saveActiveTabToLocalStorage(tabId);\n    // Aktualizace URL parametru\n    this.router.navigate([], {\n      relativeTo: this.route,\n      queryParams: {\n        tab: tabId\n      },\n      queryParamsHandling: 'merge'\n    });\n  }\n  /**\r\n   * Uložení stavu aktuálního filtru\r\n   */\n  saveCurrentFilterState() {\n    // Uložení stavu filtru podle aktuální záložky\n    switch (this.activeTab) {\n      case 'activity':\n        this.activityFilterState = this.getFilterState('activity');\n        break;\n      case 'error':\n        this.errorFilterState = this.getFilterState('error');\n        break;\n      case 'api':\n        this.apiFilterState = this.getFilterState('api');\n        break;\n    }\n  }\n  /**\r\n   * Získání stavu filtru pro daný typ logu\r\n   */\n  getFilterState(logType) {\n    // Získání stavu filtru z localStorage\n    try {\n      const filterKey = `current_filter_${logType}`;\n      const filterJson = localStorage.getItem(filterKey);\n      if (filterJson) {\n        return JSON.parse(filterJson);\n      }\n    } catch (error) {\n      console.error(`Chyba při načítání stavu filtru pro ${logType}`, error);\n    }\n    return null;\n  }\n  /**\r\n   * Uložení aktivní záložky do localStorage\r\n   */\n  saveActiveTabToLocalStorage(tabId) {\n    try {\n      localStorage.setItem('logs_active_tab', tabId);\n      console.log(`Uložena aktivní záložka do localStorage: ${tabId}`);\n    } catch (error) {\n      console.error('Chyba při ukládání aktivní záložky do localStorage:', error);\n    }\n  }\n  /**\r\n   * Načtení aktivní záložky z localStorage\r\n   */\n  loadActiveTabFromLocalStorage() {\n    try {\n      const activeTab = localStorage.getItem('logs_active_tab');\n      console.log(`Načtena aktivní záložka z localStorage: ${activeTab}`);\n      return activeTab;\n    } catch (error) {\n      console.error('Chyba při načítání aktivní záložky z localStorage:', error);\n      return null;\n    }\n  }\n  static {\n    this.ɵfac = function LogsComponent_Factory(t) {\n      return new (t || LogsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LogsComponent,\n      selectors: [[\"app-logs\"]],\n      decls: 9,\n      vars: 6,\n      consts: [[1, \"container\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [3, \"tabs\", \"activeTabId\", \"tabChange\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"]],\n      template: function LogsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3, \"Logy syst\\u00E9mu\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"app-tab-navigation\", 2);\n          i0.ɵɵlistener(\"tabChange\", function LogsComponent_Template_app_tab_navigation_tabChange_4_listener($event) {\n            return ctx.changeTab($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3);\n          i0.ɵɵtemplate(6, LogsComponent_app_activity_logs_6_Template, 1, 0, \"app-activity-logs\", 4);\n          i0.ɵɵtemplate(7, LogsComponent_app_error_logs_7_Template, 1, 0, \"app-error-logs\", 4);\n          i0.ɵɵtemplate(8, LogsComponent_app_api_logs_8_Template, 1, 0, \"app-api-logs\", 4);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"tabs\", ctx.tabs)(\"activeTabId\", ctx.activeTab);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngSwitch\", ctx.activeTab);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngSwitchCase\", \"activity\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngSwitchCase\", \"error\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngSwitchCase\", \"api\");\n        }\n      },\n      dependencies: [i2.NgSwitch, i2.NgSwitchCase, i3.TabNavigationComponent, i4.ActivityLogsComponent, i5.ErrorLogsComponent, i6.ApiLogsComponent],\n      styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbG9ncy9sb2dzLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsMEVBQTBFIiwic291cmNlc0NvbnRlbnQiOlsiLyogU3R5bHkgcHJvIHrDg8KhbG/DhcK+a3kgYnlseSBww4XCmWVzdW51dHkgZG8gc2TDg8KtbGVuw4PCqSBrb21wb25lbnR5IHRhYi1uYXZpZ2F0aW9uICovXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;;;;IAcIA,oCAAkE;;;;;IAClEA,iCAAyD;;;;;IACzDA,+BAAmD;;;ACHvD,OAAM,MAAOC,aAAa;EAexBC,YACUC,MAAc,EACdC,KAAqB;IADrB,WAAM,GAAND,MAAM;IACN,UAAK,GAALC,KAAK;IAhBf,cAAS,GAAW,UAAU;IAE9B;IACA,SAAI,GAAc,CAChB;MAAEC,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE,cAAc;MAAEC,IAAI,EAAE;IAAU,CAAE,EAC3D;MAAEF,EAAE,EAAE,OAAO;MAAEC,KAAK,EAAE,eAAe;MAAEC,IAAI,EAAE;IAAsB,CAAE,EACrE;MAAEF,EAAE,EAAE,KAAK;MAAEC,KAAK,EAAE,cAAc;MAAEC,IAAI,EAAE;IAAa,CAAE,CAC1D;IAED;IACQ,wBAAmB,GAAQ,IAAI;IAC/B,qBAAgB,GAAQ,IAAI;IAC5B,mBAAc,GAAQ,IAAI;EAK9B;EAEJC,QAAQ;IACN;IACA,IAAI,CAACJ,KAAK,CAACK,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxC,IAAIA,MAAM,CAAC,KAAK,CAAC,EAAE;QACjB;QACA,IAAI,CAACC,SAAS,GAAGD,MAAM,CAAC,KAAK,CAAC;QAC9B;QACA,IAAI,CAACE,2BAA2B,CAACF,MAAM,CAAC,KAAK,CAAC,CAAC;OAChD,MAAM;QACL;QACA,MAAMG,aAAa,GAAG,IAAI,CAACC,6BAA6B,EAAE;QAC1D,IAAID,aAAa,EAAE;UACjB,IAAI,CAACF,SAAS,GAAGE,aAAa;UAC9B;UACA,IAAI,CAACX,MAAM,CAACa,QAAQ,CAAC,EAAE,EAAE;YACvBC,UAAU,EAAE,IAAI,CAACb,KAAK;YACtBK,WAAW,EAAE;cAAES,GAAG,EAAEJ;YAAa,CAAE;YACnCK,mBAAmB,EAAE;WACtB,CAAC;;;IAGR,CAAC,CAAC;EACJ;EAEA;;;EAGAC,SAAS,CAACF,GAAQ;IAChB;IACA,IAAI,CAACG,sBAAsB,EAAE;IAE7B;IACA,MAAMC,KAAK,GAAG,OAAOJ,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGK,MAAM,CAACL,GAAG,CAAC;IACzD,IAAI,CAACN,SAAS,GAAGU,KAAK;IAEtB;IACA,IAAI,CAACT,2BAA2B,CAACS,KAAK,CAAC;IAEvC;IACA,IAAI,CAACnB,MAAM,CAACa,QAAQ,CAAC,EAAE,EAAE;MACvBC,UAAU,EAAE,IAAI,CAACb,KAAK;MACtBK,WAAW,EAAE;QAAES,GAAG,EAAEI;MAAK,CAAE;MAC3BH,mBAAmB,EAAE;KACtB,CAAC;EACJ;EAEA;;;EAGQE,sBAAsB;IAC5B;IACA,QAAQ,IAAI,CAACT,SAAS;MACpB,KAAK,UAAU;QACb,IAAI,CAACY,mBAAmB,GAAG,IAAI,CAACC,cAAc,CAAC,UAAU,CAAC;QAC1D;MACF,KAAK,OAAO;QACV,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACD,cAAc,CAAC,OAAO,CAAC;QACpD;MACF,KAAK,KAAK;QACR,IAAI,CAACE,cAAc,GAAG,IAAI,CAACF,cAAc,CAAC,KAAK,CAAC;QAChD;IAAM;EAEZ;EAEA;;;EAGQA,cAAc,CAACG,OAAe;IACpC;IACA,IAAI;MACF,MAAMC,SAAS,GAAG,kBAAkBD,OAAO,EAAE;MAC7C,MAAME,UAAU,GAAGC,YAAY,CAACC,OAAO,CAACH,SAAS,CAAC;MAClD,IAAIC,UAAU,EAAE;QACd,OAAOG,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;;KAEhC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuCP,OAAO,EAAE,EAAEO,KAAK,CAAC;;IAExE,OAAO,IAAI;EACb;EAEA;;;EAGQtB,2BAA2B,CAACS,KAAa;IAC/C,IAAI;MACFS,YAAY,CAACM,OAAO,CAAC,iBAAiB,EAAEf,KAAK,CAAC;MAC9Cc,OAAO,CAACE,GAAG,CAAC,4CAA4ChB,KAAK,EAAE,CAAC;KACjE,CAAC,OAAOa,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;;EAE/E;EAEA;;;EAGQpB,6BAA6B;IACnC,IAAI;MACF,MAAMH,SAAS,GAAGmB,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC;MACzDI,OAAO,CAACE,GAAG,CAAC,2CAA2C1B,SAAS,EAAE,CAAC;MACnE,OAAOA,SAAS;KACjB,CAAC,OAAOuB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;MAC1E,OAAO,IAAI;;EAEf;;;uBA7HWlC,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAsC;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UDb1B3C,8BAAuB;UAEfA,iCAAY;UAAAA,iBAAK;UAIvBA,6CAGkC;UAAhCA;YAAA,OAAa4C,qBAAiB;UAAA,EAAC;UACjC5C,iBAAqB;UAGrBA,8BAA4B;UAC1BA,0FAAkE;UAClEA,oFAAyD;UACzDA,gFAAmD;UACrDA,iBAAM;;;UAVJA,eAAa;UAAbA,+BAAa;UAMVA,eAAsB;UAAtBA,wCAAsB;UACLA,eAAwB;UAAxBA,yCAAwB;UAC3BA,eAAqB;UAArBA,sCAAqB;UACvBA,eAAmB;UAAnBA,oCAAmB", "names": ["i0", "LogsComponent", "constructor", "router", "route", "id", "label", "icon", "ngOnInit", "queryParams", "subscribe", "params", "activeTab", "saveActiveTabToLocalStorage", "lastActiveTab", "loadActiveTabFromLocalStorage", "navigate", "relativeTo", "tab", "queryParamsHandling", "changeTab", "saveCurrentFilterState", "tabId", "String", "activityFilterState", "getFilterState", "errorFilterState", "apiFilterState", "logType", "<PERSON><PERSON><PERSON>", "filter<PERSON>son", "localStorage", "getItem", "JSON", "parse", "error", "console", "setItem", "log", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\logs\\logs.component.html", "C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\logs\\logs.component.ts"], "sourcesContent": ["<div class=\"container\">\n  <div class=\"d-flex justify-content-between align-items-center mb-4\">\n    <h2><PERSON><PERSON> systému</h2>\n  </div>\n\n  <!-- Z<PERSON>ložky pro přepínání mezi typy logů -->\n  <app-tab-navigation\n    [tabs]=\"tabs\"\n    [activeTabId]=\"activeTab\"\n    (tabChange)=\"changeTab($event)\">\n  </app-tab-navigation>\n\n  <!-- Obsah podle vybra<PERSON> -->\n  <div [ngSwitch]=\"activeTab\">\n    <app-activity-logs *ngSwitchCase=\"'activity'\"></app-activity-logs>\n    <app-error-logs *ngSwitchCase=\"'error'\"></app-error-logs>\n    <app-api-logs *ngSwitchCase=\"'api'\"></app-api-logs>\n  </div>\n</div>\n", "import { Component, OnInit, ViewChild } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { LogFilterComponent } from './log-filter/log-filter.component';\nimport { ActivityLogsComponent } from './activity-logs/activity-logs.component';\nimport { ErrorLogsComponent } from './error-logs/error-logs.component';\nimport { ApiLogsComponent } from './api-logs/api-logs.component';\nimport { TabItem } from '../shared/tab-navigation/tab-navigation.component';\n\n@Component({\n  selector: 'app-logs',\n  templateUrl: './logs.component.html',\n  styleUrls: ['./logs.component.css']\n})\nexport class LogsComponent implements OnInit {\n  activeTab: string = 'activity';\n\n  // Definice záložek\n  tabs: TabItem[] = [\n    { id: 'activity', label: 'Auditní logy', icon: 'activity' },\n    { id: 'error', label: 'Logy aplikace', icon: 'exclamation-triangle' },\n    { id: 'api', label: 'Logy DIS API', icon: 'hdd-network' }\n  ];\n\n  // Ukládání stavu filtrů pro jednotlivé záložky\n  private activityFilterState: any = null;\n  private errorFilterState: any = null;\n  private apiFilterState: any = null;\n\n  constructor(\n    private router: Router,\n    private route: ActivatedRoute\n  ) { }\n\n  ngOnInit(): void {\n    // Získání aktivní záložky z URL parametru nebo z localStorage\n    this.route.queryParams.subscribe(params => {\n      if (params['tab']) {\n        // Priorita: URL parametr\n        this.activeTab = params['tab'];\n        // Uložení do localStorage pro budoucí použití\n        this.saveActiveTabToLocalStorage(params['tab']);\n      } else {\n        // Pokud není v URL, zkusíme načíst z localStorage\n        const lastActiveTab = this.loadActiveTabFromLocalStorage();\n        if (lastActiveTab) {\n          this.activeTab = lastActiveTab;\n          // Aktualizace URL parametru\n          this.router.navigate([], {\n            relativeTo: this.route,\n            queryParams: { tab: lastActiveTab },\n            queryParamsHandling: 'merge'\n          });\n        }\n      }\n    });\n  }\n\n  /**\n   * Změna aktivní záložky\n   */\n  changeTab(tab: any): void {\n    // Uložení stavu aktuálního filtru před přepnutím záložky\n    this.saveCurrentFilterState();\n\n    // Převod parametru na string, pokud není\n    const tabId = typeof tab === 'string' ? tab : String(tab);\n    this.activeTab = tabId;\n\n    // Uložení aktivní záložky do localStorage\n    this.saveActiveTabToLocalStorage(tabId);\n\n    // Aktualizace URL parametru\n    this.router.navigate([], {\n      relativeTo: this.route,\n      queryParams: { tab: tabId },\n      queryParamsHandling: 'merge'\n    });\n  }\n\n  /**\n   * Uložení stavu aktuálního filtru\n   */\n  private saveCurrentFilterState(): void {\n    // Uložení stavu filtru podle aktuální záložky\n    switch (this.activeTab) {\n      case 'activity':\n        this.activityFilterState = this.getFilterState('activity');\n        break;\n      case 'error':\n        this.errorFilterState = this.getFilterState('error');\n        break;\n      case 'api':\n        this.apiFilterState = this.getFilterState('api');\n        break;\n    }\n  }\n\n  /**\n   * Získání stavu filtru pro daný typ logu\n   */\n  private getFilterState(logType: string): any {\n    // Získání stavu filtru z localStorage\n    try {\n      const filterKey = `current_filter_${logType}`;\n      const filterJson = localStorage.getItem(filterKey);\n      if (filterJson) {\n        return JSON.parse(filterJson);\n      }\n    } catch (error) {\n      console.error(`Chyba při načítání stavu filtru pro ${logType}`, error);\n    }\n    return null;\n  }\n\n  /**\n   * Uložení aktivní záložky do localStorage\n   */\n  private saveActiveTabToLocalStorage(tabId: string): void {\n    try {\n      localStorage.setItem('logs_active_tab', tabId);\n      console.log(`Uložena aktivní záložka do localStorage: ${tabId}`);\n    } catch (error) {\n      console.error('Chyba při ukládání aktivní záložky do localStorage:', error);\n    }\n  }\n\n  /**\n   * Načtení aktivní záložky z localStorage\n   */\n  private loadActiveTabFromLocalStorage(): string | null {\n    try {\n      const activeTab = localStorage.getItem('logs_active_tab');\n      console.log(`Načtena aktivní záložka z localStorage: ${activeTab}`);\n      return activeTab;\n    } catch (error) {\n      console.error('Chyba při načítání aktivní záložky z localStorage:', error);\n      return null;\n    }\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}