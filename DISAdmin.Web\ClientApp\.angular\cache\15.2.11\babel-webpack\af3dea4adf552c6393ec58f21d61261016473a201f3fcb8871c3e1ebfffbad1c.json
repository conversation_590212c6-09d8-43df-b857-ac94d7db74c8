{"ast": null, "code": "import { ApplicationLogLevelHelper } from '../../models/logs.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/logs.service\";\nimport * as i2 from \"../../services/modal.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"../log-filter/log-filter.component\";\nfunction ErrorLogsComponent_tr_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 16)(2, \"div\", 17)(3, \"span\", 18);\n    i0.ɵɵtext(4, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction ErrorLogsComponent_tr_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 19);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction ErrorLogsComponent_tr_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 16);\n    i0.ɵɵtext(2, \" Nebyly nalezeny \\u017E\\u00E1dn\\u00E9 logy aplikace \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ErrorLogsComponent_tr_28_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const log_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"badge rounded-pill \", ctx_r7.getStatusCodeClass(log_r5.statusCode), \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", log_r5.statusCode, \" \");\n  }\n}\nfunction ErrorLogsComponent_tr_28_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ErrorLogsComponent_tr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 20);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_td_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.showLogDetail(log_r5));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\", 20);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_td_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.showLogDetail(log_r5));\n    });\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 21);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_td_click_7_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.showLogDetail(log_r5));\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 22);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_td_click_9_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.showLogDetail(log_r5));\n    });\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 20);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_td_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.showLogDetail(log_r5));\n    });\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"td\", 22);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_td_click_14_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.showLogDetail(log_r5));\n    });\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 20);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_td_click_16_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.showLogDetail(log_r5));\n    });\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\", 20);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_td_click_18_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.showLogDetail(log_r5));\n    });\n    i0.ɵɵtemplate(19, ErrorLogsComponent_tr_28_span_19_Template, 2, 4, \"span\", 23);\n    i0.ɵɵtemplate(20, ErrorLogsComponent_tr_28_span_20_Template, 2, 0, \"span\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 20);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_td_click_21_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.showLogDetail(log_r5));\n    });\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\")(24, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function ErrorLogsComponent_tr_28_Template_button_click_24_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const log_r5 = restoredCtx.$implicit;\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.showLogDetail(log_r5));\n    });\n    i0.ɵɵelement(25, \"i\", 25);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const log_r5 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(3, 16, log_r5.timestamp, \"dd.MM.yyyy HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMapInterpolate1(\"badge rounded-pill \", ctx_r3.getLogLevelClass(log_r5.logLevel), \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getLocalizedLogLevel(log_r5.logLevel), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(log_r5.message);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(log_r5.category || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMapInterpolate1(\"badge rounded-pill \", ctx_r3.getSourceClass(log_r5.source), \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getLocalizedSource(log_r5.source), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(log_r5.requestPath || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(log_r5.requestMethod || \"-\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", log_r5.statusCode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !log_r5.statusCode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(log_r5.username || \"Syst\\u00E9m\");\n  }\n}\nfunction ErrorLogsComponent_div_36_span_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMapInterpolate1(\"badge rounded-pill \", ctx_r21.getStatusCodeClass(ctx_r21.selectedLog.statusCode), \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r21.selectedLog.statusCode, \" \");\n  }\n}\nfunction ErrorLogsComponent_div_36_span_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ErrorLogsComponent_div_36_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 30)(2, \"h6\");\n    i0.ɵɵtext(3, \"Stack Trace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 31)(5, \"div\", 32)(6, \"pre\", 35);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r23.selectedLog.stackTrace);\n  }\n}\nfunction ErrorLogsComponent_div_36_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 36)(2, \"h6\");\n    i0.ɵɵtext(3, \"Dodate\\u010Dn\\u00E9 informace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 31)(5, \"div\", 32)(6, \"pre\", 33);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r24.selectedLog.additionalInfo);\n  }\n}\nfunction ErrorLogsComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28)(3, \"h6\");\n    i0.ɵɵtext(4, \"Z\\u00E1kladn\\u00ED informace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"table\", 29)(6, \"tr\")(7, \"th\");\n    i0.ɵɵtext(8, \"ID:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"tr\")(12, \"th\");\n    i0.ɵɵtext(13, \"\\u010Cas:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"tr\")(18, \"th\");\n    i0.ɵɵtext(19, \"U\\u017Eivatel:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"tr\")(23, \"th\");\n    i0.ɵɵtext(24, \"IP adresa:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"td\");\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"tr\")(28, \"th\");\n    i0.ɵɵtext(29, \"Zdroj:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"td\")(31, \"span\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(33, \"div\", 28)(34, \"h6\");\n    i0.ɵɵtext(35, \"Detaily po\\u017Eadavku\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"table\", 29)(37, \"tr\")(38, \"th\");\n    i0.ɵɵtext(39, \"Cesta:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"td\");\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"tr\")(43, \"th\");\n    i0.ɵɵtext(44, \"Metoda:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"td\");\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"tr\")(48, \"th\");\n    i0.ɵɵtext(49, \"Stavov\\u00FD k\\u00F3d:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"td\");\n    i0.ɵɵtemplate(51, ErrorLogsComponent_div_36_span_51_Template, 2, 4, \"span\", 23);\n    i0.ɵɵtemplate(52, ErrorLogsComponent_div_36_span_52_Template, 2, 0, \"span\", 5);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(53, \"div\", 27)(54, \"div\", 30)(55, \"h6\");\n    i0.ɵɵtext(56, \"Zpr\\u00E1va chyby\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"div\", 31)(58, \"div\", 32)(59, \"p\", 33);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵtemplate(61, ErrorLogsComponent_div_36_div_61_Template, 8, 1, \"div\", 34);\n    i0.ɵɵtemplate(62, ErrorLogsComponent_div_36_div_62_Template, 8, 1, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedLog.id);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(16, 15, ctx_r4.selectedLog.timestamp, \"dd.MM.yyyy HH:mm:ss\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedLog.username || \"Syst\\u00E9m\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedLog.ipAddress);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassMapInterpolate1(\"badge rounded-pill \", ctx_r4.getSourceClass(ctx_r4.selectedLog.source), \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getLocalizedSource(ctx_r4.selectedLog.source), \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedLog.requestPath || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedLog.requestMethod || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedLog.statusCode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.selectedLog.statusCode);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedLog.message);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedLog.stackTrace);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedLog.additionalInfo);\n  }\n}\nexport class ErrorLogsComponent {\n  constructor(logsService, modalService) {\n    this.logsService = logsService;\n    this.modalService = modalService;\n    this.logs = [];\n    this.currentFilter = {\n      maxResults: 100\n      // Pro logy chyb neomezujeme zdroj, chceme vidět chyby ze všech zdrojů\n    };\n\n    this.isLoading = false;\n    this.error = null;\n    this.selectedLog = null;\n  }\n  ngOnInit() {\n    // Načtení aktuálního stavu filtru z localStorage\n    this.loadCurrentFilterState();\n  }\n  ngAfterViewInit() {\n    // Načtení logů až po inicializaci komponenty\n    setTimeout(() => {\n      this.loadLogs();\n    }, 0);\n  }\n  /**\r\n   * Načtení aktuálního stavu filtru z localStorage\r\n   */\n  loadCurrentFilterState() {\n    try {\n      const filterKey = 'current_filter_error';\n      const filterJson = localStorage.getItem(filterKey);\n      if (filterJson) {\n        const savedFilter = JSON.parse(filterJson);\n        // Převod řetězcových hodnot na objekty Date\n        if (savedFilter.fromDate) {\n          savedFilter.fromDate = new Date(savedFilter.fromDate);\n        }\n        if (savedFilter.toDate) {\n          savedFilter.toDate = new Date(savedFilter.toDate);\n        }\n        // Nastavení hodnot filtru\n        this.currentFilter = {\n          ...this.currentFilter,\n          ...savedFilter\n        };\n        console.log('Načten filtr pro logy chyb:', this.currentFilter);\n      }\n    } catch (error) {\n      console.error('Chyba při načítání stavu filtru pro logy chyb', error);\n    }\n  }\n  /**\r\n   * Načtení logů podle aktuálního filtru\r\n   */\n  loadLogs() {\n    this.isLoading = true;\n    this.error = null;\n    this.logsService.getErrorLogs(this.currentFilter).subscribe({\n      next: logs => {\n        this.logs = logs;\n        this.isLoading = false;\n      },\n      error: err => {\n        console.error('Chyba při načítání logů chyb', err);\n        this.error = 'Nepodařilo se načíst logy chyb';\n        this.isLoading = false;\n      }\n    });\n  }\n  /**\r\n   * Aktualizace filtru a načtení logů\r\n   */\n  onFilterChange(filter) {\n    this.currentFilter = filter;\n    this.loadLogs();\n  }\n  /**\r\n   * Zobrazení detailu logu\r\n   */\n  showLogDetail(log) {\n    this.selectedLog = log;\n    // Otevření modálního okna\n    this.modalService.open('errorLogDetailModal');\n  }\n  /**\r\n   * Zavření detailu logu\r\n   */\n  closeLogDetail() {\n    this.selectedLog = null;\n    // Zavření modálního okna\n    this.modalService.close('errorLogDetailModal');\n  }\n  /**\r\n   * Získání lokalizovaného názvu zdroje logu\r\n   */\n  getLocalizedSource(source) {\n    switch (source) {\n      case 'DISAdmin':\n        return 'DISAdmin';\n      case 'DISApi':\n        return 'DIS API';\n      default:\n        return source;\n    }\n  }\n  /**\r\n   * Získání CSS třídy pro zdroj logu\r\n   */\n  getSourceClass(source) {\n    switch (source) {\n      case 'DISAdmin':\n        return 'bg-primary';\n      case 'DISApi':\n        return 'bg-info';\n      default:\n        return 'bg-secondary';\n    }\n  }\n  /**\r\n   * Získání CSS třídy pro stavový kód\r\n   */\n  getStatusCodeClass(statusCode) {\n    if (!statusCode) return 'bg-secondary';\n    if (statusCode >= 500) {\n      return 'bg-danger';\n    } else if (statusCode >= 400) {\n      return 'bg-warning';\n    } else if (statusCode >= 300) {\n      return 'bg-info';\n    } else if (statusCode >= 200) {\n      return 'bg-success';\n    } else {\n      return 'bg-secondary';\n    }\n  }\n  /**\r\n   * Získání lokalizovaného názvu úrovně logování\r\n   */\n  getLocalizedLogLevel(logLevel) {\n    return ApplicationLogLevelHelper.getLocalizedName(logLevel);\n  }\n  /**\r\n   * Získání CSS třídy pro úroveň logování\r\n   */\n  getLogLevelClass(logLevel) {\n    return ApplicationLogLevelHelper.getLogLevelClass(logLevel);\n  }\n  static {\n    this.ɵfac = function ErrorLogsComponent_Factory(t) {\n      return new (t || ErrorLogsComponent)(i0.ɵɵdirectiveInject(i1.LogsService), i0.ɵɵdirectiveInject(i2.ModalService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ErrorLogsComponent,\n      selectors: [[\"app-error-logs\"]],\n      decls: 40,\n      vars: 7,\n      consts: [[\"logType\", \"error\", 3, \"showLogLevelFilter\", \"showCategoryFilter\", \"filterChange\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\", \"mb-0\"], [1, \"table-header-override\", \"dark-header\"], [1, \"dark-header-row\"], [4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [\"id\", \"errorLogDetailModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"errorLogDetailModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"errorLogDetailModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\", 3, \"click\"], [\"class\", \"modal-body\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"colspan\", \"10\", 1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [\"colspan\", \"10\", 1, \"text-center\", \"text-danger\", \"py-4\"], [1, \"cursor-pointer\", 3, \"click\"], [1, \"text-truncate\", \"cursor-pointer\", 2, \"max-width\", \"250px\", 3, \"click\"], [1, \"text-truncate\", \"cursor-pointer\", 2, \"max-width\", \"150px\", 3, \"click\"], [3, \"class\", 4, \"ngIf\"], [\"title\", \"Zobrazit detail\", 1, \"btn\", \"btn-sm\", \"btn-outline-info\", 3, \"click\"], [1, \"bi\", \"bi-info-circle\"], [1, \"modal-body\"], [1, \"row\"], [1, \"col-md-6\", \"mb-3\"], [1, \"table\", \"table-sm\"], [1, \"col-12\", \"mb-3\"], [1, \"card\"], [1, \"card-body\"], [1, \"mb-0\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"mb-0\", \"stack-trace\"], [1, \"col-12\"]],\n      template: function ErrorLogsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"app-log-filter\", 0);\n          i0.ɵɵlistener(\"filterChange\", function ErrorLogsComponent_Template_app_log_filter_filterChange_0_listener($event) {\n            return ctx.onFilterChange($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"table\", 2)(3, \"thead\", 3)(4, \"tr\", 4)(5, \"th\");\n          i0.ɵɵtext(6, \"\\u010Cas\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"th\");\n          i0.ɵɵtext(8, \"\\u00DArove\\u0148\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"th\");\n          i0.ɵɵtext(10, \"Zpr\\u00E1va\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"th\");\n          i0.ɵɵtext(12, \"Kategorie\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"th\");\n          i0.ɵɵtext(14, \"Zdroj\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"th\");\n          i0.ɵɵtext(16, \"Cesta\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"th\");\n          i0.ɵɵtext(18, \"Metoda\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"th\");\n          i0.ɵɵtext(20, \"K\\u00F3d\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"th\");\n          i0.ɵɵtext(22, \"U\\u017Eivatel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(23, \"th\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"tbody\");\n          i0.ɵɵtemplate(25, ErrorLogsComponent_tr_25_Template, 5, 0, \"tr\", 5);\n          i0.ɵɵtemplate(26, ErrorLogsComponent_tr_26_Template, 3, 1, \"tr\", 5);\n          i0.ɵɵtemplate(27, ErrorLogsComponent_tr_27_Template, 3, 0, \"tr\", 5);\n          i0.ɵɵtemplate(28, ErrorLogsComponent_tr_28_Template, 26, 19, \"tr\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 7)(30, \"div\", 8)(31, \"div\", 9)(32, \"div\", 10)(33, \"h5\", 11);\n          i0.ɵɵtext(34, \"Detail logu chyby\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function ErrorLogsComponent_Template_button_click_35_listener() {\n            return ctx.closeLogDetail();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(36, ErrorLogsComponent_div_36_Template, 63, 18, \"div\", 13);\n          i0.ɵɵelementStart(37, \"div\", 14)(38, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function ErrorLogsComponent_Template_button_click_38_listener() {\n            return ctx.closeLogDetail();\n          });\n          i0.ɵɵtext(39, \"Zav\\u0159\\u00EDt\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"showLogLevelFilter\", true)(\"showCategoryFilter\", true);\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.logs.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.logs);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedLog);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.LogFilterComponent, i3.DatePipe],\n      styles: [\".cursor-pointer[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  border-radius: 6px !important;\\n  overflow: hidden !important;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin: 0 !important;\\n  border-collapse: collapse !important;\\n}\\n\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child, .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child {\\n  padding-left: 1rem !important;\\n}\\n\\n\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child {\\n  padding-left: 1rem !important;\\n  border-left: none !important;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  padding: 0.35em 0.65em;\\n}\\n\\n\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-left: none !important;\\n}\\n\\n\\n.table-hover[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 123, 255, 0.075) !important;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%]   .table-hover[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #1a2530 !important;\\n}\\n\\n\\n.stack-trace[_ngcontent-%COMP%] {\\n  white-space: pre-wrap;\\n  font-size: 0.85rem;\\n  max-height: 300px;\\n  overflow-y: auto;\\n}\\n\\n\\n.bg-success[_ngcontent-%COMP%] {\\n  background-color: #28a745 !important;\\n}\\n\\n.bg-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff !important;\\n}\\n\\n.bg-info[_ngcontent-%COMP%] {\\n  background-color: #17a2b8 !important;\\n}\\n\\n.bg-warning[_ngcontent-%COMP%] {\\n  background-color: #ffc107 !important;\\n  color: #212529;\\n}\\n\\n.bg-danger[_ngcontent-%COMP%] {\\n  background-color: #dc3545 !important;\\n}\\n\\n.bg-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d !important;\\n}\\n\\n\\n@media (prefers-color-scheme: dark) {\\n  .table[_ngcontent-%COMP%] {\\n    color: var(--bs-light);\\n  }\\n\\n  .bg-warning[_ngcontent-%COMP%] {\\n    color: #212529;\\n  }\\n\\n  pre[_ngcontent-%COMP%] {\\n    color: var(--bs-light);\\n  }\\n}\\n\\n\\n[_nghost-%COMP%]     .card > .card-body > .table-responsive > .table > thead > tr > th:first-child {\\n  border-top-left-radius: 6px !important;\\n}\\n\\n[_nghost-%COMP%]     .card > .card-body > .table-responsive > .table > thead > tr > th:last-child {\\n  border-top-right-radius: 6px !important;\\n}\\n\\n\\n[_nghost-%COMP%]     .card > .card-body > .table-responsive > .table > thead {\\n  background-color: var(--bs-table-bg, #111921) !important;\\n}\\n\\n[_nghost-%COMP%]     .card > .card-body > .table-responsive > .table > thead > tr {\\n  background-color: var(--bs-table-bg, #111921) !important;\\n}\\n\\n[_nghost-%COMP%]     .card > .card-body > .table-responsive > .table > thead > tr > th {\\n  background-color: var(--bs-table-bg, #111921) !important;\\n  color: white !important;\\n  border-color: var(--bs-table-bg, #111921) !important;\\n}\\n\\n\\n[_nghost-%COMP%]     .card > .card-body > .table-responsive > .table {\\n  min-width: 100%; \\n}\\n\\n\\n[_nghost-%COMP%]     .card > .card-body > .table-responsive > .table > tbody > tr > td > .badge {\\n  padding: 0.35em 0.65em;\\n  font-size: 0.875rem; \\n}\\n\\n\\n[_nghost-%COMP%]     .card > .card-body > .table-responsive > .table > tbody > tr > td:nth-child(5), [_nghost-%COMP%]     .card > .card-body > .table-responsive > .table > thead > tr > th:nth-child(5) {\\n  max-width: 250px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n\\n[_nghost-%COMP%]     .card > .card-body > .table-responsive > .table > thead > tr > th {\\n  padding: 0.5rem;\\n  font-size: 1rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AACA,SAAyEA,yBAAyB,QAAQ,yBAAyB;;;;;;;;ICyBzHC,0BAAsB;IAGcA,+CAAW;IAAAA,iBAAO;;;;;IAItDA,0BAAgC;IAE5BA,YACF;IAAAA,iBAAK;;;;IADHA,eACF;IADEA,6CACF;;;;;IAEFA,0BAAsD;IAElDA,oEACF;IAAAA,iBAAK;;;;;IAmBHA,4BAAiG;IAC/FA,YACF;IAAAA,iBAAO;;;;;IAFsBA,kGAAmE;IAC9FA,eACF;IADEA,kDACF;;;;;IACAA,4BAA8B;IAAAA,iBAAC;IAAAA,iBAAO;;;;;;IApB1CA,0BAA4C;IACtCA;MAAA;MAAA;MAAA;MAAA,OAASA,4CAAkB;IAAA,EAAC;IAAwBA,YAAgD;;IAAAA,iBAAK;IAC7GA,8BAAwD;IAApDA;MAAA;MAAA;MAAA;MAAA,OAASA,4CAAkB;IAAA,EAAC;IAC9BA,4BAAsE;IACpEA,YACF;IAAAA,iBAAO;IAETA,8BAAgG;IAA5FA;MAAA;MAAA;MAAA;MAAA,OAASA,4CAAkB;IAAA,EAAC;IAAgEA,YAAiB;IAAAA,iBAAK;IACtHA,8BAAgG;IAA5FA;MAAA;MAAA;MAAA;MAAA,OAASA,4CAAkB;IAAA,EAAC;IAAgEA,aAAyB;IAAAA,iBAAK;IAC9HA,+BAAwD;IAApDA;MAAA;MAAA;MAAA;MAAA,OAASA,4CAAkB;IAAA,EAAC;IAC9BA,6BAAkE;IAChEA,aACF;IAAAA,iBAAO;IAETA,+BAAgG;IAA5FA;MAAA;MAAA;MAAA;MAAA,OAASA,4CAAkB;IAAA,EAAC;IAAgEA,aAA4B;IAAAA,iBAAK;IACjIA,+BAAwD;IAApDA;MAAA;MAAA;MAAA;MAAA,OAASA,4CAAkB;IAAA,EAAC;IAAwBA,aAA8B;IAAAA,iBAAK;IAC3FA,+BAAwD;IAApDA;MAAA;MAAA;MAAA;MAAA,OAASA,4CAAkB;IAAA,EAAC;IAC9BA,8EAEO;IACPA,6EAAsC;IACxCA,iBAAK;IACLA,+BAAwD;IAApDA;MAAA;MAAA;MAAA;MAAA,OAASA,4CAAkB;IAAA,EAAC;IAAwBA,aAA8B;IAAAA,iBAAK;IAC3FA,2BAAI;IAC0CA;MAAA;MAAA;MAAA;MAAA,OAASA,4CAAkB;IAAA,EAAC;IACtEA,yBAAiC;IACnCA,iBAAS;;;;;IAzB6CA,eAAgD;IAAhDA,oFAAgD;IAEhGA,eAA+D;IAA/DA,8FAA+D;IACnEA,eACF;IADEA,6EACF;IAE8FA,eAAiB;IAAjBA,oCAAiB;IACjBA,eAAyB;IAAzBA,4CAAyB;IAEjHA,eAA2D;IAA3DA,0FAA2D;IAC/DA,eACF;IADEA,yEACF;IAE8FA,eAA4B;IAA5BA,+CAA4B;IACpEA,eAA8B;IAA9BA,iDAA8B;IAE7EA,eAAoB;IAApBA,wCAAoB;IAGpBA,eAAqB;IAArBA,yCAAqB;IAE0BA,eAA8B;IAA9BA,sDAA8B;;;;;IAgEhFA,4BAAiH;IAC/GA,YACF;IAAAA,iBAAO;;;;IAF8BA,gHAA2E;IAC9GA,eACF;IADEA,+DACF;;;;;IACAA,4BAAsC;IAAAA,iBAAC;IAAAA,iBAAO;;;;;IAkBxDA,+BAAgD;IAExCA,2BAAW;IAAAA,iBAAK;IACpBA,+BAAkB;IAEgBA,YAA4B;IAAAA,iBAAM;;;;IAAlCA,eAA4B;IAA5BA,oDAA4B;;;;;IAMlEA,+BAAoD;IAE5CA,6CAAmB;IAAAA,iBAAK;IAC5BA,+BAAkB;IAEIA,YAAgC;IAAAA,iBAAM;;;;IAAtCA,eAAgC;IAAhCA,wDAAgC;;;;;IAlF5DA,+BAA4C;IAGlCA,4CAAkB;IAAAA,iBAAK;IAC3BA,iCAA8B;IAEtBA,mBAAG;IAAAA,iBAAK;IACZA,0BAAI;IAAAA,aAAoB;IAAAA,iBAAK;IAE/BA,2BAAI;IACEA,0BAAI;IAAAA,iBAAK;IACbA,2BAAI;IAAAA,aAAwD;;IAAAA,iBAAK;IAEnEA,2BAAI;IACEA,+BAAS;IAAAA,iBAAK;IAClBA,2BAAI;IAAAA,aAAsC;IAAAA,iBAAK;IAEjDA,2BAAI;IACEA,2BAAU;IAAAA,iBAAK;IACnBA,2BAAI;IAAAA,aAA2B;IAAAA,iBAAK;IAEtCA,2BAAI;IACEA,uBAAM;IAAAA,iBAAK;IACfA,2BAAI;IAEAA,aACF;IAAAA,iBAAO;IAKfA,gCAA2B;IACrBA,uCAAiB;IAAAA,iBAAK;IAC1BA,kCAA8B;IAEtBA,uBAAM;IAAAA,iBAAK;IACfA,2BAAI;IAAAA,aAAoC;IAAAA,iBAAK;IAE/CA,2BAAI;IACEA,wBAAO;IAAAA,iBAAK;IAChBA,2BAAI;IAAAA,aAAsC;IAAAA,iBAAK;IAEjDA,2BAAI;IACEA,uCAAY;IAAAA,iBAAK;IACrBA,2BAAI;IACFA,+EAEO;IACPA,8EAA8C;IAChDA,iBAAK;IAMbA,gCAAiB;IAETA,kCAAY;IAAAA,iBAAK;IACrBA,gCAAkB;IAEEA,aAAyB;IAAAA,iBAAI;IAMrDA,6EASM;IAENA,6EASM;IACRA,iBAAM;;;;IAhFQA,gBAAoB;IAApBA,2CAAoB;IAIpBA,eAAwD;IAAxDA,iGAAwD;IAIxDA,eAAsC;IAAtCA,kEAAsC;IAItCA,eAA2B;IAA3BA,kDAA2B;IAKvBA,eAAmE;IAAnEA,sGAAmE;IACvEA,eACF;IADEA,qFACF;IAUEA,eAAoC;IAApCA,2DAAoC;IAIpCA,eAAsC;IAAtCA,6DAAsC;IAKjCA,eAA4B;IAA5BA,oDAA4B;IAG5BA,eAA6B;IAA7BA,qDAA6B;IAYtBA,eAAyB;IAAzBA,gDAAyB;IAM/BA,eAA4B;IAA5BA,oDAA4B;IAW5BA,eAAgC;IAAhCA,wDAAgC;;;ADvJ1D,OAAM,MAAOC,kBAAkB;EAU7BC,YACUC,WAAwB,EACxBC,YAA0B;IAD1B,gBAAW,GAAXD,WAAW;IACX,iBAAY,GAAZC,YAAY;IAXtB,SAAI,GAAuB,EAAE;IAC7B,kBAAa,GAAqB;MAChCC,UAAU,EAAE;MACZ;KACD;;IACD,cAAS,GAAG,KAAK;IACjB,UAAK,GAAkB,IAAI;IAC3B,gBAAW,GAA4B,IAAI;EAKvC;EAEJC,QAAQ;IACN;IACA,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEAC,eAAe;IACb;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,QAAQ,EAAE;IACjB,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;;;EAGQH,sBAAsB;IAC5B,IAAI;MACF,MAAMI,SAAS,GAAG,sBAAsB;MACxC,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAACH,SAAS,CAAC;MAElD,IAAIC,UAAU,EAAE;QACd,MAAMG,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;QAE1C;QACA,IAAIG,WAAW,CAACG,QAAQ,EAAE;UACxBH,WAAW,CAACG,QAAQ,GAAG,IAAIC,IAAI,CAACJ,WAAW,CAACG,QAAQ,CAAC;;QAGvD,IAAIH,WAAW,CAACK,MAAM,EAAE;UACtBL,WAAW,CAACK,MAAM,GAAG,IAAID,IAAI,CAACJ,WAAW,CAACK,MAAM,CAAC;;QAGnD;QACA,IAAI,CAACC,aAAa,GAAG;UAAE,GAAG,IAAI,CAACA,aAAa;UAAE,GAAGN;QAAW,CAAE;QAC9DO,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACF,aAAa,CAAC;;KAEjE,CAAC,OAAOG,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;;EAEzE;EAEA;;;EAGAd,QAAQ;IACN,IAAI,CAACe,SAAS,GAAG,IAAI;IACrB,IAAI,CAACD,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACrB,WAAW,CAACuB,YAAY,CAAC,IAAI,CAACL,aAAa,CAAC,CAACM,SAAS,CAAC;MAC1DC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACA,IAAI,GAAGA,IAAI;QAChB,IAAI,CAACJ,SAAS,GAAG,KAAK;MACxB,CAAC;MACDD,KAAK,EAAGM,GAAG,IAAI;QACbR,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEM,GAAG,CAAC;QAClD,IAAI,CAACN,KAAK,GAAG,gCAAgC;QAC7C,IAAI,CAACC,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEA;;;EAGAM,cAAc,CAACC,MAAwB;IACrC,IAAI,CAACX,aAAa,GAAGW,MAAM;IAC3B,IAAI,CAACtB,QAAQ,EAAE;EACjB;EAEA;;;EAGAuB,aAAa,CAACV,GAAqB;IACjC,IAAI,CAACW,WAAW,GAAGX,GAAG;IACtB;IACA,IAAI,CAACnB,YAAY,CAAC+B,IAAI,CAAC,qBAAqB,CAAC;EAC/C;EAEA;;;EAGAC,cAAc;IACZ,IAAI,CAACF,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAAC9B,YAAY,CAACiC,KAAK,CAAC,qBAAqB,CAAC;EAChD;EAEA;;;EAGAC,kBAAkB,CAACC,MAAc;IAC/B,QAAQA,MAAM;MACZ,KAAK,UAAU;QAAE,OAAO,UAAU;MAClC,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B;QAAS,OAAOA,MAAM;IAAC;EAE3B;EAEA;;;EAGAC,cAAc,CAACD,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,UAAU;QAAE,OAAO,YAAY;MACpC,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B;QAAS,OAAO,cAAc;IAAC;EAEnC;EAEA;;;EAGAE,kBAAkB,CAACC,UAAkB;IACnC,IAAI,CAACA,UAAU,EAAE,OAAO,cAAc;IAEtC,IAAIA,UAAU,IAAI,GAAG,EAAE;MACrB,OAAO,WAAW;KACnB,MAAM,IAAIA,UAAU,IAAI,GAAG,EAAE;MAC5B,OAAO,YAAY;KACpB,MAAM,IAAIA,UAAU,IAAI,GAAG,EAAE;MAC5B,OAAO,SAAS;KACjB,MAAM,IAAIA,UAAU,IAAI,GAAG,EAAE;MAC5B,OAAO,YAAY;KACpB,MAAM;MACL,OAAO,cAAc;;EAEzB;EAEA;;;EAGAC,oBAAoB,CAACC,QAAgB;IACnC,OAAO7C,yBAAyB,CAAC8C,gBAAgB,CAACD,QAAQ,CAAC;EAC7D;EAEA;;;EAGAE,gBAAgB,CAACF,QAAgB;IAC/B,OAAO7C,yBAAyB,CAAC+C,gBAAgB,CAACF,QAAQ,CAAC;EAC7D;;;uBA3JW3C,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAA8C;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCT/BnD,yCAI0C;UAAxCA;YAAA,OAAgBoD,0BAAsB;UAAA,EAAC;UACzCpD,iBAAiB;UAGjBA,8BAA8B;UAIdA,wBAAG;UAAAA,iBAAK;UACZA,0BAAI;UAAAA,gCAAM;UAAAA,iBAAK;UACfA,0BAAI;UAAAA,4BAAM;UAAAA,iBAAK;UACfA,2BAAI;UAAAA,0BAAS;UAAAA,iBAAK;UAClBA,2BAAI;UAAAA,sBAAK;UAAAA,iBAAK;UACdA,2BAAI;UAAAA,sBAAK;UAAAA,iBAAK;UACdA,2BAAI;UAAAA,uBAAM;UAAAA,iBAAK;UACfA,2BAAI;UAAAA,yBAAG;UAAAA,iBAAK;UACZA,2BAAI;UAAAA,8BAAQ;UAAAA,iBAAK;UACjBA,sBAAS;UACXA,iBAAK;UAEPA,8BAAO;UACLA,mEAMK;UACLA,mEAIK;UACLA,mEAIK;UACLA,qEA4BK;UACPA,iBAAQ;UAKhBA,+BAA6H;UAI/DA,kCAAiB;UAAAA,iBAAK;UAC5EA,mCAA+G;UAA3BA;YAAA,OAASoD,oBAAgB;UAAA,EAAC;UAACpD,iBAAS;UAE1HA,wEAuFM;UACNA,gCAA0B;UACgDA;YAAA,OAASoD,oBAAgB;UAAA,EAAC;UAACpD,iCAAM;UAAAA,iBAAS;;;UA1KxHA,yCAA2B;UAuBdA,gBAAe;UAAfA,oCAAe;UAOfA,eAAyB;UAAzBA,kDAAyB;UAKzBA,eAA+C;UAA/CA,4EAA+C;UAKhCA,eAAS;UAATA,kCAAS;UAyCRA,eAAiB;UAAjBA,sCAAiB", "names": ["ApplicationLogLevelHelper", "i0", "ErrorLogsComponent", "constructor", "logsService", "modalService", "maxResults", "ngOnInit", "loadCurrentFilterState", "ngAfterViewInit", "setTimeout", "loadLogs", "<PERSON><PERSON><PERSON>", "filter<PERSON>son", "localStorage", "getItem", "savedFilter", "JSON", "parse", "fromDate", "Date", "toDate", "currentFilter", "console", "log", "error", "isLoading", "getErrorLogs", "subscribe", "next", "logs", "err", "onFilterChange", "filter", "showLogDetail", "<PERSON><PERSON><PERSON>", "open", "closeLogDetail", "close", "getLocalizedSource", "source", "getSourceClass", "getStatusCodeClass", "statusCode", "getLocalizedLogLevel", "logLevel", "getLocalizedName", "getLogLevelClass", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\logs\\error-logs\\error-logs.component.ts", "C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\logs\\error-logs\\error-logs.component.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit } from '@angular/core';\nimport { ErrorLogResponse, LogFilterRequest, LogSourceHelper, LogSource, ApplicationLogLevelHelper } from '../../models/logs.model';\nimport { LogsService } from '../../services/logs.service';\nimport { ModalService } from '../../services/modal.service';\n\n@Component({\n  selector: 'app-error-logs',\n  templateUrl: './error-logs.component.html',\n  styleUrls: ['../logs-common.css']\n})\nexport class ErrorLogsComponent implements OnInit, AfterViewInit {\n  logs: ErrorLogResponse[] = [];\n  currentFilter: LogFilterRequest = {\n    maxResults: 100,\n    // Pro logy chyb neomezujeme zdroj, chceme vidět chyby ze všech zdrojů\n  };\n  isLoading = false;\n  error: string | null = null;\n  selectedLog: ErrorLogResponse | null = null;\n\n  constructor(\n    private logsService: LogsService,\n    private modalService: ModalService\n  ) { }\n\n  ngOnInit(): void {\n    // Načtení aktuálního stavu filtru z localStorage\n    this.loadCurrentFilterState();\n  }\n\n  ngAfterViewInit(): void {\n    // Načtení logů až po inicializaci komponenty\n    setTimeout(() => {\n      this.loadLogs();\n    }, 0);\n  }\n\n  /**\n   * Načtení aktuálního stavu filtru z localStorage\n   */\n  private loadCurrentFilterState(): void {\n    try {\n      const filterKey = 'current_filter_error';\n      const filterJson = localStorage.getItem(filterKey);\n\n      if (filterJson) {\n        const savedFilter = JSON.parse(filterJson);\n\n        // Převod řetězcových hodnot na objekty Date\n        if (savedFilter.fromDate) {\n          savedFilter.fromDate = new Date(savedFilter.fromDate);\n        }\n\n        if (savedFilter.toDate) {\n          savedFilter.toDate = new Date(savedFilter.toDate);\n        }\n\n        // Nastavení hodnot filtru\n        this.currentFilter = { ...this.currentFilter, ...savedFilter };\n        console.log('Načten filtr pro logy chyb:', this.currentFilter);\n      }\n    } catch (error) {\n      console.error('Chyba při načítání stavu filtru pro logy chyb', error);\n    }\n  }\n\n  /**\n   * Načtení logů podle aktuálního filtru\n   */\n  loadLogs(): void {\n    this.isLoading = true;\n    this.error = null;\n\n    this.logsService.getErrorLogs(this.currentFilter).subscribe({\n      next: (logs) => {\n        this.logs = logs;\n        this.isLoading = false;\n      },\n      error: (err) => {\n        console.error('Chyba při načítání logů chyb', err);\n        this.error = 'Nepodařilo se načíst logy chyb';\n        this.isLoading = false;\n      }\n    });\n  }\n\n  /**\n   * Aktualizace filtru a načtení logů\n   */\n  onFilterChange(filter: LogFilterRequest): void {\n    this.currentFilter = filter;\n    this.loadLogs();\n  }\n\n  /**\n   * Zobrazení detailu logu\n   */\n  showLogDetail(log: ErrorLogResponse): void {\n    this.selectedLog = log;\n    // Otevření modálního okna\n    this.modalService.open('errorLogDetailModal');\n  }\n\n  /**\n   * Zavření detailu logu\n   */\n  closeLogDetail(): void {\n    this.selectedLog = null;\n    // Zavření modálního okna\n    this.modalService.close('errorLogDetailModal');\n  }\n\n  /**\n   * Získání lokalizovaného názvu zdroje logu\n   */\n  getLocalizedSource(source: string): string {\n    switch (source) {\n      case 'DISAdmin': return 'DISAdmin';\n      case 'DISApi': return 'DIS API';\n      default: return source;\n    }\n  }\n\n  /**\n   * Získání CSS třídy pro zdroj logu\n   */\n  getSourceClass(source: string): string {\n    switch (source) {\n      case 'DISAdmin': return 'bg-primary';\n      case 'DISApi': return 'bg-info';\n      default: return 'bg-secondary';\n    }\n  }\n\n  /**\n   * Získání CSS třídy pro stavový kód\n   */\n  getStatusCodeClass(statusCode: number): string {\n    if (!statusCode) return 'bg-secondary';\n\n    if (statusCode >= 500) {\n      return 'bg-danger';\n    } else if (statusCode >= 400) {\n      return 'bg-warning';\n    } else if (statusCode >= 300) {\n      return 'bg-info';\n    } else if (statusCode >= 200) {\n      return 'bg-success';\n    } else {\n      return 'bg-secondary';\n    }\n  }\n\n  /**\n   * Získání lokalizovaného názvu úrovně logování\n   */\n  getLocalizedLogLevel(logLevel: string): string {\n    return ApplicationLogLevelHelper.getLocalizedName(logLevel);\n  }\n\n  /**\n   * Získání CSS třídy pro úroveň logování\n   */\n  getLogLevelClass(logLevel: string): string {\n    return ApplicationLogLevelHelper.getLogLevelClass(logLevel);\n  }\n}\n", "<!-- Filtr -->\n<app-log-filter\n  logType=\"error\"\n  [showLogLevelFilter]=\"true\"\n  [showCategoryFilter]=\"true\"\n  (filterChange)=\"onFilterChange($event)\">\n</app-log-filter>\n\n<!-- <PERSON><PERSON><PERSON> logů -->\n<div class=\"table-responsive\">\n  <table class=\"table table-hover mb-0\">\n    <thead class=\"table-header-override dark-header\">\n      <tr class=\"dark-header-row\">\n            <th>Čas</th>\n            <th>Úroveň</th>\n            <th>Zpráva</th>\n            <th>Kategorie</th>\n            <th>Zdroj</th>\n            <th>Cesta</th>\n            <th>Metoda</th>\n            <th>Kód</th>\n            <th>Uživatel</th>\n            <th></th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr *ngIf=\"isLoading\">\n            <td colspan=\"10\" class=\"text-center py-4\">\n              <div class=\"spinner-border text-primary\" role=\"status\">\n                <span class=\"visually-hidden\">Načítání...</span>\n              </div>\n            </td>\n          </tr>\n          <tr *ngIf=\"!isLoading && error\">\n            <td colspan=\"10\" class=\"text-center text-danger py-4\">\n              {{ error }}\n            </td>\n          </tr>\n          <tr *ngIf=\"!isLoading && !error && logs.length === 0\">\n            <td colspan=\"10\" class=\"text-center py-4\">\n              Nebyly nalezeny žádné logy aplikace\n            </td>\n          </tr>\n          <tr *ngFor=\"let log of logs; let i = index\">\n            <td (click)=\"showLogDetail(log)\" class=\"cursor-pointer\">{{ log.timestamp | date:'dd.MM.yyyy HH:mm:ss' }}</td>\n            <td (click)=\"showLogDetail(log)\" class=\"cursor-pointer\">\n              <span class=\"badge rounded-pill {{ getLogLevelClass(log.logLevel) }}\">\n                {{ getLocalizedLogLevel(log.logLevel) }}\n              </span>\n            </td>\n            <td (click)=\"showLogDetail(log)\" class=\"text-truncate cursor-pointer\" style=\"max-width: 250px;\">{{ log.message }}</td>\n            <td (click)=\"showLogDetail(log)\" class=\"text-truncate cursor-pointer\" style=\"max-width: 150px;\">{{ log.category || '-' }}</td>\n            <td (click)=\"showLogDetail(log)\" class=\"cursor-pointer\">\n              <span class=\"badge rounded-pill {{ getSourceClass(log.source) }}\">\n                {{ getLocalizedSource(log.source) }}\n              </span>\n            </td>\n            <td (click)=\"showLogDetail(log)\" class=\"text-truncate cursor-pointer\" style=\"max-width: 150px;\">{{ log.requestPath || '-' }}</td>\n            <td (click)=\"showLogDetail(log)\" class=\"cursor-pointer\">{{ log.requestMethod || '-' }}</td>\n            <td (click)=\"showLogDetail(log)\" class=\"cursor-pointer\">\n              <span *ngIf=\"log.statusCode\" class=\"badge rounded-pill {{ getStatusCodeClass(log.statusCode) }}\">\n                {{ log.statusCode }}\n              </span>\n              <span *ngIf=\"!log.statusCode\">-</span>\n            </td>\n            <td (click)=\"showLogDetail(log)\" class=\"cursor-pointer\">{{ log.username || 'Systém' }}</td>\n            <td>\n              <button class=\"btn btn-sm btn-outline-info\" (click)=\"showLogDetail(log)\" title=\"Zobrazit detail\">\n                <i class=\"bi bi-info-circle\"></i>\n              </button>\n            </td>\n          </tr>\n        </tbody>\n      </table>\n    </div>\n\n<!-- Modální okno s detailem logu -->\n<div class=\"modal fade\" id=\"errorLogDetailModal\" tabindex=\"-1\" aria-labelledby=\"errorLogDetailModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog modal-lg\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\" id=\"errorLogDetailModalLabel\">Detail logu chyby</h5>\n        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Zavřít\" (click)=\"closeLogDetail()\"></button>\n      </div>\n      <div class=\"modal-body\" *ngIf=\"selectedLog\">\n        <div class=\"row\">\n          <div class=\"col-md-6 mb-3\">\n            <h6>Základní informace</h6>\n            <table class=\"table table-sm\">\n              <tr>\n                <th>ID:</th>\n                <td>{{ selectedLog.id }}</td>\n              </tr>\n              <tr>\n                <th>Čas:</th>\n                <td>{{ selectedLog.timestamp | date:'dd.MM.yyyy HH:mm:ss' }}</td>\n              </tr>\n              <tr>\n                <th>Uživatel:</th>\n                <td>{{ selectedLog.username || 'Systém' }}</td>\n              </tr>\n              <tr>\n                <th>IP adresa:</th>\n                <td>{{ selectedLog.ipAddress }}</td>\n              </tr>\n              <tr>\n                <th>Zdroj:</th>\n                <td>\n                  <span class=\"badge rounded-pill {{ getSourceClass(selectedLog.source) }}\">\n                    {{ getLocalizedSource(selectedLog.source) }}\n                  </span>\n                </td>\n              </tr>\n            </table>\n          </div>\n          <div class=\"col-md-6 mb-3\">\n            <h6>Detaily požadavku</h6>\n            <table class=\"table table-sm\">\n              <tr>\n                <th>Cesta:</th>\n                <td>{{ selectedLog.requestPath || '-' }}</td>\n              </tr>\n              <tr>\n                <th>Metoda:</th>\n                <td>{{ selectedLog.requestMethod || '-' }}</td>\n              </tr>\n              <tr>\n                <th>Stavový kód:</th>\n                <td>\n                  <span *ngIf=\"selectedLog.statusCode\" class=\"badge rounded-pill {{ getStatusCodeClass(selectedLog.statusCode) }}\">\n                    {{ selectedLog.statusCode }}\n                  </span>\n                  <span *ngIf=\"!selectedLog.statusCode\">-</span>\n                </td>\n              </tr>\n            </table>\n          </div>\n        </div>\n\n        <div class=\"row\">\n          <div class=\"col-12 mb-3\">\n            <h6>Zpráva chyby</h6>\n            <div class=\"card\">\n              <div class=\"card-body\">\n                <p class=\"mb-0\">{{ selectedLog.message }}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"row\" *ngIf=\"selectedLog.stackTrace\">\n          <div class=\"col-12 mb-3\">\n            <h6>Stack Trace</h6>\n            <div class=\"card\">\n              <div class=\"card-body\">\n                <pre class=\"mb-0 stack-trace\">{{ selectedLog.stackTrace }}</pre>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"row\" *ngIf=\"selectedLog.additionalInfo\">\n          <div class=\"col-12\">\n            <h6>Dodatečné informace</h6>\n            <div class=\"card\">\n              <div class=\"card-body\">\n                <pre class=\"mb-0\">{{ selectedLog.additionalInfo }}</pre>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\" (click)=\"closeLogDetail()\">Zavřít</button>\n      </div>\n    </div>\n  </div>\n</div>\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}