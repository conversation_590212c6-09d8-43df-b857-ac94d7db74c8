{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/VSCodeProjects/DISAdminAugment/DISAdmin.Web/ClientApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { ActivityTypeHelper, LogSourceHelper, LogSource, ApplicationLogLevelHelper } from '../../models/logs.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/logs.service\";\nimport * as i3 from \"../../services/user.service\";\nimport * as i4 from \"../../services/modal.service\";\nimport * as i5 from \"ngx-toastr\";\nimport * as i6 from \"@angular/common\";\nfunction LogFilterComponent_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.activeFilterCount);\n  }\n}\nfunction LogFilterComponent_div_18_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r12.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(user_r12.username);\n  }\n}\nfunction LogFilterComponent_div_18_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const source_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", source_r13.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(source_r13.label);\n  }\n}\nfunction LogFilterComponent_div_18_ng_container_17_option_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r15.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(type_r15.label);\n  }\n}\nfunction LogFilterComponent_div_18_ng_container_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 32)(2, \"label\", 48);\n    i0.ɵɵtext(3, \"Typ aktivity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"select\", 49)(5, \"option\", 35);\n    i0.ɵɵtext(6, \"V\\u0161echny typy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, LogFilterComponent_div_18_ng_container_17_option_7_Template, 2, 2, \"option\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 32)(9, \"label\", 50);\n    i0.ɵɵtext(10, \"Entita\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"input\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 32)(13, \"label\", 52);\n    i0.ɵɵtext(14, \"ID entity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngValue\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.activityTypes);\n  }\n}\nfunction LogFilterComponent_div_18_ng_container_18_div_1_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const level_r19 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", level_r19.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(level_r19.label);\n  }\n}\nfunction LogFilterComponent_div_18_ng_container_18_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\", 54);\n    i0.ɵɵtext(2, \"\\u00DArove\\u0148 logov\\u00E1n\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"select\", 55)(4, \"option\", 35);\n    i0.ɵɵtext(5, \"V\\u0161echny \\u00FArovn\\u011B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, LogFilterComponent_div_18_ng_container_18_div_1_option_6_Template, 2, 2, \"option\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngValue\", null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.logLevels);\n  }\n}\nfunction LogFilterComponent_div_18_ng_container_18_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\", 56);\n    i0.ɵɵtext(2, \"Kategorie\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 57);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LogFilterComponent_div_18_ng_container_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LogFilterComponent_div_18_ng_container_18_div_1_Template, 7, 2, \"div\", 45);\n    i0.ɵɵtemplate(2, LogFilterComponent_div_18_ng_container_18_div_2_Template, 4, 0, \"div\", 45);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.showLogLevelFilter);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.showCategoryFilter);\n  }\n}\nfunction LogFilterComponent_div_18_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r20.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(option_r20.label);\n  }\n}\nfunction LogFilterComponent_div_18_div_28_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1, \" Zadejte po\\u010D\\u00E1te\\u010Dn\\u00ED datum \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\nfunction LogFilterComponent_div_18_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\", 58);\n    i0.ɵɵtext(2, \"Od\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 59);\n    i0.ɵɵtemplate(4, LogFilterComponent_div_18_div_28_div_4_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ((tmp_0_0 = ctx_r9.filterForm.get(\"fromDate\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r9.filterForm.get(\"fromDate\")) == null ? null : tmp_0_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r9.filterForm.get(\"fromDate\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"required\"]);\n  }\n}\nfunction LogFilterComponent_div_18_div_29_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1, \" Zadejte koncov\\u00E9 datum \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LogFilterComponent_div_18_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\", 62);\n    i0.ɵɵtext(2, \"Do\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 63);\n    i0.ɵɵtemplate(4, LogFilterComponent_div_18_div_29_div_4_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ((tmp_0_0 = ctx_r10.filterForm.get(\"toDate\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r10.filterForm.get(\"toDate\")) == null ? null : tmp_0_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r10.filterForm.get(\"toDate\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"required\"]);\n  }\n}\nfunction LogFilterComponent_div_18_div_30_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵlistener(\"click\", function LogFilterComponent_div_18_div_30_div_4_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r26);\n      const filter_r24 = restoredCtx.$implicit;\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r25.applySavedFilter(filter_r24));\n    });\n    i0.ɵɵelementStart(1, \"span\", 68);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function LogFilterComponent_div_18_div_30_div_4_Template_button_click_3_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r26);\n      const filter_r24 = restoredCtx.$implicit;\n      const ctx_r27 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r27.deleteSavedFilter(filter_r24, $event));\n    });\n    i0.ɵɵelement(4, \"i\", 70);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const filter_r24 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(filter_r24.name);\n  }\n}\nfunction LogFilterComponent_div_18_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"h6\");\n    i0.ɵɵtext(2, \"Ulo\\u017Een\\u00E9 filtry\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 65);\n    i0.ɵɵtemplate(4, LogFilterComponent_div_18_div_30_div_4_Template, 5, 1, \"div\", 66);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.savedFilters);\n  }\n}\nfunction LogFilterComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"form\", 20)(2, \"div\", 31)(3, \"div\", 32)(4, \"label\", 33);\n    i0.ɵɵtext(5, \"U\\u017Eivatel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"select\", 34)(7, \"option\", 35);\n    i0.ɵɵtext(8, \"V\\u0161ichni u\\u017Eivatel\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, LogFilterComponent_div_18_option_9_Template, 2, 2, \"option\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 32)(11, \"label\", 37);\n    i0.ɵɵtext(12, \"Zdroj\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"select\", 38)(14, \"option\", 35);\n    i0.ɵɵtext(15, \"V\\u0161echny zdroje\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, LogFilterComponent_div_18_option_16_Template, 2, 2, \"option\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(17, LogFilterComponent_div_18_ng_container_17_Template, 16, 2, \"ng-container\", 40);\n    i0.ɵɵtemplate(18, LogFilterComponent_div_18_ng_container_18_Template, 3, 2, \"ng-container\", 40);\n    i0.ɵɵelementStart(19, \"div\", 32)(20, \"label\", 41);\n    i0.ɵɵtext(21, \"Po\\u010Det z\\u00E1znam\\u016F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"input\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 32)(24, \"label\", 43);\n    i0.ɵɵtext(25, \"\\u010Casov\\u00E9 obdob\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"select\", 44);\n    i0.ɵɵtemplate(27, LogFilterComponent_div_18_option_27_Template, 2, 2, \"option\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(28, LogFilterComponent_div_18_div_28_Template, 5, 4, \"div\", 45);\n    i0.ɵɵtemplate(29, LogFilterComponent_div_18_div_29_Template, 5, 4, \"div\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(30, LogFilterComponent_div_18_div_30_Template, 5, 1, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_8_0;\n    let tmp_9_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.filterForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngValue\", null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.users);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngValue\", null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.logSources);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.logType === \"activity\" || ctx_r1.logType === \"api\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.logType === \"error\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.periodOptions);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx_r1.filterForm.get(\"period\")) == null ? null : tmp_8_0.value) === \"custom\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx_r1.filterForm.get(\"period\")) == null ? null : tmp_9_0.value) === \"custom\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.savedFilters.length > 0);\n  }\n}\nfunction LogFilterComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.error);\n  }\n}\nfunction LogFilterComponent_span_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 72);\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"bi-funnel\": a0,\n    \"bi-funnel-fill text-primary\": a1\n  };\n};\nconst _c2 = function (a0, a1) {\n  return {\n    \"bi-chevron-up\": a0,\n    \"bi-chevron-down\": a1\n  };\n};\nexport let LogFilterComponent = /*#__PURE__*/(() => {\n  class LogFilterComponent {\n    constructor(fb, logsService, userService, modalService, toastr) {\n      this.fb = fb;\n      this.logsService = logsService;\n      this.userService = userService;\n      this.modalService = modalService;\n      this.toastr = toastr;\n      this.logType = 'activity'; // activity, error, api\n      this.showLogLevelFilter = false; // Zobrazit filtr úrovně logování\n      this.showCategoryFilter = false; // Zobrazit filtr kategorie\n      this.filterChange = new EventEmitter();\n      this.savedFilters = [];\n      this.users = [];\n      this.activityTypes = ActivityTypeHelper.getActivityTypes();\n      this.logSources = LogSourceHelper.getLogSources();\n      this.logLevels = ApplicationLogLevelHelper.getLogLevels();\n      this.isLoading = false;\n      this.error = null;\n      // Vlastnosti pro skrývání/zobrazování filtru\n      this.isFilterVisible = false; // Filtr je ve výchozím stavu skrytý\n      this.hasActiveFilters = false;\n      this.activeFilterCount = 0;\n      // Časová období pro dropdown\n      this.periodOptions = [{\n        value: 1,\n        label: '1 den'\n      }, {\n        value: 7,\n        label: '7 dní'\n      }, {\n        value: 30,\n        label: '30 dní'\n      }, {\n        value: 90,\n        label: '90 dní'\n      }, {\n        value: 'custom',\n        label: 'Vlastní období'\n      }];\n      this.filterForm = this.createFilterForm();\n      this.saveFilterForm = this.fb.group({\n        name: ['']\n      });\n    }\n    ngOnInit() {\n      this.loadUsers();\n      this.loadSavedFilters();\n      this.loadFilterVisibilityState();\n      this.loadCurrentFilterState();\n      // Automatické aplikování filtru při změně formuláře\n      this.filterForm.valueChanges.subscribe(() => {\n        this.updateActiveFiltersIndicator();\n        this.applyFilter();\n      });\n      // Sledování změny období pro zobrazení/skrytí polí fromDate a toDate\n      this.filterForm.get('period')?.valueChanges.subscribe(period => {\n        this.updateDateFieldsValidation(period);\n      });\n    }\n    /**\r\n     * Aktualizace validace datumových polí podle vybraného období\r\n     */\n    updateDateFieldsValidation(period) {\n      const fromDateControl = this.filterForm.get('fromDate');\n      const toDateControl = this.filterForm.get('toDate');\n      if (period === 'custom') {\n        // Při vlastním období jsou pole povinná\n        fromDateControl?.setValidators([Validators.required]);\n        toDateControl?.setValidators([Validators.required]);\n      } else {\n        // Při přednastaveném období nejsou pole povinná\n        fromDateControl?.clearValidators();\n        toDateControl?.clearValidators();\n      }\n      fromDateControl?.updateValueAndValidity();\n      toDateControl?.updateValueAndValidity();\n    }\n    /**\r\n     * Načtení aktuálního stavu filtru z localStorage\r\n     */\n    loadCurrentFilterState() {\n      try {\n        const filterKey = `current_filter_${this.logType}`;\n        const filterJson = localStorage.getItem(filterKey);\n        if (filterJson) {\n          const savedFilter = JSON.parse(filterJson);\n          // Detekce starého formátu filtru (bez pole period)\n          if (savedFilter.fromDate && savedFilter.toDate && !savedFilter.period) {\n            // Pokusíme se detekovat období podle rozdílu dat\n            const fromDate = new Date(savedFilter.fromDate);\n            const toDate = new Date(savedFilter.toDate);\n            const diffDays = Math.round((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));\n            // Pokud je rozdíl blízký některému z předdefinovaných období, použijeme ho\n            if (diffDays <= 1) {\n              savedFilter.period = 1;\n            } else if (diffDays <= 7) {\n              savedFilter.period = 7;\n            } else if (diffDays <= 30) {\n              savedFilter.period = 30;\n            } else if (diffDays <= 90) {\n              savedFilter.period = 90;\n            } else {\n              savedFilter.period = 'custom';\n            }\n          }\n          // Převod objektů Date na řetězce pro input\n          if (savedFilter.fromDate) {\n            const fromDate = new Date(savedFilter.fromDate);\n            savedFilter.fromDate = this.formatDateForInput(fromDate);\n          }\n          if (savedFilter.toDate) {\n            const toDate = new Date(savedFilter.toDate);\n            savedFilter.toDate = this.formatDateForInput(toDate);\n          }\n          // Nastavení hodnot formuláře\n          this.filterForm.patchValue(savedFilter);\n          // Aktualizace validace datumových polí\n          this.updateDateFieldsValidation(savedFilter.period);\n          // Aktualizace indikátoru aktivních filtrů\n          this.updateActiveFiltersIndicator();\n        }\n      } catch (error) {\n        console.error(`Chyba při načítání stavu filtru pro ${this.logType}`, error);\n      }\n    }\n    /**\r\n     * Přepne viditelnost filtru a uloží stav do localStorage\r\n     */\n    toggleFilterVisibility() {\n      this.isFilterVisible = !this.isFilterVisible;\n      // Uložení stavu viditelnosti do localStorage\n      try {\n        const visibilityKey = `filter_visibility_logs_${this.logType}`;\n        localStorage.setItem(visibilityKey, this.isFilterVisible.toString());\n      } catch (error) {\n        console.error('Chyba při ukládání stavu viditelnosti filtru do localStorage', error);\n      }\n    }\n    /**\r\n     * Načte stav viditelnosti filtru z localStorage\r\n     */\n    loadFilterVisibilityState() {\n      try {\n        const visibilityKey = `filter_visibility_logs_${this.logType}`;\n        const savedVisibility = localStorage.getItem(visibilityKey);\n        if (savedVisibility !== null) {\n          this.isFilterVisible = savedVisibility === 'true';\n        }\n      } catch (error) {\n        console.error('Chyba při načítání stavu viditelnosti filtru z localStorage', error);\n      }\n    }\n    /**\r\n     * Aktualizuje indikátor aktivních filtrů\r\n     */\n    updateActiveFiltersIndicator() {\n      const values = this.filterForm.value;\n      // Získání výchozích hodnot pro časové filtry\n      const today = new Date();\n      const endOfDay = new Date(today);\n      endOfDay.setHours(23, 59, 59, 999);\n      const sevenDaysAgo = new Date(today);\n      sevenDaysAgo.setDate(today.getDate() - 7);\n      sevenDaysAgo.setHours(0, 0, 0, 0);\n      // Formátování dat pro porovnání\n      const defaultFromDateStr = this.formatDateForInput(sevenDaysAgo);\n      const defaultToDateStr = this.formatDateForInput(endOfDay);\n      // Výchozí hodnota pro období\n      const defaultPeriod = 7;\n      // Odstranění prázdných hodnot a výchozích hodnot\n      const activeFilters = Object.keys(values).filter(key => {\n        // Ignorujeme maxResults s hodnotou 100, což je výchozí hodnota\n        if (key === 'maxResults' && values[key] === 100) {\n          return false;\n        }\n        // Ignorujeme source s výchozí hodnotou podle typu logu\n        if (key === 'source') {\n          if (this.logType === 'activity' && values[key] === LogSource.DISAdmin) {\n            return false;\n          }\n          if (this.logType === 'api' && values[key] === LogSource.DISApi) {\n            return false;\n          }\n        }\n        // Ignorujeme výchozí hodnotu pro období\n        if (key === 'period' && values[key] === defaultPeriod) {\n          return false;\n        }\n        // Při přednastaveném období ignorujeme fromDate a toDate\n        if (values.period !== 'custom' && (key === 'fromDate' || key === 'toDate')) {\n          return false;\n        }\n        // Při vlastním období kontrolujeme výchozí hodnoty\n        if (values.period === 'custom') {\n          if (key === 'fromDate' && values[key] === defaultFromDateStr) {\n            return false;\n          }\n          if (key === 'toDate' && values[key] === defaultToDateStr) {\n            return false;\n          }\n        }\n        // Ignorujeme null, prázdné řetězce a undefined\n        return values[key] !== null && values[key] !== '' && values[key] !== undefined;\n      }).reduce((obj, key) => {\n        obj[key] = values[key];\n        return obj;\n      }, {});\n      this.activeFilterCount = Object.keys(activeFilters).length;\n      this.hasActiveFilters = this.activeFilterCount > 0;\n      // Výpis aktivních filtrů do konzole pro ladění\n      if (this.hasActiveFilters) {\n        console.log('Aktivní filtry:', activeFilters);\n      }\n    }\n    /**\r\n     * Vytvoření formuláře pro filtrování\r\n     */\n    createFilterForm() {\n      // Nastavení výchozích hodnot pro časové filtry\n      const today = new Date();\n      const endOfDay = new Date(today);\n      endOfDay.setHours(23, 59, 59, 999);\n      const sevenDaysAgo = new Date(today);\n      sevenDaysAgo.setDate(today.getDate() - 7);\n      sevenDaysAgo.setHours(0, 0, 0, 0);\n      // Formátování dat pro input typu datetime-local\n      const fromDateStr = this.formatDateForInput(sevenDaysAgo);\n      const toDateStr = this.formatDateForInput(endOfDay);\n      // Základní pole pro všechny typy logů\n      const formControls = {\n        period: [7],\n        fromDate: [fromDateStr],\n        toDate: [toDateStr],\n        userId: [null],\n        maxResults: [100],\n        source: [null]\n      };\n      // Nastavení výchozí hodnoty pro zdroj podle typu logu\n      if (this.logType === 'activity') {\n        formControls.source = [LogSource.DISAdmin];\n      } else if (this.logType === 'api') {\n        formControls.source = [LogSource.DISApi];\n      }\n      // Přidání specifických polí podle typu logu\n      if (this.logType === 'activity' || this.logType === 'api') {\n        formControls.entityName = [''];\n        formControls.entityId = [null];\n        formControls.activityType = [''];\n      }\n      // Přidání polí pro error logy\n      if (this.logType === 'error') {\n        formControls.logLevel = [null];\n        formControls.category = [''];\n      }\n      return this.fb.group(formControls);\n    }\n    /**\r\n     * Načtení uživatelů pro filtr\r\n     */\n    loadUsers() {\n      this.userService.getUsers().subscribe({\n        next: users => {\n          this.users = users;\n        },\n        error: err => {\n          console.error('Chyba při načítání uživatelů', err);\n        }\n      });\n    }\n    /**\r\n     * Načtení uložených filtrů\r\n     */\n    loadSavedFilters() {\n      this.logsService.getSavedLogFilters(this.logType).subscribe({\n        next: filters => {\n          this.savedFilters = filters;\n        },\n        error: err => {\n          console.error('Chyba při načítání uložených filtrů', err);\n        }\n      });\n    }\n    /**\r\n     * Aplikování filtru\r\n     */\n    applyFilter() {\n      const formValues = this.filterForm.value;\n      const filterData = {\n        ...formValues\n      };\n      // Zpracování časového období\n      if (formValues.period !== 'custom') {\n        // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n        const today = new Date();\n        const endOfDay = new Date(today);\n        endOfDay.setHours(23, 59, 59, 999);\n        const fromDate = new Date(today);\n        fromDate.setDate(today.getDate() - Number(formValues.period));\n        fromDate.setHours(0, 0, 0, 0);\n        filterData.fromDate = fromDate;\n        filterData.toDate = endOfDay;\n      } else {\n        // Při vlastním období použijeme zadané hodnoty\n        if (formValues.fromDate) {\n          filterData.fromDate = new Date(formValues.fromDate);\n        }\n        if (formValues.toDate) {\n          filterData.toDate = new Date(formValues.toDate);\n        }\n      }\n      // Uložení stavu filtru do localStorage\n      this.saveFilterState(filterData);\n      this.filterChange.emit(filterData);\n    }\n    /**\r\n     * Načtení dat podle aktuálního filtru\r\n     */\n    refreshData() {\n      // Aplikujeme aktuální filtr znovu\n      this.applyFilter();\n    }\n    /**\r\n     * Uložení stavu filtru do localStorage\r\n     */\n    saveFilterState(filterData) {\n      try {\n        const filterKey = `current_filter_${this.logType}`;\n        localStorage.setItem(filterKey, JSON.stringify(filterData));\n      } catch (error) {\n        console.error(`Chyba při ukládání stavu filtru pro ${this.logType}`, error);\n      }\n    }\n    /**\r\n     * Resetování filtru\r\n     */\n    resetFilter() {\n      // Nastavení výchozích hodnot pro časové filtry\n      const today = new Date();\n      const endOfDay = new Date(today);\n      endOfDay.setHours(23, 59, 59, 999);\n      const sevenDaysAgo = new Date(today);\n      sevenDaysAgo.setDate(today.getDate() - 7);\n      sevenDaysAgo.setHours(0, 0, 0, 0);\n      // Formátování dat pro input typu datetime-local\n      const fromDateStr = this.formatDateForInput(sevenDaysAgo);\n      const toDateStr = this.formatDateForInput(endOfDay);\n      // Základní hodnoty pro reset\n      const resetValues = {\n        period: 7,\n        fromDate: fromDateStr,\n        toDate: toDateStr,\n        maxResults: 100\n      };\n      // Nastavení výchozí hodnoty pro zdroj podle typu logu\n      if (this.logType === 'activity') {\n        resetValues.source = LogSource.DISAdmin;\n      } else if (this.logType === 'api') {\n        resetValues.source = LogSource.DISApi;\n      }\n      this.filterForm.reset(resetValues);\n      this.updateActiveFiltersIndicator();\n      this.applyFilter();\n      // Zobrazení notifikace o resetování filtru\n      this.toastr.info('Filtr byl resetován', 'Reset');\n    }\n    /**\r\n     * Formátuje datum pro input typu datetime-local\r\n     * Format: YYYY-MM-DDThh:mm\r\n     */\n    formatDateForInput(date) {\n      const year = date.getFullYear();\n      const month = (date.getMonth() + 1).toString().padStart(2, '0');\n      const day = date.getDate().toString().padStart(2, '0');\n      const hours = date.getHours().toString().padStart(2, '0');\n      const minutes = date.getMinutes().toString().padStart(2, '0');\n      return `${year}-${month}-${day}T${hours}:${minutes}`;\n    }\n    /**\r\n     * Otevření modálního okna pro uložení filtru\r\n     */\n    openSaveFilterModal() {\n      this.saveFilterForm.reset();\n      this.modalService.open('saveFilterModal');\n    }\n    /**\r\n     * Uložení filtru\r\n     */\n    saveFilter() {\n      if (this.saveFilterForm.invalid) {\n        return;\n      }\n      const filterName = this.saveFilterForm.get('name')?.value;\n      const filterData = this.filterForm.value;\n      const saveRequest = {\n        name: filterName,\n        logType: this.logType,\n        filterData: JSON.stringify(filterData)\n      };\n      this.isLoading = true;\n      this.logsService.saveLogFilter(saveRequest).subscribe({\n        next: response => {\n          this.savedFilters.push(response);\n          this.modalService.close('saveFilterModal');\n          this.isLoading = false;\n        },\n        error: err => {\n          console.error('Chyba při ukládání filtru', err);\n          this.error = 'Nepodařilo se uložit filtr';\n          this.isLoading = false;\n        }\n      });\n    }\n    /**\r\n     * Aplikování uloženého filtru\r\n     */\n    applySavedFilter(filter) {\n      try {\n        const parsedData = JSON.parse(filter.filterData);\n        // Vytvoříme nový objekt pro formulář s řetězcovými hodnotami pro datetime-local inputy\n        const formValues = {\n          ...parsedData\n        };\n        // Detekce starého formátu filtru (bez pole period)\n        if (parsedData.fromDate && parsedData.toDate && !parsedData.period) {\n          // Pokusíme se detekovat období podle rozdílu dat\n          const fromDate = new Date(parsedData.fromDate);\n          const toDate = new Date(parsedData.toDate);\n          const diffDays = Math.round((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));\n          // Pokud je rozdíl blízký některému z předdefinovaných období, použijeme ho\n          if (diffDays <= 1) {\n            formValues.period = 1;\n          } else if (diffDays <= 7) {\n            formValues.period = 7;\n          } else if (diffDays <= 30) {\n            formValues.period = 30;\n          } else if (diffDays <= 90) {\n            formValues.period = 90;\n          } else {\n            formValues.period = 'custom';\n          }\n        }\n        // Převod objektů Date na řetězce pro input\n        if (parsedData.fromDate) {\n          const fromDate = new Date(parsedData.fromDate);\n          formValues.fromDate = this.formatDateForInput(fromDate);\n        }\n        if (parsedData.toDate) {\n          const toDate = new Date(parsedData.toDate);\n          formValues.toDate = this.formatDateForInput(toDate);\n        }\n        // Resetujeme formulář, aby se vyčistily všechny hodnoty\n        this.filterForm.reset({\n          maxResults: 100\n        });\n        // Nastavíme hodnoty formuláře\n        this.filterForm.patchValue(formValues);\n        // Zajistíme, že zdroj je správně nastaven podle typu logu\n        if (this.logType === 'activity') {\n          this.filterForm.patchValue({\n            source: LogSource.DISAdmin\n          });\n        } else if (this.logType === 'api') {\n          this.filterForm.patchValue({\n            source: LogSource.DISApi\n          });\n        }\n        // Aktualizace validace datumových polí\n        this.updateDateFieldsValidation(formValues.period);\n        // Aktualizujeme indikátor aktivních filtrů\n        this.updateActiveFiltersIndicator();\n        // Aplikujeme filtr, který převede řetězce zpět na Date objekty\n        this.applyFilter();\n      } catch (error) {\n        console.error('Chyba při aplikování uloženého filtru', error);\n      }\n    }\n    /**\r\n     * Odstranění uloženého filtru\r\n     */\n    deleteSavedFilter(filter, event) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        event.stopPropagation(); // Zabránění aplikování filtru při kliknutí na tlačítko smazat\n        // Použijeme vlastní dialog pro potvrzení místo nativního confirm\n        const confirmed = yield _this.modalService.confirm(`Opravdu chcete smazat filtr \"${filter.name}\"?`, 'Odstranit filtr', 'OK', 'Zrušit', 'btn-danger', 'btn-secondary');\n        if (confirmed) {\n          _this.logsService.deleteLogFilter(filter.id).subscribe({\n            next: () => {\n              _this.savedFilters = _this.savedFilters.filter(f => f.id !== filter.id);\n            },\n            error: err => {\n              console.error('Chyba při mazání filtru', err);\n            }\n          });\n        }\n      })();\n    }\n    static {\n      this.ɵfac = function LogFilterComponent_Factory(t) {\n        return new (t || LogFilterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.LogsService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.ModalService), i0.ɵɵdirectiveInject(i5.ToastrService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LogFilterComponent,\n        selectors: [[\"app-log-filter\"]],\n        inputs: {\n          logType: \"logType\",\n          showLogLevelFilter: \"showLogLevelFilter\",\n          showCategoryFilter: \"showCategoryFilter\"\n        },\n        outputs: {\n          filterChange: \"filterChange\"\n        },\n        decls: 39,\n        vars: 14,\n        consts: [[1, \"card\", \"mb-4\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\", \"filter-title\", 3, \"click\"], [1, \"bi\", \"fs-5\", \"me-2\", 3, \"ngClass\"], [\"class\", \"badge bg-danger ms-2 badge-smaller\", 4, \"ngIf\"], [1, \"bi\", \"ms-2\", 3, \"ngClass\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", \"me-4\", 3, \"click\"], [1, \"bi\", \"bi-save\", \"me-1\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"me-2\", 3, \"click\"], [1, \"bi\", \"bi-arrow-counterclockwise\", \"me-1\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-repeat\", \"me-1\"], [\"class\", \"card-body\", 4, \"ngIf\"], [\"id\", \"saveFilterModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"saveFilterModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"saveFilterModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\"], [1, \"modal-body\"], [3, \"formGroup\"], [1, \"mb-3\"], [\"for\", \"filterName\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"filterName\", \"formControlName\", \"name\", \"required\", \"\", 1, \"form-control\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [\"class\", \"spinner-border spinner-border-sm me-1\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [1, \"badge\", \"bg-danger\", \"ms-2\", \"badge-smaller\"], [1, \"card-body\"], [1, \"row\"], [1, \"col-md-6\", \"col-lg-3\", \"mb-3\"], [\"for\", \"userId\", 1, \"form-label\"], [\"id\", \"userId\", \"formControlName\", \"userId\", 1, \"form-select\"], [3, \"ngValue\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"source\", 1, \"form-label\"], [\"id\", \"source\", \"formControlName\", \"source\", 1, \"form-select\"], [3, \"ngValue\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"for\", \"maxResults\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"maxResults\", \"formControlName\", \"maxResults\", \"min\", \"1\", \"max\", \"1000\", 1, \"form-control\"], [\"for\", \"period\", 1, \"form-label\"], [\"id\", \"period\", \"formControlName\", \"period\", 1, \"form-select\"], [\"class\", \"col-md-6 col-lg-3 mb-3\", 4, \"ngIf\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [3, \"value\"], [\"for\", \"activityType\", 1, \"form-label\"], [\"id\", \"activityType\", \"formControlName\", \"activityType\", 1, \"form-select\"], [\"for\", \"entityName\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"entityName\", \"formControlName\", \"entityName\", \"placeholder\", \"Nap\\u0159. customers, versions...\", 1, \"form-control\"], [\"for\", \"entityId\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"entityId\", \"formControlName\", \"entityId\", 1, \"form-control\"], [\"for\", \"logLevel\", 1, \"form-label\"], [\"id\", \"logLevel\", \"formControlName\", \"logLevel\", 1, \"form-select\"], [\"for\", \"category\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"category\", \"formControlName\", \"category\", \"placeholder\", \"Nap\\u0159. DISAdmin.Api.Controllers...\", 1, \"form-control\"], [\"for\", \"fromDate\", 1, \"form-label\"], [\"type\", \"datetime-local\", \"id\", \"fromDate\", \"formControlName\", \"fromDate\", 1, \"form-control\", 3, \"ngClass\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [1, \"invalid-feedback\"], [\"for\", \"toDate\", 1, \"form-label\"], [\"type\", \"datetime-local\", \"id\", \"toDate\", \"formControlName\", \"toDate\", 1, \"form-control\", 3, \"ngClass\"], [1, \"mt-3\"], [1, \"saved-filters\"], [\"class\", \"saved-filter\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"saved-filter\", 3, \"click\"], [1, \"filter-name\"], [1, \"btn\", \"btn-sm\", \"btn-link\", \"text-danger\", 3, \"click\"], [1, \"bi\", \"bi-trash\"], [1, \"alert\", \"alert-danger\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-1\"]],\n        template: function LogFilterComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h5\", 2);\n            i0.ɵɵlistener(\"click\", function LogFilterComponent_Template_h5_click_2_listener() {\n              return ctx.toggleFilterVisibility();\n            });\n            i0.ɵɵelement(3, \"i\", 3);\n            i0.ɵɵelementStart(4, \"span\");\n            i0.ɵɵtext(5, \"Filtr\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(6, LogFilterComponent_span_6_Template, 2, 1, \"span\", 4);\n            i0.ɵɵelement(7, \"i\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"div\")(9, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function LogFilterComponent_Template_button_click_9_listener() {\n              return ctx.openSaveFilterModal();\n            });\n            i0.ɵɵelement(10, \"i\", 7);\n            i0.ɵɵtext(11, \"Ulo\\u017Eit filtr \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"button\", 8);\n            i0.ɵɵlistener(\"click\", function LogFilterComponent_Template_button_click_12_listener() {\n              return ctx.resetFilter();\n            });\n            i0.ɵɵelement(13, \"i\", 9);\n            i0.ɵɵtext(14, \"Reset \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"button\", 10);\n            i0.ɵɵlistener(\"click\", function LogFilterComponent_Template_button_click_15_listener() {\n              return ctx.refreshData();\n            });\n            i0.ɵɵelement(16, \"i\", 11);\n            i0.ɵɵtext(17, \"Na\\u010D\\u00EDst data \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(18, LogFilterComponent_div_18_Template, 31, 11, \"div\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"div\", 13)(20, \"div\", 14)(21, \"div\", 15)(22, \"div\", 16)(23, \"h5\", 17);\n            i0.ɵɵtext(24, \"Ulo\\u017Eit filtr\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(25, \"button\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"div\", 19)(27, \"form\", 20)(28, \"div\", 21)(29, \"label\", 22);\n            i0.ɵɵtext(30, \"N\\u00E1zev filtru\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(31, \"input\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(32, LogFilterComponent_div_32_Template, 2, 1, \"div\", 24);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(33, \"div\", 25)(34, \"button\", 26);\n            i0.ɵɵtext(35, \"Zru\\u0161it\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"button\", 27);\n            i0.ɵɵlistener(\"click\", function LogFilterComponent_Template_button_click_36_listener() {\n              return ctx.saveFilter();\n            });\n            i0.ɵɵtemplate(37, LogFilterComponent_span_37_Template, 1, 0, \"span\", 28);\n            i0.ɵɵtext(38, \" Ulo\\u017Eit \");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(8, _c1, !ctx.hasActiveFilters, ctx.hasActiveFilters));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.hasActiveFilters);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(11, _c2, ctx.isFilterVisible, !ctx.isFilterVisible));\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngIf\", ctx.isFilterVisible);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"formGroup\", ctx.saveFilterForm);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", ctx.saveFilterForm.invalid || ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          }\n        },\n        dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName],\n        styles: [\".filter-title[_ngcontent-%COMP%]{cursor:pointer;-webkit-user-select:none;user-select:none;display:flex;align-items:center}.filter-title[_ngcontent-%COMP%]:hover{color:var(--bs-primary)}.badge-smaller[_ngcontent-%COMP%]{font-size:.7rem;padding:.25em .45em}.filter-title[_ngcontent-%COMP%]   i.bi-chevron-down[_ngcontent-%COMP%], .filter-title[_ngcontent-%COMP%]   i.bi-chevron-up[_ngcontent-%COMP%]{transition:transform .2s ease-in-out}.saved-filters[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.5rem;margin-top:.5rem}.saved-filter[_ngcontent-%COMP%]{display:flex;align-items:center;background-color:rgba(var(--bs-primary-rgb),.1);border:1px solid rgba(var(--bs-primary-rgb),.2);border-radius:4px;padding:.25rem .75rem;cursor:pointer;transition:all .2s}.saved-filter[_ngcontent-%COMP%]:hover{background-color:rgba(var(--bs-primary-rgb),.2)}.filter-name[_ngcontent-%COMP%]{margin-right:.5rem}.btn-link[_ngcontent-%COMP%]{padding:0;font-size:.875rem}@media (prefers-color-scheme: dark){.saved-filter[_ngcontent-%COMP%]{background-color:rgba(var(--bs-primary-rgb),.2);border-color:rgba(var(--bs-primary-rgb),.3)}.saved-filter[_ngcontent-%COMP%]:hover{background-color:rgba(var(--bs-primary-rgb),.3)}}\"]\n      });\n    }\n  }\n  return LogFilterComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}