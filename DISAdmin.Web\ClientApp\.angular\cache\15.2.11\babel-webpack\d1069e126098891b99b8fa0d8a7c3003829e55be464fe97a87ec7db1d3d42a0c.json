{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { InstanceStatus } from '../models/instance.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../services/customer.service\";\nimport * as i4 from \"../services/instance.service\";\nimport * as i5 from \"../services/certificate.service\";\nimport * as i6 from \"../services/modal.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../shared/certificate-modal/certificate-modal.component\";\nfunction InstanceWizardComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction InstanceWizardComponent_div_28_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const customer_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", customer_r11.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(customer_r11.name);\n  }\n}\nfunction InstanceWizardComponent_div_28_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1, \" Vyberte z\\u00E1kazn\\u00EDka \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\nfunction InstanceWizardComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h5\", 30);\n    i0.ɵɵtext(2, \"Vyberte z\\u00E1kazn\\u00EDka\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"form\", 31)(4, \"div\", 30)(5, \"label\", 32);\n    i0.ɵɵtext(6, \"Z\\u00E1kazn\\u00EDk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"select\", 33)(8, \"option\", 34);\n    i0.ɵɵtext(9, \"-- Vyberte z\\u00E1kazn\\u00EDka --\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, InstanceWizardComponent_div_28_option_10_Template, 2, 2, \"option\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, InstanceWizardComponent_div_28_div_11_Template, 2, 0, \"div\", 36);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_3_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.customerForm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ((tmp_1_0 = ctx_r1.customerForm.get(\"customerId\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r1.customerForm.get(\"customerId\")) == null ? null : tmp_1_0.touched)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.customers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r1.customerForm.get(\"customerId\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r1.customerForm.get(\"customerId\")) == null ? null : tmp_3_0.touched));\n  }\n}\nfunction InstanceWizardComponent_div_29_div_8_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Zadejte n\\u00E1zev instance\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InstanceWizardComponent_div_29_div_8_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"N\\u00E1zev instance nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 200 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InstanceWizardComponent_div_29_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, InstanceWizardComponent_div_29_div_8_span_1_Template, 2, 0, \"span\", 10);\n    i0.ɵɵtemplate(2, InstanceWizardComponent_div_29_div_8_span_2_Template, 2, 0, \"span\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r12.instanceForm.get(\"name\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r12.instanceForm.get(\"name\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction InstanceWizardComponent_div_29_div_13_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Zadejte URL serveru\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InstanceWizardComponent_div_29_div_13_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"URL serveru nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 255 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InstanceWizardComponent_div_29_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, InstanceWizardComponent_div_29_div_13_span_1_Template, 2, 0, \"span\", 10);\n    i0.ɵɵtemplate(2, InstanceWizardComponent_div_29_div_13_span_2_Template, 2, 0, \"span\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r13.instanceForm.get(\"serverUrl\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r13.instanceForm.get(\"serverUrl\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"maxlength\"]);\n  }\n}\nfunction InstanceWizardComponent_div_29_div_36_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Pozn\\u00E1mky nesm\\u00ED b\\u00FDt del\\u0161\\u00ED ne\\u017E 500 znak\\u016F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InstanceWizardComponent_div_29_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, InstanceWizardComponent_div_29_div_36_span_1_Template, 2, 0, \"span\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r14.instanceForm.get(\"notes\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"maxlength\"]);\n  }\n}\nfunction InstanceWizardComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h5\", 30);\n    i0.ɵɵtext(2, \"Informace o instanci\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"form\", 31)(4, \"div\", 30)(5, \"label\", 39);\n    i0.ɵɵtext(6, \"N\\u00E1zev instance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 40);\n    i0.ɵɵtemplate(8, InstanceWizardComponent_div_29_div_8_Template, 3, 2, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 30)(10, \"label\", 41);\n    i0.ɵɵtext(11, \"URL serveru\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 42);\n    i0.ɵɵtemplate(13, InstanceWizardComponent_div_29_div_13_Template, 3, 2, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 30)(15, \"label\", 43);\n    i0.ɵɵtext(16, \"Datum expirace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 30)(19, \"label\", 45);\n    i0.ɵɵtext(20, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"select\", 46)(22, \"option\", 37);\n    i0.ɵɵtext(23, \"Aktivn\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"option\", 37);\n    i0.ɵɵtext(25, \"Trial\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"option\", 37);\n    i0.ɵɵtext(27, \"\\u00DAdr\\u017Eba\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"option\", 37);\n    i0.ɵɵtext(29, \"Blokov\\u00E1no\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"option\", 37);\n    i0.ɵɵtext(31, \"Expirov\\u00E1no\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 30)(33, \"label\", 47);\n    i0.ɵɵtext(34, \"Pozn\\u00E1mky\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(35, \"textarea\", 48);\n    i0.ɵɵtemplate(36, InstanceWizardComponent_div_29_div_36_Template, 2, 1, \"div\", 36);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_10_0;\n    let tmp_11_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.instanceForm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, ((tmp_1_0 = ctx_r2.instanceForm.get(\"name\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r2.instanceForm.get(\"name\")) == null ? null : tmp_1_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r2.instanceForm.get(\"name\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r2.instanceForm.get(\"name\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(14, _c0, ((tmp_3_0 = ctx_r2.instanceForm.get(\"serverUrl\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r2.instanceForm.get(\"serverUrl\")) == null ? null : tmp_3_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r2.instanceForm.get(\"serverUrl\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r2.instanceForm.get(\"serverUrl\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"value\", ctx_r2.InstanceStatus.Active);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r2.InstanceStatus.Trial);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r2.InstanceStatus.Maintenance);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r2.InstanceStatus.Blocked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r2.InstanceStatus.Expired);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(16, _c0, ((tmp_10_0 = ctx_r2.instanceForm.get(\"notes\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx_r2.instanceForm.get(\"notes\")) == null ? null : tmp_10_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx_r2.instanceForm.get(\"notes\")) == null ? null : tmp_11_0.invalid) && ((tmp_11_0 = ctx_r2.instanceForm.get(\"notes\")) == null ? null : tmp_11_0.touched));\n  }\n}\nfunction InstanceWizardComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h5\", 30);\n    i0.ɵɵtext(2, \"Povolen\\u00E9 moduly\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"form\", 31)(4, \"div\", 49);\n    i0.ɵɵelement(5, \"input\", 50);\n    i0.ɵɵelementStart(6, \"label\", 51);\n    i0.ɵɵtext(7, \"Reporting\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 49);\n    i0.ɵɵelement(9, \"input\", 52);\n    i0.ɵɵelementStart(10, \"label\", 53);\n    i0.ɵɵtext(11, \"Roz\\u0161\\u00ED\\u0159en\\u00E9 zabezpe\\u010Den\\u00ED\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 49);\n    i0.ɵɵelement(13, \"input\", 54);\n    i0.ɵɵelementStart(14, \"label\", 55);\n    i0.ɵɵtext(15, \"API integrace\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 49);\n    i0.ɵɵelement(17, \"input\", 56);\n    i0.ɵɵelementStart(18, \"label\", 57);\n    i0.ɵɵtext(19, \"Export dat\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 49);\n    i0.ɵɵelement(21, \"input\", 58);\n    i0.ɵɵelementStart(22, \"label\", 59);\n    i0.ɵɵtext(23, \"P\\u0159izp\\u016Fsoben\\u00ED\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.modulesForm);\n  }\n}\nfunction InstanceWizardComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h5\", 30);\n    i0.ɵɵtext(2, \"Certifik\\u00E1t\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"form\", 31)(4, \"div\", 49);\n    i0.ɵɵelement(5, \"input\", 60);\n    i0.ɵɵelementStart(6, \"label\", 61);\n    i0.ɵɵtext(7, \"Vygenerovat certifik\\u00E1t pro instanci\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 62);\n    i0.ɵɵelement(9, \"i\", 63);\n    i0.ɵɵtext(10, \" Certifik\\u00E1t bude vygenerov\\u00E1n automaticky po vytvo\\u0159en\\u00ED instance. Bude platn\\u00FD po dobu 1 roku. \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formGroup\", ctx_r4.certificateForm);\n  }\n}\nfunction InstanceWizardComponent_button_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function InstanceWizardComponent_button_33_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.previousStep());\n    });\n    i0.ɵɵelement(1, \"i\", 65);\n    i0.ɵɵtext(2, \"Zp\\u011Bt \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r5.loading);\n  }\n}\nfunction InstanceWizardComponent_button_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function InstanceWizardComponent_button_35_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.nextStep());\n    });\n    i0.ɵɵtext(1, \" Dal\\u0161\\u00ED\");\n    i0.ɵɵelement(2, \"i\", 67);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r6.loading);\n  }\n}\nfunction InstanceWizardComponent_button_36_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 70);\n  }\n}\nfunction InstanceWizardComponent_button_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function InstanceWizardComponent_button_36_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.createInstance());\n    });\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵtext(2, \"Vytvo\\u0159it instanci \");\n    i0.ɵɵtemplate(3, InstanceWizardComponent_button_36_span_3_Template, 1, 0, \"span\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r7.loading);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.loading);\n  }\n}\nfunction InstanceWizardComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 62);\n    i0.ɵɵelement(2, \"i\", 63);\n    i0.ɵɵtext(3, \" Pro instanci byl vygenerov\\u00E1n certifik\\u00E1t. M\\u016F\\u017Eete ho st\\u00E1hnout kliknut\\u00EDm na tla\\u010D\\u00EDtko n\\u00ED\\u017Ee. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function InstanceWizardComponent_div_48_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.showCertificateModal());\n    });\n    i0.ɵɵelement(5, \"i\", 73);\n    i0.ɵɵtext(6, \"Zobrazit certifik\\u00E1t \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let InstanceWizardComponent = /*#__PURE__*/(() => {\n  class InstanceWizardComponent {\n    constructor(fb, router, customerService, instanceService, certificateService, modalService) {\n      this.fb = fb;\n      this.router = router;\n      this.customerService = customerService;\n      this.instanceService = instanceService;\n      this.certificateService = certificateService;\n      this.modalService = modalService;\n      this.currentStep = 1;\n      this.totalSteps = 4;\n      this.customers = [];\n      this.loading = false;\n      this.error = null;\n      // Export enum pro použití v template\n      this.InstanceStatus = InstanceStatus;\n      this.createdInstanceId = null;\n      this.generatedCertificate = null;\n      this.customerForm = this.fb.group({\n        customerId: ['', Validators.required]\n      });\n      this.instanceForm = this.fb.group({\n        name: ['', [Validators.required, Validators.maxLength(200)]],\n        serverUrl: ['', [Validators.required, Validators.maxLength(255)]],\n        expirationDate: [''],\n        status: [InstanceStatus.Active, Validators.required],\n        notes: ['', Validators.maxLength(500)]\n      });\n      this.modulesForm = this.fb.group({\n        moduleReporting: [true],\n        moduleAdvancedSecurity: [false],\n        moduleApiIntegration: [false],\n        moduleDataExport: [false],\n        moduleCustomization: [false]\n      });\n      this.certificateForm = this.fb.group({\n        generateCertificate: [true]\n      });\n    }\n    ngOnInit() {\n      this.loadCustomers();\n    }\n    loadCustomers() {\n      this.loading = true;\n      this.customerService.getCustomers().subscribe({\n        next: customers => {\n          this.customers = customers;\n          this.loading = false;\n        },\n        error: err => {\n          console.error('Chyba při načítání zákazníků', err);\n          this.error = 'Chyba při načítání zákazníků';\n          this.loading = false;\n        }\n      });\n    }\n    nextStep() {\n      if (this.currentStep < this.totalSteps) {\n        // Validace aktuálního kroku\n        if (this.currentStep === 1 && this.customerForm.invalid) {\n          this.customerForm.markAllAsTouched();\n          return;\n        }\n        if (this.currentStep === 2 && this.instanceForm.invalid) {\n          this.instanceForm.markAllAsTouched();\n          return;\n        }\n        this.currentStep++;\n      }\n    }\n    previousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--;\n      }\n    }\n    createInstance() {\n      if (this.customerForm.invalid || this.instanceForm.invalid || this.modulesForm.invalid) {\n        return;\n      }\n      this.loading = true;\n      this.error = null;\n      const customerId = this.customerForm.value.customerId;\n      const instanceFormValue = this.instanceForm.value;\n      // Převod prázdného stringu na null pro expirationDate a správný převod status\n      const instanceData = {\n        ...instanceFormValue,\n        ...this.modulesForm.value,\n        customerId,\n        expirationDate: instanceFormValue.expirationDate && instanceFormValue.expirationDate.trim() !== '' ? new Date(instanceFormValue.expirationDate) : null,\n        status: Number(instanceFormValue.status) // Zajistíme, že status je číslo\n      };\n\n      this.instanceService.createInstance(instanceData).subscribe({\n        next: response => {\n          this.createdInstanceId = response.id;\n          if (this.certificateForm.value.generateCertificate && this.createdInstanceId) {\n            this.generateCertificate(this.createdInstanceId);\n          } else {\n            this.loading = false;\n            this.showSuccessModal();\n          }\n        },\n        error: err => {\n          console.error('Chyba při vytváření instance - detailní informace:', {\n            error: err,\n            status: err.status,\n            statusText: err.statusText,\n            message: err.error?.message || err.message,\n            url: err.url,\n            instanceData: instanceData,\n            timestamp: new Date().toISOString()\n          });\n          // Zobrazení detailnější chybové zprávy uživateli\n          let errorMessage = 'Chyba při vytváření instance';\n          if (err.error?.message) {\n            errorMessage = err.error.message;\n            // Pokud jsou k dispozici validační chyby, zobrazíme je\n            if (err.error.errors) {\n              const validationErrors = Object.keys(err.error.errors).map(key => err.error.errors[key].join(', ')).join('; ');\n              errorMessage += ': ' + validationErrors;\n            }\n          } else if (err.status === 0) {\n            errorMessage = 'Chyba připojení k serveru';\n          } else if (err.status >= 500) {\n            errorMessage = 'Chyba serveru při vytváření instance';\n          } else if (err.status === 400) {\n            errorMessage = 'Neplatná data pro vytvoření instance';\n          } else if (err.status === 404) {\n            errorMessage = 'Zákazník nebyl nalezen';\n          }\n          this.error = errorMessage;\n          this.loading = false;\n        }\n      });\n    }\n    generateCertificate(instanceId) {\n      this.certificateService.generateCertificate(instanceId).subscribe({\n        next: response => {\n          // Uložení vygenerovaného certifikátu - vytvoříme kopii, aby se zajistilo, že se data nepřepíší\n          // Zkontrolujeme, zda heslo existuje v odpovědi, jinak použijeme výchozí heslo\n          const certificatePassword = response.password || 'password';\n          this.generatedCertificate = {\n            certificate: response.certificate,\n            privateKey: response.privateKey,\n            thumbprint: response.thumbprint,\n            expirationDate: response.expirationDate,\n            password: certificatePassword,\n            certificatePassword: certificatePassword\n          };\n          // Explicitní nastavení hesla pro zobrazení v modálním okně\n          setTimeout(() => {\n            const passwordElement = document.getElementById('wizardCertificatePassword');\n            if (passwordElement && this.generatedCertificate) {\n              passwordElement.textContent = this.generatedCertificate.password || 'Heslo není k dispozici';\n            }\n          }, 100);\n          this.loading = false;\n          this.showSuccessModal();\n        },\n        error: err => {\n          console.error('Chyba při generování certifikátu', err);\n          this.error = 'Instance byla vytvořena, ale došlo k chybě při generování certifikátu';\n          this.loading = false;\n          this.showSuccessModal();\n        }\n      });\n    }\n    showSuccessModal() {\n      this.modalService.open('successModal');\n    }\n    closeSuccessModal() {\n      this.modalService.close('successModal');\n    }\n    closeCertificateModal() {\n      this.modalService.close('certificateInfoModal');\n    }\n    showCertificateModal() {\n      this.modalService.open('certificateInfoModal');\n    }\n    downloadCertificate() {\n      if (!this.generatedCertificate) {\n        return;\n      }\n      // Vytvoření a stažení souboru .pfx\n      const blob = this.base64ToBlob(this.generatedCertificate.privateKey, 'application/x-pkcs12');\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `certificate-${this.createdInstanceId}.pfx`;\n      document.body.appendChild(a);\n      a.click();\n      window.URL.revokeObjectURL(url);\n      document.body.removeChild(a);\n    }\n    /**\r\n     * Pomocná metoda pro konverzi Base64 na Blob\r\n     */\n    base64ToBlob(base64, contentType) {\n      const byteCharacters = atob(base64);\n      const byteArrays = [];\n      for (let offset = 0; offset < byteCharacters.length; offset += 512) {\n        const slice = byteCharacters.slice(offset, offset + 512);\n        const byteNumbers = new Array(slice.length);\n        for (let i = 0; i < slice.length; i++) {\n          byteNumbers[i] = slice.charCodeAt(i);\n        }\n        const byteArray = new Uint8Array(byteNumbers);\n        byteArrays.push(byteArray);\n      }\n      return new Blob(byteArrays, {\n        type: contentType\n      });\n    }\n    goToCustomers() {\n      this.closeSuccessModal();\n      this.router.navigate(['/customers']);\n    }\n    getStepClass(step) {\n      if (step === this.currentStep) {\n        return 'active';\n      } else if (step < this.currentStep) {\n        return 'completed';\n      } else {\n        return '';\n      }\n    }\n    static {\n      this.ɵfac = function InstanceWizardComponent_Factory(t) {\n        return new (t || InstanceWizardComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.CustomerService), i0.ɵɵdirectiveInject(i4.InstanceService), i0.ɵɵdirectiveInject(i5.CertificateService), i0.ɵɵdirectiveInject(i6.ModalService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: InstanceWizardComponent,\n        selectors: [[\"app-instance-wizard\"]],\n        decls: 55,\n        vars: 15,\n        consts: [[1, \"container\", \"mt-4\"], [1, \"card\"], [1, \"card-header\", \"bg-primary\", \"text-white\"], [1, \"mb-0\"], [1, \"card-body\"], [\"class\", \"alert alert-danger mb-4\", 4, \"ngIf\"], [1, \"wizard-steps\", \"mb-4\"], [1, \"step\", 3, \"ngClass\"], [1, \"step-number\"], [1, \"step-title\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\", \"mt-4\"], [\"class\", \"btn btn-secondary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary me-2\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-success\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"id\", \"successModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"successModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\", \"bg-success\", \"text-white\"], [\"id\", \"successModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\", \"btn-close-white\", 3, \"click\"], [1, \"modal-body\"], [1, \"alert\", \"alert-success\"], [1, \"bi\", \"bi-check-circle-fill\", \"me-2\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [\"modalId\", \"certificateInfoModal\", 3, \"certificate\", \"instanceName\", \"close\", \"download\"], [1, \"alert\", \"alert-danger\", \"mb-4\"], [1, \"mb-3\"], [3, \"formGroup\"], [\"for\", \"customerId\", 1, \"form-label\", \"required\"], [\"id\", \"customerId\", \"formControlName\", \"customerId\", 1, \"form-select\", 3, \"ngClass\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [3, \"value\"], [1, \"invalid-feedback\"], [\"for\", \"name\", 1, \"form-label\", \"required\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"serverUrl\", 1, \"form-label\", \"required\"], [\"type\", \"text\", \"id\", \"serverUrl\", \"formControlName\", \"serverUrl\", 1, \"form-control\", 3, \"ngClass\"], [\"for\", \"expirationDate\", 1, \"form-label\"], [\"type\", \"date\", \"id\", \"expirationDate\", \"formControlName\", \"expirationDate\", 1, \"form-control\"], [\"for\", \"status\", 1, \"form-label\"], [\"id\", \"status\", \"formControlName\", \"status\", 1, \"form-select\"], [\"for\", \"notes\", 1, \"form-label\"], [\"id\", \"notes\", \"formControlName\", \"notes\", \"rows\", \"3\", 1, \"form-control\", 3, \"ngClass\"], [1, \"mb-3\", \"form-check\"], [\"type\", \"checkbox\", \"id\", \"moduleReporting\", \"formControlName\", \"moduleReporting\", 1, \"form-check-input\"], [\"for\", \"moduleReporting\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleAdvancedSecurity\", \"formControlName\", \"moduleAdvancedSecurity\", 1, \"form-check-input\"], [\"for\", \"moduleAdvancedSecurity\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleApiIntegration\", \"formControlName\", \"moduleApiIntegration\", 1, \"form-check-input\"], [\"for\", \"moduleApiIntegration\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleDataExport\", \"formControlName\", \"moduleDataExport\", 1, \"form-check-input\"], [\"for\", \"moduleDataExport\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"moduleCustomization\", \"formControlName\", \"moduleCustomization\", 1, \"form-check-input\"], [\"for\", \"moduleCustomization\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"generateCertificate\", \"formControlName\", \"generateCertificate\", 1, \"form-check-input\"], [\"for\", \"generateCertificate\", 1, \"form-check-label\"], [1, \"alert\", \"alert-info\"], [1, \"bi\", \"bi-info-circle-fill\", \"me-2\"], [1, \"btn\", \"btn-secondary\", 3, \"disabled\", \"click\"], [1, \"bi\", \"bi-arrow-left\", \"me-2\"], [1, \"btn\", \"btn-primary\", \"me-2\", 3, \"disabled\", \"click\"], [1, \"bi\", \"bi-arrow-right\", \"ms-2\"], [1, \"btn\", \"btn-success\", 3, \"disabled\", \"click\"], [\"class\", \"spinner-border spinner-border-sm ms-2\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"ms-2\"], [1, \"mt-3\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-info-circle\", \"me-2\"]],\n        template: function InstanceWizardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h4\", 3);\n            i0.ɵɵtext(4, \"Pr\\u016Fvodce vytvo\\u0159en\\u00EDm instance DIS\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(5, \"div\", 4);\n            i0.ɵɵtemplate(6, InstanceWizardComponent_div_6_Template, 2, 1, \"div\", 5);\n            i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8);\n            i0.ɵɵtext(10, \"1\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"div\", 9);\n            i0.ɵɵtext(12, \"Z\\u00E1kazn\\u00EDk\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"div\", 7)(14, \"div\", 8);\n            i0.ɵɵtext(15, \"2\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"div\", 9);\n            i0.ɵɵtext(17, \"Instance\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(18, \"div\", 7)(19, \"div\", 8);\n            i0.ɵɵtext(20, \"3\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"div\", 9);\n            i0.ɵɵtext(22, \"Moduly\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(23, \"div\", 7)(24, \"div\", 8);\n            i0.ɵɵtext(25, \"4\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"div\", 9);\n            i0.ɵɵtext(27, \"Certifik\\u00E1t\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(28, InstanceWizardComponent_div_28_Template, 12, 6, \"div\", 10);\n            i0.ɵɵtemplate(29, InstanceWizardComponent_div_29_Template, 37, 18, \"div\", 10);\n            i0.ɵɵtemplate(30, InstanceWizardComponent_div_30_Template, 24, 1, \"div\", 10);\n            i0.ɵɵtemplate(31, InstanceWizardComponent_div_31_Template, 11, 1, \"div\", 10);\n            i0.ɵɵelementStart(32, \"div\", 11);\n            i0.ɵɵtemplate(33, InstanceWizardComponent_button_33_Template, 3, 1, \"button\", 12);\n            i0.ɵɵelementStart(34, \"div\");\n            i0.ɵɵtemplate(35, InstanceWizardComponent_button_35_Template, 3, 1, \"button\", 13);\n            i0.ɵɵtemplate(36, InstanceWizardComponent_button_36_Template, 4, 2, \"button\", 14);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(37, \"div\", 15)(38, \"div\", 16)(39, \"div\", 17)(40, \"div\", 18)(41, \"h5\", 19);\n            i0.ɵɵtext(42, \"Instance vytvo\\u0159ena\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"button\", 20);\n            i0.ɵɵlistener(\"click\", function InstanceWizardComponent_Template_button_click_43_listener() {\n              return ctx.closeSuccessModal();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(44, \"div\", 21)(45, \"div\", 22);\n            i0.ɵɵelement(46, \"i\", 23);\n            i0.ɵɵtext(47, \" Instance byla \\u00FAsp\\u011B\\u0161n\\u011B vytvo\\u0159ena. \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(48, InstanceWizardComponent_div_48_Template, 7, 0, \"div\", 24);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"div\", 25)(50, \"button\", 26);\n            i0.ɵɵlistener(\"click\", function InstanceWizardComponent_Template_button_click_50_listener() {\n              return ctx.closeSuccessModal();\n            });\n            i0.ɵɵtext(51, \"Zav\\u0159\\u00EDt\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"button\", 27);\n            i0.ɵɵlistener(\"click\", function InstanceWizardComponent_Template_button_click_52_listener() {\n              return ctx.goToCustomers();\n            });\n            i0.ɵɵtext(53, \" P\\u0159ej\\u00EDt na seznam z\\u00E1kazn\\u00EDk\\u016F \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(54, \"app-certificate-modal\", 28);\n            i0.ɵɵlistener(\"close\", function InstanceWizardComponent_Template_app_certificate_modal_close_54_listener() {\n              return ctx.closeCertificateModal();\n            })(\"download\", function InstanceWizardComponent_Template_app_certificate_modal_download_54_listener() {\n              return ctx.downloadCertificate();\n            });\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngClass\", ctx.getStepClass(1));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngClass\", ctx.getStepClass(2));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngClass\", ctx.getStepClass(3));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngClass\", ctx.getStepClass(4));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 4);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentStep < ctx.totalSteps);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentStep === ctx.totalSteps);\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"ngIf\", ctx.generatedCertificate);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"certificate\", ctx.generatedCertificate)(\"instanceName\", ctx.instanceForm.value.name || \"\");\n          }\n        },\n        dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i8.CertificateModalComponent],\n        styles: [\".wizard-steps[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:2rem;position:relative}.wizard-steps[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:20px;left:0;right:0;height:2px;background:#e9ecef;z-index:0}.step[_ngcontent-%COMP%]{position:relative;z-index:1;display:flex;flex-direction:column;align-items:center;flex:1}.step-number[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background-color:#e9ecef;color:#6c757d;display:flex;align-items:center;justify-content:center;font-weight:700;margin-bottom:.5rem;border:2px solid #e9ecef}.step-title[_ngcontent-%COMP%]{font-size:.875rem;color:#6c757d}.step.active[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%]{background-color:#0d6efd;color:#fff;border-color:#0d6efd}.step.active[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]{color:#0d6efd;font-weight:700}.step.completed[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%]{background-color:#198754;color:#fff;border-color:#198754}.step.completed[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]{color:#198754}.card[_ngcontent-%COMP%]{border-radius:.5rem;box-shadow:0 .125rem .25rem #00000013;overflow:hidden}.card-header[_ngcontent-%COMP%]{border-bottom:none}.form-check[_ngcontent-%COMP%]{margin-bottom:1rem}.form-check-input[_ngcontent-%COMP%]:checked{background-color:#0d6efd;border-color:#0d6efd}.btn-primary[_ngcontent-%COMP%]{background-color:#0d6efd;border-color:#0d6efd}.btn-success[_ngcontent-%COMP%]{background-color:#198754;border-color:#198754}.btn-secondary[_ngcontent-%COMP%]{background-color:#6c757d;border-color:#6c757d}.alert-info[_ngcontent-%COMP%]{background-color:#cff4fc;border-color:#b6effb;color:#055160}.alert-success[_ngcontent-%COMP%]{background-color:#d1e7dd;border-color:#badbcc;color:#0f5132}.list-group-item[_ngcontent-%COMP%]{border-color:#dee2e6}@media (prefers-color-scheme: dark){.wizard-steps[_ngcontent-%COMP%]:before{background:#495057}.step-number[_ngcontent-%COMP%]{background-color:#495057;color:#e9ecef;border-color:#495057}.step-title[_ngcontent-%COMP%]{color:#e9ecef}.step.active[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%]{background-color:#0d6efd;border-color:#0d6efd}.step.active[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]{color:#0d6efd}.step.completed[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%]{background-color:#198754;border-color:#198754}.step.completed[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]{color:#198754}.card[_ngcontent-%COMP%]{background-color:#2b3035;border-color:#373b3e}.form-control[_ngcontent-%COMP%], .form-select[_ngcontent-%COMP%]{background-color:#212529;border-color:#495057;color:#e9ecef}.form-control[_ngcontent-%COMP%]:focus, .form-select[_ngcontent-%COMP%]:focus{background-color:#2b3035;color:#e9ecef}.alert-info[_ngcontent-%COMP%]{background-color:#0d3b66;border-color:#0d3b66;color:#e9ecef}.alert-success[_ngcontent-%COMP%]{background-color:#0f5132;border-color:#0f5132;color:#e9ecef}.list-group-item[_ngcontent-%COMP%]{background-color:#2b3035;border-color:#373b3e;color:#e9ecef}.text-muted[_ngcontent-%COMP%]{color:#adb5bd!important}}\"]\n      });\n    }\n  }\n  return InstanceWizardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}