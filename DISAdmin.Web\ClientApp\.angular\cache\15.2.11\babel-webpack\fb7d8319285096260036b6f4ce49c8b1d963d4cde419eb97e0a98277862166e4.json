{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/VSCodeProjects/DISAdminAugment/DISAdmin.Web/ClientApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { ActivityTypeHelper, LogSourceHelper, LogSource, ApplicationLogLevelHelper } from '../../models/logs.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/logs.service\";\nimport * as i3 from \"../../services/user.service\";\nimport * as i4 from \"../../services/modal.service\";\nimport * as i5 from \"ngx-toastr\";\nimport * as i6 from \"@angular/common\";\nfunction LogFilterComponent_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.activeFilterCount);\n  }\n}\nfunction LogFilterComponent_div_18_option_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r12.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(user_r12.username);\n  }\n}\nfunction LogFilterComponent_div_18_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const source_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", source_r13.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(source_r13.label);\n  }\n}\nfunction LogFilterComponent_div_18_ng_container_17_option_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r15.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(type_r15.label);\n  }\n}\nfunction LogFilterComponent_div_18_ng_container_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 32)(2, \"label\", 48);\n    i0.ɵɵtext(3, \"Typ aktivity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"select\", 49)(5, \"option\", 35);\n    i0.ɵɵtext(6, \"V\\u0161echny typy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, LogFilterComponent_div_18_ng_container_17_option_7_Template, 2, 2, \"option\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 32)(9, \"label\", 50);\n    i0.ɵɵtext(10, \"Entita\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"input\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 32)(13, \"label\", 52);\n    i0.ɵɵtext(14, \"ID entity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngValue\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.activityTypes);\n  }\n}\nfunction LogFilterComponent_div_18_ng_container_18_div_1_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const level_r19 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", level_r19.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(level_r19.label);\n  }\n}\nfunction LogFilterComponent_div_18_ng_container_18_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\", 54);\n    i0.ɵɵtext(2, \"\\u00DArove\\u0148 logov\\u00E1n\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"select\", 55)(4, \"option\", 35);\n    i0.ɵɵtext(5, \"V\\u0161echny \\u00FArovn\\u011B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, LogFilterComponent_div_18_ng_container_18_div_1_option_6_Template, 2, 2, \"option\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngValue\", null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.logLevels);\n  }\n}\nfunction LogFilterComponent_div_18_ng_container_18_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\", 56);\n    i0.ɵɵtext(2, \"Kategorie\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 57);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LogFilterComponent_div_18_ng_container_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LogFilterComponent_div_18_ng_container_18_div_1_Template, 7, 2, \"div\", 45);\n    i0.ɵɵtemplate(2, LogFilterComponent_div_18_ng_container_18_div_2_Template, 4, 0, \"div\", 45);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.showLogLevelFilter);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.showCategoryFilter);\n  }\n}\nfunction LogFilterComponent_div_18_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r20.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(option_r20.label);\n  }\n}\nfunction LogFilterComponent_div_18_div_28_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1, \" Zadejte po\\u010D\\u00E1te\\u010Dn\\u00ED datum \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\nfunction LogFilterComponent_div_18_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\", 58);\n    i0.ɵɵtext(2, \"Od\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 59);\n    i0.ɵɵtemplate(4, LogFilterComponent_div_18_div_28_div_4_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ((tmp_0_0 = ctx_r9.filterForm.get(\"fromDate\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r9.filterForm.get(\"fromDate\")) == null ? null : tmp_0_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r9.filterForm.get(\"fromDate\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"required\"]);\n  }\n}\nfunction LogFilterComponent_div_18_div_29_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1, \" Zadejte koncov\\u00E9 datum \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LogFilterComponent_div_18_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\", 62);\n    i0.ɵɵtext(2, \"Do\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 63);\n    i0.ɵɵtemplate(4, LogFilterComponent_div_18_div_29_div_4_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ((tmp_0_0 = ctx_r10.filterForm.get(\"toDate\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r10.filterForm.get(\"toDate\")) == null ? null : tmp_0_0.touched)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r10.filterForm.get(\"toDate\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"required\"]);\n  }\n}\nfunction LogFilterComponent_div_18_div_30_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵlistener(\"click\", function LogFilterComponent_div_18_div_30_div_4_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r26);\n      const filter_r24 = restoredCtx.$implicit;\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r25.applySavedFilter(filter_r24));\n    });\n    i0.ɵɵelementStart(1, \"span\", 68);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function LogFilterComponent_div_18_div_30_div_4_Template_button_click_3_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r26);\n      const filter_r24 = restoredCtx.$implicit;\n      const ctx_r27 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r27.deleteSavedFilter(filter_r24, $event));\n    });\n    i0.ɵɵelement(4, \"i\", 70);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const filter_r24 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(filter_r24.name);\n  }\n}\nfunction LogFilterComponent_div_18_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"h6\");\n    i0.ɵɵtext(2, \"Ulo\\u017Een\\u00E9 filtry\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 65);\n    i0.ɵɵtemplate(4, LogFilterComponent_div_18_div_30_div_4_Template, 5, 1, \"div\", 66);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r11.savedFilters);\n  }\n}\nfunction LogFilterComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"form\", 20)(2, \"div\", 31)(3, \"div\", 32)(4, \"label\", 33);\n    i0.ɵɵtext(5, \"U\\u017Eivatel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"select\", 34)(7, \"option\", 35);\n    i0.ɵɵtext(8, \"V\\u0161ichni u\\u017Eivatel\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, LogFilterComponent_div_18_option_9_Template, 2, 2, \"option\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 32)(11, \"label\", 37);\n    i0.ɵɵtext(12, \"Zdroj\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"select\", 38)(14, \"option\", 35);\n    i0.ɵɵtext(15, \"V\\u0161echny zdroje\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, LogFilterComponent_div_18_option_16_Template, 2, 2, \"option\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(17, LogFilterComponent_div_18_ng_container_17_Template, 16, 2, \"ng-container\", 40);\n    i0.ɵɵtemplate(18, LogFilterComponent_div_18_ng_container_18_Template, 3, 2, \"ng-container\", 40);\n    i0.ɵɵelementStart(19, \"div\", 32)(20, \"label\", 41);\n    i0.ɵɵtext(21, \"Po\\u010Det z\\u00E1znam\\u016F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"input\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 32)(24, \"label\", 43);\n    i0.ɵɵtext(25, \"\\u010Casov\\u00E9 obdob\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"select\", 44);\n    i0.ɵɵtemplate(27, LogFilterComponent_div_18_option_27_Template, 2, 2, \"option\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(28, LogFilterComponent_div_18_div_28_Template, 5, 4, \"div\", 45);\n    i0.ɵɵtemplate(29, LogFilterComponent_div_18_div_29_Template, 5, 4, \"div\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(30, LogFilterComponent_div_18_div_30_Template, 5, 1, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_8_0;\n    let tmp_9_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.filterForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngValue\", null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.users);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngValue\", null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.logSources);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.logType === \"activity\" || ctx_r1.logType === \"api\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.logType === \"error\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.periodOptions);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx_r1.filterForm.get(\"period\")) == null ? null : tmp_8_0.value) === \"custom\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx_r1.filterForm.get(\"period\")) == null ? null : tmp_9_0.value) === \"custom\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.savedFilters.length > 0);\n  }\n}\nfunction LogFilterComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.error);\n  }\n}\nfunction LogFilterComponent_span_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 72);\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"bi-funnel\": a0,\n    \"bi-funnel-fill text-primary\": a1\n  };\n};\nconst _c2 = function (a0, a1) {\n  return {\n    \"bi-chevron-up\": a0,\n    \"bi-chevron-down\": a1\n  };\n};\nexport class LogFilterComponent {\n  constructor(fb, logsService, userService, modalService, toastr) {\n    this.fb = fb;\n    this.logsService = logsService;\n    this.userService = userService;\n    this.modalService = modalService;\n    this.toastr = toastr;\n    this.logType = 'activity'; // activity, error, api\n    this.showLogLevelFilter = false; // Zobrazit filtr úrovně logování\n    this.showCategoryFilter = false; // Zobrazit filtr kategorie\n    this.filterChange = new EventEmitter();\n    this.savedFilters = [];\n    this.users = [];\n    this.activityTypes = ActivityTypeHelper.getActivityTypes();\n    this.logSources = LogSourceHelper.getLogSources();\n    this.logLevels = ApplicationLogLevelHelper.getLogLevels();\n    this.isLoading = false;\n    this.error = null;\n    // Vlastnosti pro skrývání/zobrazování filtru\n    this.isFilterVisible = false; // Filtr je ve výchozím stavu skrytý\n    this.hasActiveFilters = false;\n    this.activeFilterCount = 0;\n    // Časová období pro dropdown\n    this.periodOptions = [{\n      value: 1,\n      label: '1 den'\n    }, {\n      value: 7,\n      label: '7 dní'\n    }, {\n      value: 30,\n      label: '30 dní'\n    }, {\n      value: 90,\n      label: '90 dní'\n    }, {\n      value: 'custom',\n      label: 'Vlastní období'\n    }];\n    this.filterForm = this.createFilterForm();\n    this.saveFilterForm = this.fb.group({\n      name: ['']\n    });\n  }\n  ngOnInit() {\n    this.loadUsers();\n    this.loadSavedFilters();\n    this.loadFilterVisibilityState();\n    this.loadCurrentFilterState();\n    // Automatické aplikování filtru při změně formuláře\n    this.filterForm.valueChanges.subscribe(() => {\n      this.updateActiveFiltersIndicator();\n      this.applyFilter();\n    });\n    // Sledování změny období pro zobrazení/skrytí polí fromDate a toDate\n    this.filterForm.get('period')?.valueChanges.subscribe(period => {\n      this.updateDateFieldsValidation(period);\n    });\n  }\n  /**\r\n   * Aktualizace validace datumových polí podle vybraného období\r\n   */\n  updateDateFieldsValidation(period) {\n    const fromDateControl = this.filterForm.get('fromDate');\n    const toDateControl = this.filterForm.get('toDate');\n    if (period === 'custom') {\n      // Při vlastním období jsou pole povinná\n      fromDateControl?.setValidators([Validators.required]);\n      toDateControl?.setValidators([Validators.required]);\n    } else {\n      // Při přednastaveném období nejsou pole povinná\n      fromDateControl?.clearValidators();\n      toDateControl?.clearValidators();\n    }\n    fromDateControl?.updateValueAndValidity();\n    toDateControl?.updateValueAndValidity();\n  }\n  /**\r\n   * Načtení aktuálního stavu filtru z localStorage\r\n   */\n  loadCurrentFilterState() {\n    try {\n      const filterKey = `current_filter_${this.logType}`;\n      const filterJson = localStorage.getItem(filterKey);\n      if (filterJson) {\n        const savedFilter = JSON.parse(filterJson);\n        // Detekce starého formátu filtru (bez pole period)\n        if (savedFilter.fromDate && savedFilter.toDate && !savedFilter.period) {\n          // Pokusíme se detekovat období podle rozdílu dat\n          const fromDate = new Date(savedFilter.fromDate);\n          const toDate = new Date(savedFilter.toDate);\n          const diffDays = Math.round((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));\n          // Pokud je rozdíl blízký některému z předdefinovaných období, použijeme ho\n          if (diffDays <= 1) {\n            savedFilter.period = 1;\n          } else if (diffDays <= 7) {\n            savedFilter.period = 7;\n          } else if (diffDays <= 30) {\n            savedFilter.period = 30;\n          } else if (diffDays <= 90) {\n            savedFilter.period = 90;\n          } else {\n            savedFilter.period = 'custom';\n          }\n        }\n        // Převod objektů Date na řetězce pro input\n        if (savedFilter.fromDate) {\n          const fromDate = new Date(savedFilter.fromDate);\n          savedFilter.fromDate = this.formatDateForInput(fromDate);\n        }\n        if (savedFilter.toDate) {\n          const toDate = new Date(savedFilter.toDate);\n          savedFilter.toDate = this.formatDateForInput(toDate);\n        }\n        // Nastavení hodnot formuláře\n        this.filterForm.patchValue(savedFilter);\n        // Aktualizace validace datumových polí\n        this.updateDateFieldsValidation(savedFilter.period);\n        // Aktualizace indikátoru aktivních filtrů\n        this.updateActiveFiltersIndicator();\n      }\n    } catch (error) {\n      console.error(`Chyba při načítání stavu filtru pro ${this.logType}`, error);\n    }\n  }\n  /**\r\n   * Přepne viditelnost filtru a uloží stav do localStorage\r\n   */\n  toggleFilterVisibility() {\n    this.isFilterVisible = !this.isFilterVisible;\n    // Uložení stavu viditelnosti do localStorage\n    try {\n      const visibilityKey = `filter_visibility_logs_${this.logType}`;\n      localStorage.setItem(visibilityKey, this.isFilterVisible.toString());\n    } catch (error) {\n      console.error('Chyba při ukládání stavu viditelnosti filtru do localStorage', error);\n    }\n  }\n  /**\r\n   * Načte stav viditelnosti filtru z localStorage\r\n   */\n  loadFilterVisibilityState() {\n    try {\n      const visibilityKey = `filter_visibility_logs_${this.logType}`;\n      const savedVisibility = localStorage.getItem(visibilityKey);\n      if (savedVisibility !== null) {\n        this.isFilterVisible = savedVisibility === 'true';\n      }\n    } catch (error) {\n      console.error('Chyba při načítání stavu viditelnosti filtru z localStorage', error);\n    }\n  }\n  /**\r\n   * Aktualizuje indikátor aktivních filtrů\r\n   */\n  updateActiveFiltersIndicator() {\n    const values = this.filterForm.value;\n    // Získání výchozích hodnot pro časové filtry\n    const today = new Date();\n    const endOfDay = new Date(today);\n    endOfDay.setHours(23, 59, 59, 999);\n    const sevenDaysAgo = new Date(today);\n    sevenDaysAgo.setDate(today.getDate() - 7);\n    sevenDaysAgo.setHours(0, 0, 0, 0);\n    // Formátování dat pro porovnání\n    const defaultFromDateStr = this.formatDateForInput(sevenDaysAgo);\n    const defaultToDateStr = this.formatDateForInput(endOfDay);\n    // Výchozí hodnota pro období\n    const defaultPeriod = 7;\n    // Odstranění prázdných hodnot a výchozích hodnot\n    const activeFilters = Object.keys(values).filter(key => {\n      // Ignorujeme maxResults s hodnotou 100, což je výchozí hodnota\n      if (key === 'maxResults' && values[key] === 100) {\n        return false;\n      }\n      // Ignorujeme source s výchozí hodnotou podle typu logu\n      if (key === 'source') {\n        if (this.logType === 'activity' && values[key] === LogSource.DISAdmin) {\n          return false;\n        }\n        if (this.logType === 'api' && values[key] === LogSource.DISApi) {\n          return false;\n        }\n      }\n      // Ignorujeme výchozí hodnotu pro období\n      if (key === 'period' && values[key] === defaultPeriod) {\n        return false;\n      }\n      // Při přednastaveném období ignorujeme fromDate a toDate\n      if (values.period !== 'custom' && (key === 'fromDate' || key === 'toDate')) {\n        return false;\n      }\n      // Při vlastním období kontrolujeme výchozí hodnoty\n      if (values.period === 'custom') {\n        if (key === 'fromDate' && values[key] === defaultFromDateStr) {\n          return false;\n        }\n        if (key === 'toDate' && values[key] === defaultToDateStr) {\n          return false;\n        }\n      }\n      // Ignorujeme null, prázdné řetězce a undefined\n      return values[key] !== null && values[key] !== '' && values[key] !== undefined;\n    }).reduce((obj, key) => {\n      obj[key] = values[key];\n      return obj;\n    }, {});\n    this.activeFilterCount = Object.keys(activeFilters).length;\n    this.hasActiveFilters = this.activeFilterCount > 0;\n    // Výpis aktivních filtrů do konzole pro ladění\n    if (this.hasActiveFilters) {\n      console.log('Aktivní filtry:', activeFilters);\n    }\n  }\n  /**\r\n   * Vytvoření formuláře pro filtrování\r\n   */\n  createFilterForm() {\n    // Nastavení výchozích hodnot pro časové filtry\n    const today = new Date();\n    const endOfDay = new Date(today);\n    endOfDay.setHours(23, 59, 59, 999);\n    const sevenDaysAgo = new Date(today);\n    sevenDaysAgo.setDate(today.getDate() - 7);\n    sevenDaysAgo.setHours(0, 0, 0, 0);\n    // Formátování dat pro input typu datetime-local\n    const fromDateStr = this.formatDateForInput(sevenDaysAgo);\n    const toDateStr = this.formatDateForInput(endOfDay);\n    // Základní pole pro všechny typy logů\n    const formControls = {\n      period: [7],\n      fromDate: [fromDateStr],\n      toDate: [toDateStr],\n      userId: [null],\n      maxResults: [100],\n      source: [null]\n    };\n    // Nastavení výchozí hodnoty pro zdroj podle typu logu\n    if (this.logType === 'activity') {\n      formControls.source = [LogSource.DISAdmin];\n    } else if (this.logType === 'api') {\n      formControls.source = [LogSource.DISApi];\n    }\n    // Přidání specifických polí podle typu logu\n    if (this.logType === 'activity' || this.logType === 'api') {\n      formControls.entityName = [''];\n      formControls.entityId = [null];\n      formControls.activityType = [''];\n    }\n    // Přidání polí pro error logy\n    if (this.logType === 'error') {\n      formControls.logLevel = [null];\n      formControls.category = [''];\n    }\n    return this.fb.group(formControls);\n  }\n  /**\r\n   * Načtení uživatelů pro filtr\r\n   */\n  loadUsers() {\n    this.userService.getUsers().subscribe({\n      next: users => {\n        this.users = users;\n      },\n      error: err => {\n        console.error('Chyba při načítání uživatelů', err);\n      }\n    });\n  }\n  /**\r\n   * Načtení uložených filtrů\r\n   */\n  loadSavedFilters() {\n    this.logsService.getSavedLogFilters(this.logType).subscribe({\n      next: filters => {\n        this.savedFilters = filters;\n      },\n      error: err => {\n        console.error('Chyba při načítání uložených filtrů', err);\n      }\n    });\n  }\n  /**\r\n   * Aplikování filtru\r\n   */\n  applyFilter() {\n    const formValues = this.filterForm.value;\n    const filterData = {\n      ...formValues\n    };\n    // Zpracování časového období\n    if (formValues.period !== 'custom') {\n      // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n      const today = new Date();\n      const endOfDay = new Date(today);\n      endOfDay.setHours(23, 59, 59, 999);\n      const fromDate = new Date(today);\n      fromDate.setDate(today.getDate() - Number(formValues.period));\n      fromDate.setHours(0, 0, 0, 0);\n      filterData.fromDate = fromDate;\n      filterData.toDate = endOfDay;\n    } else {\n      // Při vlastním období použijeme zadané hodnoty\n      if (formValues.fromDate) {\n        filterData.fromDate = new Date(formValues.fromDate);\n      }\n      if (formValues.toDate) {\n        filterData.toDate = new Date(formValues.toDate);\n      }\n    }\n    // Uložení stavu filtru do localStorage\n    this.saveFilterState(filterData);\n    this.filterChange.emit(filterData);\n  }\n  /**\r\n   * Načtení dat podle aktuálního filtru\r\n   */\n  refreshData() {\n    // Aplikujeme aktuální filtr znovu\n    this.applyFilter();\n  }\n  /**\r\n   * Uložení stavu filtru do localStorage\r\n   */\n  saveFilterState(filterData) {\n    try {\n      const filterKey = `current_filter_${this.logType}`;\n      localStorage.setItem(filterKey, JSON.stringify(filterData));\n    } catch (error) {\n      console.error(`Chyba při ukládání stavu filtru pro ${this.logType}`, error);\n    }\n  }\n  /**\r\n   * Resetování filtru\r\n   */\n  resetFilter() {\n    // Nastavení výchozích hodnot pro časové filtry\n    const today = new Date();\n    const endOfDay = new Date(today);\n    endOfDay.setHours(23, 59, 59, 999);\n    const sevenDaysAgo = new Date(today);\n    sevenDaysAgo.setDate(today.getDate() - 7);\n    sevenDaysAgo.setHours(0, 0, 0, 0);\n    // Formátování dat pro input typu datetime-local\n    const fromDateStr = this.formatDateForInput(sevenDaysAgo);\n    const toDateStr = this.formatDateForInput(endOfDay);\n    // Základní hodnoty pro reset\n    const resetValues = {\n      period: 7,\n      fromDate: fromDateStr,\n      toDate: toDateStr,\n      maxResults: 100\n    };\n    // Nastavení výchozí hodnoty pro zdroj podle typu logu\n    if (this.logType === 'activity') {\n      resetValues.source = LogSource.DISAdmin;\n    } else if (this.logType === 'api') {\n      resetValues.source = LogSource.DISApi;\n    }\n    this.filterForm.reset(resetValues);\n    this.updateActiveFiltersIndicator();\n    this.applyFilter();\n    // Zobrazení notifikace o resetování filtru\n    this.toastr.info('Filtr byl resetován', 'Reset');\n  }\n  /**\r\n   * Formátuje datum pro input typu datetime-local\r\n   * Format: YYYY-MM-DDThh:mm\r\n   */\n  formatDateForInput(date) {\n    const year = date.getFullYear();\n    const month = (date.getMonth() + 1).toString().padStart(2, '0');\n    const day = date.getDate().toString().padStart(2, '0');\n    const hours = date.getHours().toString().padStart(2, '0');\n    const minutes = date.getMinutes().toString().padStart(2, '0');\n    return `${year}-${month}-${day}T${hours}:${minutes}`;\n  }\n  /**\r\n   * Otevření modálního okna pro uložení filtru\r\n   */\n  openSaveFilterModal() {\n    this.saveFilterForm.reset();\n    this.modalService.open('saveFilterModal');\n  }\n  /**\r\n   * Uložení filtru\r\n   */\n  saveFilter() {\n    if (this.saveFilterForm.invalid) {\n      return;\n    }\n    const filterName = this.saveFilterForm.get('name')?.value;\n    const filterData = this.filterForm.value;\n    const saveRequest = {\n      name: filterName,\n      logType: this.logType,\n      filterData: JSON.stringify(filterData)\n    };\n    this.isLoading = true;\n    this.logsService.saveLogFilter(saveRequest).subscribe({\n      next: response => {\n        this.savedFilters.push(response);\n        this.modalService.close('saveFilterModal');\n        this.isLoading = false;\n      },\n      error: err => {\n        console.error('Chyba při ukládání filtru', err);\n        this.error = 'Nepodařilo se uložit filtr';\n        this.isLoading = false;\n      }\n    });\n  }\n  /**\r\n   * Aplikování uloženého filtru\r\n   */\n  applySavedFilter(filter) {\n    try {\n      const parsedData = JSON.parse(filter.filterData);\n      // Vytvoříme nový objekt pro formulář s řetězcovými hodnotami pro datetime-local inputy\n      const formValues = {\n        ...parsedData\n      };\n      // Detekce starého formátu filtru (bez pole period)\n      if (parsedData.fromDate && parsedData.toDate && !parsedData.period) {\n        // Pokusíme se detekovat období podle rozdílu dat\n        const fromDate = new Date(parsedData.fromDate);\n        const toDate = new Date(parsedData.toDate);\n        const diffDays = Math.round((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));\n        // Pokud je rozdíl blízký některému z předdefinovaných období, použijeme ho\n        if (diffDays <= 1) {\n          formValues.period = 1;\n        } else if (diffDays <= 7) {\n          formValues.period = 7;\n        } else if (diffDays <= 30) {\n          formValues.period = 30;\n        } else if (diffDays <= 90) {\n          formValues.period = 90;\n        } else {\n          formValues.period = 'custom';\n        }\n      }\n      // Převod objektů Date na řetězce pro input\n      if (parsedData.fromDate) {\n        const fromDate = new Date(parsedData.fromDate);\n        formValues.fromDate = this.formatDateForInput(fromDate);\n      }\n      if (parsedData.toDate) {\n        const toDate = new Date(parsedData.toDate);\n        formValues.toDate = this.formatDateForInput(toDate);\n      }\n      // Resetujeme formulář, aby se vyčistily všechny hodnoty\n      this.filterForm.reset({\n        maxResults: 100\n      });\n      // Nastavíme hodnoty formuláře\n      this.filterForm.patchValue(formValues);\n      // Zajistíme, že zdroj je správně nastaven podle typu logu\n      if (this.logType === 'activity') {\n        this.filterForm.patchValue({\n          source: LogSource.DISAdmin\n        });\n      } else if (this.logType === 'api') {\n        this.filterForm.patchValue({\n          source: LogSource.DISApi\n        });\n      }\n      // Aktualizace validace datumových polí\n      this.updateDateFieldsValidation(formValues.period);\n      // Aktualizujeme indikátor aktivních filtrů\n      this.updateActiveFiltersIndicator();\n      // Aplikujeme filtr, který převede řetězce zpět na Date objekty\n      this.applyFilter();\n    } catch (error) {\n      console.error('Chyba při aplikování uloženého filtru', error);\n    }\n  }\n  /**\r\n   * Odstranění uloženého filtru\r\n   */\n  deleteSavedFilter(filter, event) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation(); // Zabránění aplikování filtru při kliknutí na tlačítko smazat\n      // Použijeme vlastní dialog pro potvrzení místo nativního confirm\n      const confirmed = yield _this.modalService.confirm(`Opravdu chcete smazat filtr \"${filter.name}\"?`, 'Odstranit filtr', 'OK', 'Zrušit', 'btn-danger', 'btn-secondary');\n      if (confirmed) {\n        _this.logsService.deleteLogFilter(filter.id).subscribe({\n          next: () => {\n            _this.savedFilters = _this.savedFilters.filter(f => f.id !== filter.id);\n          },\n          error: err => {\n            console.error('Chyba při mazání filtru', err);\n          }\n        });\n      }\n    })();\n  }\n  static {\n    this.ɵfac = function LogFilterComponent_Factory(t) {\n      return new (t || LogFilterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.LogsService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.ModalService), i0.ɵɵdirectiveInject(i5.ToastrService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LogFilterComponent,\n      selectors: [[\"app-log-filter\"]],\n      inputs: {\n        logType: \"logType\",\n        showLogLevelFilter: \"showLogLevelFilter\",\n        showCategoryFilter: \"showCategoryFilter\"\n      },\n      outputs: {\n        filterChange: \"filterChange\"\n      },\n      decls: 39,\n      vars: 14,\n      consts: [[1, \"card\", \"mb-4\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\", \"filter-title\", 3, \"click\"], [1, \"bi\", \"fs-5\", \"me-2\", 3, \"ngClass\"], [\"class\", \"badge bg-danger ms-2 badge-smaller\", 4, \"ngIf\"], [1, \"bi\", \"ms-2\", 3, \"ngClass\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", \"me-4\", 3, \"click\"], [1, \"bi\", \"bi-save\", \"me-1\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"me-2\", 3, \"click\"], [1, \"bi\", \"bi-arrow-counterclockwise\", \"me-1\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-repeat\", \"me-1\"], [\"class\", \"card-body\", 4, \"ngIf\"], [\"id\", \"saveFilterModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"saveFilterModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"saveFilterModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\"], [1, \"modal-body\"], [3, \"formGroup\"], [1, \"mb-3\"], [\"for\", \"filterName\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"filterName\", \"formControlName\", \"name\", \"required\", \"\", 1, \"form-control\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [\"class\", \"spinner-border spinner-border-sm me-1\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [1, \"badge\", \"bg-danger\", \"ms-2\", \"badge-smaller\"], [1, \"card-body\"], [1, \"row\"], [1, \"col-md-6\", \"col-lg-3\", \"mb-3\"], [\"for\", \"userId\", 1, \"form-label\"], [\"id\", \"userId\", \"formControlName\", \"userId\", 1, \"form-select\"], [3, \"ngValue\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"source\", 1, \"form-label\"], [\"id\", \"source\", \"formControlName\", \"source\", 1, \"form-select\"], [3, \"ngValue\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"for\", \"maxResults\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"maxResults\", \"formControlName\", \"maxResults\", \"min\", \"1\", \"max\", \"1000\", 1, \"form-control\"], [\"for\", \"period\", 1, \"form-label\"], [\"id\", \"period\", \"formControlName\", \"period\", 1, \"form-select\"], [\"class\", \"col-md-6 col-lg-3 mb-3\", 4, \"ngIf\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [3, \"value\"], [\"for\", \"activityType\", 1, \"form-label\"], [\"id\", \"activityType\", \"formControlName\", \"activityType\", 1, \"form-select\"], [\"for\", \"entityName\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"entityName\", \"formControlName\", \"entityName\", \"placeholder\", \"Nap\\u0159. customers, versions...\", 1, \"form-control\"], [\"for\", \"entityId\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"entityId\", \"formControlName\", \"entityId\", 1, \"form-control\"], [\"for\", \"logLevel\", 1, \"form-label\"], [\"id\", \"logLevel\", \"formControlName\", \"logLevel\", 1, \"form-select\"], [\"for\", \"category\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"category\", \"formControlName\", \"category\", \"placeholder\", \"Nap\\u0159. DISAdmin.Api.Controllers...\", 1, \"form-control\"], [\"for\", \"fromDate\", 1, \"form-label\"], [\"type\", \"datetime-local\", \"id\", \"fromDate\", \"formControlName\", \"fromDate\", 1, \"form-control\", 3, \"ngClass\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [1, \"invalid-feedback\"], [\"for\", \"toDate\", 1, \"form-label\"], [\"type\", \"datetime-local\", \"id\", \"toDate\", \"formControlName\", \"toDate\", 1, \"form-control\", 3, \"ngClass\"], [1, \"mt-3\"], [1, \"saved-filters\"], [\"class\", \"saved-filter\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"saved-filter\", 3, \"click\"], [1, \"filter-name\"], [1, \"btn\", \"btn-sm\", \"btn-link\", \"text-danger\", 3, \"click\"], [1, \"bi\", \"bi-trash\"], [1, \"alert\", \"alert-danger\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-1\"]],\n      template: function LogFilterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h5\", 2);\n          i0.ɵɵlistener(\"click\", function LogFilterComponent_Template_h5_click_2_listener() {\n            return ctx.toggleFilterVisibility();\n          });\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵelementStart(4, \"span\");\n          i0.ɵɵtext(5, \"Filtr\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, LogFilterComponent_span_6_Template, 2, 1, \"span\", 4);\n          i0.ɵɵelement(7, \"i\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\")(9, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function LogFilterComponent_Template_button_click_9_listener() {\n            return ctx.openSaveFilterModal();\n          });\n          i0.ɵɵelement(10, \"i\", 7);\n          i0.ɵɵtext(11, \"Ulo\\u017Eit filtr \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function LogFilterComponent_Template_button_click_12_listener() {\n            return ctx.resetFilter();\n          });\n          i0.ɵɵelement(13, \"i\", 9);\n          i0.ɵɵtext(14, \"Reset \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function LogFilterComponent_Template_button_click_15_listener() {\n            return ctx.refreshData();\n          });\n          i0.ɵɵelement(16, \"i\", 11);\n          i0.ɵɵtext(17, \"Na\\u010D\\u00EDst data \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(18, LogFilterComponent_div_18_Template, 31, 11, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 13)(20, \"div\", 14)(21, \"div\", 15)(22, \"div\", 16)(23, \"h5\", 17);\n          i0.ɵɵtext(24, \"Ulo\\u017Eit filtr\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(25, \"button\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 19)(27, \"form\", 20)(28, \"div\", 21)(29, \"label\", 22);\n          i0.ɵɵtext(30, \"N\\u00E1zev filtru\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(31, \"input\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, LogFilterComponent_div_32_Template, 2, 1, \"div\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 25)(34, \"button\", 26);\n          i0.ɵɵtext(35, \"Zru\\u0161it\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function LogFilterComponent_Template_button_click_36_listener() {\n            return ctx.saveFilter();\n          });\n          i0.ɵɵtemplate(37, LogFilterComponent_span_37_Template, 1, 0, \"span\", 28);\n          i0.ɵɵtext(38, \" Ulo\\u017Eit \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(8, _c1, !ctx.hasActiveFilters, ctx.hasActiveFilters));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasActiveFilters);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(11, _c2, ctx.isFilterVisible, !ctx.isFilterVisible));\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.isFilterVisible);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"formGroup\", ctx.saveFilterForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.saveFilterForm.invalid || ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\".filter-title[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.filter-title[_ngcontent-%COMP%]:hover {\\n  color: var(--bs-primary);\\n}\\n\\n.badge-smaller[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  padding: 0.25em 0.45em;\\n}\\n\\n.filter-title[_ngcontent-%COMP%]   i.bi-chevron-down[_ngcontent-%COMP%], .filter-title[_ngcontent-%COMP%]   i.bi-chevron-up[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease-in-out;\\n}\\n\\n.saved-filters[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n  margin-top: 0.5rem;\\n}\\n\\n.saved-filter[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background-color: rgba(var(--bs-primary-rgb), 0.1);\\n  border: 1px solid rgba(var(--bs-primary-rgb), 0.2);\\n  border-radius: 4px;\\n  padding: 0.25rem 0.75rem;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n}\\n\\n.saved-filter[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(var(--bs-primary-rgb), 0.2);\\n}\\n\\n.filter-name[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n.btn-link[_ngcontent-%COMP%] {\\n  padding: 0;\\n  font-size: 0.875rem;\\n}\\n\\n\\n@media (prefers-color-scheme: dark) {\\n  .saved-filter[_ngcontent-%COMP%] {\\n    background-color: rgba(var(--bs-primary-rgb), 0.2);\\n    border-color: rgba(var(--bs-primary-rgb), 0.3);\\n  }\\n\\n  .saved-filter[_ngcontent-%COMP%]:hover {\\n    background-color: rgba(var(--bs-primary-rgb), 0.3);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAAiCC,UAAU,QAAQ,gBAAgB;AACnE,SAA0EC,kBAAkB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,yBAAyB,QAAQ,yBAAyB;;;;;;;;;;ICG9KC,gCAA0E;IAAAA,YAAuB;IAAAA,iBAAO;;;;IAA9BA,eAAuB;IAAvBA,8CAAuB;;;;;IAuB3FA,kCAAqD;IAAAA,YAAmB;IAAAA,iBAAS;;;;IAA9CA,mCAAiB;IAACA,eAAmB;IAAnBA,uCAAmB;;;;;IASxEA,kCAAmE;IAAAA,YAAkB;IAAAA,iBAAS;;;;IAApDA,0CAAwB;IAACA,eAAkB;IAAlBA,sCAAkB;;;;;IAWnFA,kCAAgE;IAAAA,YAAgB;IAAAA,iBAAS;;;;IAA9CA,sCAAoB;IAACA,eAAgB;IAAhBA,oCAAgB;;;;;IANtFA,6BAAkE;IAEhEA,+BAAoC;IACWA,4BAAY;IAAAA,iBAAQ;IACjEA,kCAA6E;IACpDA,iCAAY;IAAAA,iBAAS;IAC5CA,iGAAyF;IAC3FA,iBAAS;IAIXA,+BAAoC;IACSA,uBAAM;IAAAA,iBAAQ;IACzDA,6BAAgI;IAClIA,iBAAM;IAGNA,gCAAoC;IACOA,0BAAS;IAAAA,iBAAQ;IAC1DA,6BAAmF;IACrFA,iBAAM;IACRA,0BAAe;;;;IAhBDA,eAAc;IAAdA,4BAAc;IACGA,eAAgB;IAAhBA,8CAAgB;;;;;IAwBzCA,kCAA8D;IAAAA,YAAiB;IAAAA,iBAAS;;;;IAAhDA,uCAAqB;IAACA,eAAiB;IAAjBA,qCAAiB;;;;;IAJnFA,+BAA+D;IACpBA,mDAAe;IAAAA,iBAAQ;IAChEA,kCAAqE;IAC1CA,6CAAc;IAAAA,iBAAS;IAChDA,uGAAwF;IAC1FA,iBAAS;;;;IAFCA,eAAgB;IAAhBA,8BAAgB;IACEA,eAAY;IAAZA,2CAAY;;;;;IAK1CA,+BAA+D;IACpBA,yBAAS;IAAAA,iBAAQ;IAC1DA,4BAAiI;IACnIA,iBAAM;;;;;IAdRA,6BAA0C;IAExCA,2FAMM;IAGNA,2FAGM;IACRA,0BAAe;;;;IAbwBA,eAAwB;IAAxBA,gDAAwB;IASxBA,eAAwB;IAAxBA,gDAAwB;;;;;IAgB3DA,kCAAoE;IAAAA,YAAkB;IAAAA,iBAAS;;;;IAAlDA,wCAAsB;IAACA,eAAkB;IAAlBA,sCAAkB;;;;;IASxFA,+BAAuF;IACrFA,6DACF;IAAAA,iBAAM;;;;;;;;;;IANRA,+BAAyF;IAC9CA,kBAAE;IAAAA,iBAAQ;IACnDA,4BAC8G;IAC9GA,kFAEM;IACRA,iBAAM;;;;;;IAJGA,eAAsG;IAAtGA,0NAAsG;IAC9EA,eAAsD;IAAtDA,gJAAsD;;;;;IAQrFA,+BAAqF;IACnFA,4CACF;IAAAA,iBAAM;;;;;IANRA,+BAAyF;IAChDA,kBAAE;IAAAA,iBAAQ;IACjDA,4BAC0G;IAC1GA,kFAEM;IACRA,iBAAM;;;;;;IAJGA,eAAkG;IAAlGA,wNAAkG;IAC1EA,eAAoD;IAApDA,+IAAoD;;;;;;IAWrFA,+BAAiG;IAAnCA;MAAA;MAAA;MAAA;MAAA,OAASA,mDAAwB;IAAA,EAAC;IAC9FA,gCAA0B;IAAAA,YAAiB;IAAAA,iBAAO;IAClDA,kCAA4F;IAA5CA;MAAA;MAAA;MAAA;MAAA,OAASA,4DAAiC;IAAA,EAAC;IACzFA,wBAA2B;IAC7BA,iBAAS;;;;IAHiBA,eAAiB;IAAjBA,qCAAiB;;;;;IAJjDA,+BAAkD;IAC5CA,wCAAc;IAAAA,iBAAK;IACvBA,+BAA2B;IACzBA,kFAKM;IACRA,iBAAM;;;;IANyCA,eAAe;IAAfA,8CAAe;;;;;IArGlEA,+BAA+C;IAKAA,6BAAQ;IAAAA,iBAAQ;IACvDA,kCAAiE;IACtCA,gDAAiB;IAAAA,iBAAS;IACnDA,iFAAiF;IACnFA,iBAAS;IAIXA,gCAAoC;IACKA,sBAAK;IAAAA,iBAAQ;IACpDA,mCAAiE;IACtCA,oCAAc;IAAAA,iBAAS;IAChDA,mFAA8F;IAChGA,iBAAS;IAIXA,gGAqBe;IAGfA,+FAee;IAGfA,gCAAoC;IACSA,6CAAa;IAAAA,iBAAQ;IAChEA,6BAA0G;IAC5GA,iBAAM;IAGNA,gCAAoC;IACKA,6CAAa;IAAAA,iBAAQ;IAC5DA,mCAAiE;IAC/DA,mFAA+F;IACjGA,iBAAS;IAIXA,6EAOM;IACNA,6EAOM;IACRA,iBAAM;IAIRA,6EAUM;IACRA,iBAAM;;;;;;IA5GEA,eAAwB;IAAxBA,6CAAwB;IAMdA,eAAgB;IAAhBA,8BAAgB;IACCA,eAAQ;IAARA,sCAAQ;IAQzBA,eAAgB;IAAhBA,8BAAgB;IACGA,eAAa;IAAbA,2CAAa;IAK7BA,eAAiD;IAAjDA,gFAAiD;IAwBjDA,eAAyB;IAAzBA,iDAAyB;IA2BTA,eAAgB;IAAhBA,8CAAgB;IAKVA,eAAkD;IAAlDA,gHAAkD;IAQlDA,eAAkD;IAAlDA,gHAAkD;IAYxEA,eAA6B;IAA7BA,qDAA6B;;;;;IA4B1CA,+BAA8C;IAAAA,YAAW;IAAAA,iBAAM;;;;IAAjBA,eAAW;IAAXA,kCAAW;;;;;IAMzDA,2BAA8G;;;;;;;;;;;;;;;AD1IxH,OAAM,MAAOC,kBAAkB;EA8B7BC,YACUC,EAAe,EACfC,WAAwB,EACxBC,WAAwB,EACxBC,YAA0B,EAC1BC,MAAqB;IAJrB,OAAE,GAAFJ,EAAE;IACF,gBAAW,GAAXC,WAAW;IACX,gBAAW,GAAXC,WAAW;IACX,iBAAY,GAAZC,YAAY;IACZ,WAAM,GAANC,MAAM;IAlCP,YAAO,GAAW,UAAU,CAAC,CAAC;IAC9B,uBAAkB,GAAY,KAAK,CAAC,CAAC;IACrC,uBAAkB,GAAY,KAAK,CAAC,CAAC;IACpC,iBAAY,GAAG,IAAIb,YAAY,EAAoB;IAI7D,iBAAY,GAA6B,EAAE;IAC3C,UAAK,GAAW,EAAE;IAClB,kBAAa,GAAGE,kBAAkB,CAACY,gBAAgB,EAAE;IACrD,eAAU,GAAGX,eAAe,CAACY,aAAa,EAAE;IAC5C,cAAS,GAAGV,yBAAyB,CAACW,YAAY,EAAE;IACpD,cAAS,GAAG,KAAK;IACjB,UAAK,GAAkB,IAAI;IAE3B;IACA,oBAAe,GAAG,KAAK,CAAC,CAAC;IACzB,qBAAgB,GAAG,KAAK;IACxB,sBAAiB,GAAG,CAAC;IAErB;IACA,kBAAa,GAAG,CACd;MAAEC,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAO,CAAE,EAC5B;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAO,CAAE,EAC5B;MAAED,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAQ,CAAE,EAC9B;MAAED,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAQ,CAAE,EAC9B;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAgB,CAAE,CAC7C;IASC,IAAI,CAACC,UAAU,GAAG,IAAI,CAACC,gBAAgB,EAAE;IACzC,IAAI,CAACC,cAAc,GAAG,IAAI,CAACZ,EAAE,CAACa,KAAK,CAAC;MAClCC,IAAI,EAAE,CAAC,EAAE;KACV,CAAC;EACJ;EAEAC,QAAQ;IACN,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,yBAAyB,EAAE;IAChC,IAAI,CAACC,sBAAsB,EAAE;IAE7B;IACA,IAAI,CAACT,UAAU,CAACU,YAAY,CAACC,SAAS,CAAC,MAAK;MAC1C,IAAI,CAACC,4BAA4B,EAAE;MACnC,IAAI,CAACC,WAAW,EAAE;IACpB,CAAC,CAAC;IAEF;IACA,IAAI,CAACb,UAAU,CAACc,GAAG,CAAC,QAAQ,CAAC,EAAEJ,YAAY,CAACC,SAAS,CAACI,MAAM,IAAG;MAC7D,IAAI,CAACC,0BAA0B,CAACD,MAAM,CAAC;IACzC,CAAC,CAAC;EACJ;EAEA;;;EAGQC,0BAA0B,CAACD,MAAuB;IACxD,MAAME,eAAe,GAAG,IAAI,CAACjB,UAAU,CAACc,GAAG,CAAC,UAAU,CAAC;IACvD,MAAMI,aAAa,GAAG,IAAI,CAAClB,UAAU,CAACc,GAAG,CAAC,QAAQ,CAAC;IAEnD,IAAIC,MAAM,KAAK,QAAQ,EAAE;MACvB;MACAE,eAAe,EAAEE,aAAa,CAAC,CAACrC,UAAU,CAACsC,QAAQ,CAAC,CAAC;MACrDF,aAAa,EAAEC,aAAa,CAAC,CAACrC,UAAU,CAACsC,QAAQ,CAAC,CAAC;KACpD,MAAM;MACL;MACAH,eAAe,EAAEI,eAAe,EAAE;MAClCH,aAAa,EAAEG,eAAe,EAAE;;IAGlCJ,eAAe,EAAEK,sBAAsB,EAAE;IACzCJ,aAAa,EAAEI,sBAAsB,EAAE;EACzC;EAEA;;;EAGQb,sBAAsB;IAC5B,IAAI;MACF,MAAMc,SAAS,GAAG,kBAAkB,IAAI,CAACC,OAAO,EAAE;MAClD,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAACJ,SAAS,CAAC;MAElD,IAAIE,UAAU,EAAE;QACd,MAAMG,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;QAE1C;QACA,IAAIG,WAAW,CAACG,QAAQ,IAAIH,WAAW,CAACI,MAAM,IAAI,CAACJ,WAAW,CAACb,MAAM,EAAE;UACrE;UACA,MAAMgB,QAAQ,GAAG,IAAIE,IAAI,CAACL,WAAW,CAACG,QAAQ,CAAC;UAC/C,MAAMC,MAAM,GAAG,IAAIC,IAAI,CAACL,WAAW,CAACI,MAAM,CAAC;UAC3C,MAAME,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,MAAM,CAACK,OAAO,EAAE,GAAGN,QAAQ,CAACM,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;UAE5F;UACA,IAAIH,QAAQ,IAAI,CAAC,EAAE;YACjBN,WAAW,CAACb,MAAM,GAAG,CAAC;WACvB,MAAM,IAAImB,QAAQ,IAAI,CAAC,EAAE;YACxBN,WAAW,CAACb,MAAM,GAAG,CAAC;WACvB,MAAM,IAAImB,QAAQ,IAAI,EAAE,EAAE;YACzBN,WAAW,CAACb,MAAM,GAAG,EAAE;WACxB,MAAM,IAAImB,QAAQ,IAAI,EAAE,EAAE;YACzBN,WAAW,CAACb,MAAM,GAAG,EAAE;WACxB,MAAM;YACLa,WAAW,CAACb,MAAM,GAAG,QAAQ;;;QAIjC;QACA,IAAIa,WAAW,CAACG,QAAQ,EAAE;UACxB,MAAMA,QAAQ,GAAG,IAAIE,IAAI,CAACL,WAAW,CAACG,QAAQ,CAAC;UAC/CH,WAAW,CAACG,QAAQ,GAAG,IAAI,CAACO,kBAAkB,CAACP,QAAQ,CAAC;;QAG1D,IAAIH,WAAW,CAACI,MAAM,EAAE;UACtB,MAAMA,MAAM,GAAG,IAAIC,IAAI,CAACL,WAAW,CAACI,MAAM,CAAC;UAC3CJ,WAAW,CAACI,MAAM,GAAG,IAAI,CAACM,kBAAkB,CAACN,MAAM,CAAC;;QAGtD;QACA,IAAI,CAAChC,UAAU,CAACuC,UAAU,CAACX,WAAW,CAAC;QAEvC;QACA,IAAI,CAACZ,0BAA0B,CAACY,WAAW,CAACb,MAAM,CAAC;QAEnD;QACA,IAAI,CAACH,4BAA4B,EAAE;;KAEtC,CAAC,OAAO4B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,IAAI,CAAChB,OAAO,EAAE,EAAEgB,KAAK,CAAC;;EAE/E;EAEA;;;EAGAE,sBAAsB;IACpB,IAAI,CAACC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAE5C;IACA,IAAI;MACF,MAAMC,aAAa,GAAG,0BAA0B,IAAI,CAACpB,OAAO,EAAE;MAC9DE,YAAY,CAACmB,OAAO,CAACD,aAAa,EAAE,IAAI,CAACD,eAAe,CAACG,QAAQ,EAAE,CAAC;KACrE,CAAC,OAAON,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8DAA8D,EAAEA,KAAK,CAAC;;EAExF;EAEA;;;EAGQhC,yBAAyB;IAC/B,IAAI;MACF,MAAMoC,aAAa,GAAG,0BAA0B,IAAI,CAACpB,OAAO,EAAE;MAC9D,MAAMuB,eAAe,GAAGrB,YAAY,CAACC,OAAO,CAACiB,aAAa,CAAC;MAE3D,IAAIG,eAAe,KAAK,IAAI,EAAE;QAC5B,IAAI,CAACJ,eAAe,GAAGI,eAAe,KAAK,MAAM;;KAEpD,CAAC,OAAOP,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6DAA6D,EAAEA,KAAK,CAAC;;EAEvF;EAEA;;;EAGQ5B,4BAA4B;IAClC,MAAMoC,MAAM,GAAG,IAAI,CAAChD,UAAU,CAACF,KAAK;IAEpC;IACA,MAAMmD,KAAK,GAAG,IAAIhB,IAAI,EAAE;IACxB,MAAMiB,QAAQ,GAAG,IAAIjB,IAAI,CAACgB,KAAK,CAAC;IAChCC,QAAQ,CAACC,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;IAElC,MAAMC,YAAY,GAAG,IAAInB,IAAI,CAACgB,KAAK,CAAC;IACpCG,YAAY,CAACC,OAAO,CAACJ,KAAK,CAACK,OAAO,EAAE,GAAG,CAAC,CAAC;IACzCF,YAAY,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAEjC;IACA,MAAMI,kBAAkB,GAAG,IAAI,CAACjB,kBAAkB,CAACc,YAAY,CAAC;IAChE,MAAMI,gBAAgB,GAAG,IAAI,CAAClB,kBAAkB,CAACY,QAAQ,CAAC;IAE1D;IACA,MAAMO,aAAa,GAAG,CAAC;IAEvB;IACA,MAAMC,aAAa,GAAGC,MAAM,CAACC,IAAI,CAACZ,MAAM,CAAC,CACtCa,MAAM,CAACC,GAAG,IAAG;MACZ;MACA,IAAIA,GAAG,KAAK,YAAY,IAAId,MAAM,CAACc,GAAG,CAAC,KAAK,GAAG,EAAE;QAC/C,OAAO,KAAK;;MAGd;MACA,IAAIA,GAAG,KAAK,QAAQ,EAAE;QACpB,IAAI,IAAI,CAACtC,OAAO,KAAK,UAAU,IAAIwB,MAAM,CAACc,GAAG,CAAC,KAAK7E,SAAS,CAAC8E,QAAQ,EAAE;UACrE,OAAO,KAAK;;QAEd,IAAI,IAAI,CAACvC,OAAO,KAAK,KAAK,IAAIwB,MAAM,CAACc,GAAG,CAAC,KAAK7E,SAAS,CAAC+E,MAAM,EAAE;UAC9D,OAAO,KAAK;;;MAIhB;MACA,IAAIF,GAAG,KAAK,QAAQ,IAAId,MAAM,CAACc,GAAG,CAAC,KAAKL,aAAa,EAAE;QACrD,OAAO,KAAK;;MAGd;MACA,IAAIT,MAAM,CAACjC,MAAM,KAAK,QAAQ,KAAK+C,GAAG,KAAK,UAAU,IAAIA,GAAG,KAAK,QAAQ,CAAC,EAAE;QAC1E,OAAO,KAAK;;MAGd;MACA,IAAId,MAAM,CAACjC,MAAM,KAAK,QAAQ,EAAE;QAC9B,IAAI+C,GAAG,KAAK,UAAU,IAAId,MAAM,CAACc,GAAG,CAAC,KAAKP,kBAAkB,EAAE;UAC5D,OAAO,KAAK;;QAEd,IAAIO,GAAG,KAAK,QAAQ,IAAId,MAAM,CAACc,GAAG,CAAC,KAAKN,gBAAgB,EAAE;UACxD,OAAO,KAAK;;;MAIhB;MACA,OAAOR,MAAM,CAACc,GAAG,CAAC,KAAK,IAAI,IAAId,MAAM,CAACc,GAAG,CAAC,KAAK,EAAE,IAAId,MAAM,CAACc,GAAG,CAAC,KAAKG,SAAS;IAChF,CAAC,CAAC,CACDC,MAAM,CAAC,CAACC,GAAQ,EAAEL,GAAG,KAAI;MACxBK,GAAG,CAACL,GAAG,CAAC,GAAGd,MAAM,CAACc,GAAG,CAAC;MACtB,OAAOK,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;IAER,IAAI,CAACC,iBAAiB,GAAGT,MAAM,CAACC,IAAI,CAACF,aAAa,CAAC,CAACW,MAAM;IAC1D,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACF,iBAAiB,GAAG,CAAC;IAElD;IACA,IAAI,IAAI,CAACE,gBAAgB,EAAE;MACzB7B,OAAO,CAAC8B,GAAG,CAAC,iBAAiB,EAAEb,aAAa,CAAC;;EAEjD;EAEA;;;EAGQzD,gBAAgB;IACtB;IACA,MAAMgD,KAAK,GAAG,IAAIhB,IAAI,EAAE;IACxB,MAAMiB,QAAQ,GAAG,IAAIjB,IAAI,CAACgB,KAAK,CAAC;IAChCC,QAAQ,CAACC,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;IAElC,MAAMC,YAAY,GAAG,IAAInB,IAAI,CAACgB,KAAK,CAAC;IACpCG,YAAY,CAACC,OAAO,CAACJ,KAAK,CAACK,OAAO,EAAE,GAAG,CAAC,CAAC;IACzCF,YAAY,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAEjC;IACA,MAAMqB,WAAW,GAAG,IAAI,CAAClC,kBAAkB,CAACc,YAAY,CAAC;IACzD,MAAMqB,SAAS,GAAG,IAAI,CAACnC,kBAAkB,CAACY,QAAQ,CAAC;IAEnD;IACA,MAAMwB,YAAY,GAAQ;MACxB3D,MAAM,EAAE,CAAC,CAAC,CAAC;MACXgB,QAAQ,EAAE,CAACyC,WAAW,CAAC;MACvBxC,MAAM,EAAE,CAACyC,SAAS,CAAC;MACnBE,MAAM,EAAE,CAAC,IAAI,CAAC;MACdC,UAAU,EAAE,CAAC,GAAG,CAAC;MACjBC,MAAM,EAAE,CAAC,IAAI;KACd;IAED;IACA,IAAI,IAAI,CAACrD,OAAO,KAAK,UAAU,EAAE;MAC/BkD,YAAY,CAACG,MAAM,GAAG,CAAC5F,SAAS,CAAC8E,QAAQ,CAAC;KAC3C,MAAM,IAAI,IAAI,CAACvC,OAAO,KAAK,KAAK,EAAE;MACjCkD,YAAY,CAACG,MAAM,GAAG,CAAC5F,SAAS,CAAC+E,MAAM,CAAC;;IAG1C;IACA,IAAI,IAAI,CAACxC,OAAO,KAAK,UAAU,IAAI,IAAI,CAACA,OAAO,KAAK,KAAK,EAAE;MACzDkD,YAAY,CAACI,UAAU,GAAG,CAAC,EAAE,CAAC;MAC9BJ,YAAY,CAACK,QAAQ,GAAG,CAAC,IAAI,CAAC;MAC9BL,YAAY,CAACM,YAAY,GAAG,CAAC,EAAE,CAAC;;IAGlC;IACA,IAAI,IAAI,CAACxD,OAAO,KAAK,OAAO,EAAE;MAC5BkD,YAAY,CAACO,QAAQ,GAAG,CAAC,IAAI,CAAC;MAC9BP,YAAY,CAACQ,QAAQ,GAAG,CAAC,EAAE,CAAC;;IAG9B,OAAO,IAAI,CAAC5F,EAAE,CAACa,KAAK,CAACuE,YAAY,CAAC;EACpC;EAEA;;;EAGApE,SAAS;IACP,IAAI,CAACd,WAAW,CAAC2F,QAAQ,EAAE,CAACxE,SAAS,CAAC;MACpCyE,IAAI,EAAGC,KAAK,IAAI;QACd,IAAI,CAACA,KAAK,GAAGA,KAAK;MACpB,CAAC;MACD7C,KAAK,EAAG8C,GAAG,IAAI;QACb7C,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAE8C,GAAG,CAAC;MACpD;KACD,CAAC;EACJ;EAEA;;;EAGA/E,gBAAgB;IACd,IAAI,CAAChB,WAAW,CAACgG,kBAAkB,CAAC,IAAI,CAAC/D,OAAO,CAAC,CAACb,SAAS,CAAC;MAC1DyE,IAAI,EAAGI,OAAO,IAAI;QAChB,IAAI,CAACC,YAAY,GAAGD,OAAO;MAC7B,CAAC;MACDhD,KAAK,EAAG8C,GAAG,IAAI;QACb7C,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAE8C,GAAG,CAAC;MAC3D;KACD,CAAC;EACJ;EAEA;;;EAGAzE,WAAW;IACT,MAAM6E,UAAU,GAAG,IAAI,CAAC1F,UAAU,CAACF,KAAK;IACxC,MAAM6F,UAAU,GAAqB;MAAE,GAAGD;IAAU,CAAE;IAEtD;IACA,IAAIA,UAAU,CAAC3E,MAAM,KAAK,QAAQ,EAAE;MAClC;MACA,MAAMkC,KAAK,GAAG,IAAIhB,IAAI,EAAE;MACxB,MAAMiB,QAAQ,GAAG,IAAIjB,IAAI,CAACgB,KAAK,CAAC;MAChCC,QAAQ,CAACC,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;MAElC,MAAMpB,QAAQ,GAAG,IAAIE,IAAI,CAACgB,KAAK,CAAC;MAChClB,QAAQ,CAACsB,OAAO,CAACJ,KAAK,CAACK,OAAO,EAAE,GAAGsC,MAAM,CAACF,UAAU,CAAC3E,MAAM,CAAC,CAAC;MAC7DgB,QAAQ,CAACoB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAE7BwC,UAAU,CAAC5D,QAAQ,GAAGA,QAAQ;MAC9B4D,UAAU,CAAC3D,MAAM,GAAGkB,QAAQ;KAC7B,MAAM;MACL;MACA,IAAIwC,UAAU,CAAC3D,QAAQ,EAAE;QACvB4D,UAAU,CAAC5D,QAAQ,GAAG,IAAIE,IAAI,CAACyD,UAAU,CAAC3D,QAAQ,CAAC;;MAGrD,IAAI2D,UAAU,CAAC1D,MAAM,EAAE;QACrB2D,UAAU,CAAC3D,MAAM,GAAG,IAAIC,IAAI,CAACyD,UAAU,CAAC1D,MAAM,CAAC;;;IAInD;IACA,IAAI,CAAC6D,eAAe,CAACF,UAAU,CAAC;IAEhC,IAAI,CAACG,YAAY,CAACC,IAAI,CAACJ,UAAU,CAAC;EACpC;EAEA;;;EAGAK,WAAW;IACT;IACA,IAAI,CAACnF,WAAW,EAAE;EACpB;EAEA;;;EAGQgF,eAAe,CAACF,UAA4B;IAClD,IAAI;MACF,MAAMpE,SAAS,GAAG,kBAAkB,IAAI,CAACC,OAAO,EAAE;MAClDE,YAAY,CAACmB,OAAO,CAACtB,SAAS,EAAEM,IAAI,CAACoE,SAAS,CAACN,UAAU,CAAC,CAAC;KAC5D,CAAC,OAAOnD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,IAAI,CAAChB,OAAO,EAAE,EAAEgB,KAAK,CAAC;;EAE/E;EAEA;;;EAGA0D,WAAW;IACT;IACA,MAAMjD,KAAK,GAAG,IAAIhB,IAAI,EAAE;IACxB,MAAMiB,QAAQ,GAAG,IAAIjB,IAAI,CAACgB,KAAK,CAAC;IAChCC,QAAQ,CAACC,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;IAElC,MAAMC,YAAY,GAAG,IAAInB,IAAI,CAACgB,KAAK,CAAC;IACpCG,YAAY,CAACC,OAAO,CAACJ,KAAK,CAACK,OAAO,EAAE,GAAG,CAAC,CAAC;IACzCF,YAAY,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAEjC;IACA,MAAMqB,WAAW,GAAG,IAAI,CAAClC,kBAAkB,CAACc,YAAY,CAAC;IACzD,MAAMqB,SAAS,GAAG,IAAI,CAACnC,kBAAkB,CAACY,QAAQ,CAAC;IAEnD;IACA,MAAMiD,WAAW,GAAQ;MACvBpF,MAAM,EAAE,CAAC;MACTgB,QAAQ,EAAEyC,WAAW;MACrBxC,MAAM,EAAEyC,SAAS;MACjBG,UAAU,EAAE;KACb;IAED;IACA,IAAI,IAAI,CAACpD,OAAO,KAAK,UAAU,EAAE;MAC/B2E,WAAW,CAACtB,MAAM,GAAG5F,SAAS,CAAC8E,QAAQ;KACxC,MAAM,IAAI,IAAI,CAACvC,OAAO,KAAK,KAAK,EAAE;MACjC2E,WAAW,CAACtB,MAAM,GAAG5F,SAAS,CAAC+E,MAAM;;IAGvC,IAAI,CAAChE,UAAU,CAACoG,KAAK,CAACD,WAAW,CAAC;IAClC,IAAI,CAACvF,4BAA4B,EAAE;IACnC,IAAI,CAACC,WAAW,EAAE;IAElB;IACA,IAAI,CAACnB,MAAM,CAAC2G,IAAI,CAAC,qBAAqB,EAAE,OAAO,CAAC;EAClD;EAEA;;;;EAIQ/D,kBAAkB,CAACgE,IAAU;IACnC,MAAMC,IAAI,GAAGD,IAAI,CAACE,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAG,CAACH,IAAI,CAACI,QAAQ,EAAE,GAAG,CAAC,EAAE5D,QAAQ,EAAE,CAAC6D,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC/D,MAAMC,GAAG,GAAGN,IAAI,CAAChD,OAAO,EAAE,CAACR,QAAQ,EAAE,CAAC6D,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACtD,MAAME,KAAK,GAAGP,IAAI,CAACQ,QAAQ,EAAE,CAAChE,QAAQ,EAAE,CAAC6D,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACzD,MAAMI,OAAO,GAAGT,IAAI,CAACU,UAAU,EAAE,CAAClE,QAAQ,EAAE,CAAC6D,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAE7D,OAAO,GAAGJ,IAAI,IAAIE,KAAK,IAAIG,GAAG,IAAIC,KAAK,IAAIE,OAAO,EAAE;EACtD;EAEA;;;EAGAE,mBAAmB;IACjB,IAAI,CAAC/G,cAAc,CAACkG,KAAK,EAAE;IAC3B,IAAI,CAAC3G,YAAY,CAACyH,IAAI,CAAC,iBAAiB,CAAC;EAC3C;EAEA;;;EAGAC,UAAU;IACR,IAAI,IAAI,CAACjH,cAAc,CAACkH,OAAO,EAAE;MAC/B;;IAGF,MAAMC,UAAU,GAAG,IAAI,CAACnH,cAAc,CAACY,GAAG,CAAC,MAAM,CAAC,EAAEhB,KAAK;IACzD,MAAM6F,UAAU,GAAG,IAAI,CAAC3F,UAAU,CAACF,KAAK;IAExC,MAAMwH,WAAW,GAA0B;MACzClH,IAAI,EAAEiH,UAAU;MAChB7F,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBmE,UAAU,EAAE9D,IAAI,CAACoE,SAAS,CAACN,UAAU;KACtC;IAED,IAAI,CAAC4B,SAAS,GAAG,IAAI;IACrB,IAAI,CAAChI,WAAW,CAACiI,aAAa,CAACF,WAAW,CAAC,CAAC3G,SAAS,CAAC;MACpDyE,IAAI,EAAGqC,QAAQ,IAAI;QACjB,IAAI,CAAChC,YAAY,CAACiC,IAAI,CAACD,QAAQ,CAAC;QAChC,IAAI,CAAChI,YAAY,CAACkI,KAAK,CAAC,iBAAiB,CAAC;QAC1C,IAAI,CAACJ,SAAS,GAAG,KAAK;MACxB,CAAC;MACD/E,KAAK,EAAG8C,GAAG,IAAI;QACb7C,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAE8C,GAAG,CAAC;QAC/C,IAAI,CAAC9C,KAAK,GAAG,4BAA4B;QACzC,IAAI,CAAC+E,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEA;;;EAGAK,gBAAgB,CAAC/D,MAA8B;IAC7C,IAAI;MACF,MAAMgE,UAAU,GAAGhG,IAAI,CAACC,KAAK,CAAC+B,MAAM,CAAC8B,UAAU,CAAC;MAEhD;MACA,MAAMD,UAAU,GAAQ;QAAE,GAAGmC;MAAU,CAAE;MAEzC;MACA,IAAIA,UAAU,CAAC9F,QAAQ,IAAI8F,UAAU,CAAC7F,MAAM,IAAI,CAAC6F,UAAU,CAAC9G,MAAM,EAAE;QAClE;QACA,MAAMgB,QAAQ,GAAG,IAAIE,IAAI,CAAC4F,UAAU,CAAC9F,QAAQ,CAAC;QAC9C,MAAMC,MAAM,GAAG,IAAIC,IAAI,CAAC4F,UAAU,CAAC7F,MAAM,CAAC;QAC1C,MAAME,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,MAAM,CAACK,OAAO,EAAE,GAAGN,QAAQ,CAACM,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAE5F;QACA,IAAIH,QAAQ,IAAI,CAAC,EAAE;UACjBwD,UAAU,CAAC3E,MAAM,GAAG,CAAC;SACtB,MAAM,IAAImB,QAAQ,IAAI,CAAC,EAAE;UACxBwD,UAAU,CAAC3E,MAAM,GAAG,CAAC;SACtB,MAAM,IAAImB,QAAQ,IAAI,EAAE,EAAE;UACzBwD,UAAU,CAAC3E,MAAM,GAAG,EAAE;SACvB,MAAM,IAAImB,QAAQ,IAAI,EAAE,EAAE;UACzBwD,UAAU,CAAC3E,MAAM,GAAG,EAAE;SACvB,MAAM;UACL2E,UAAU,CAAC3E,MAAM,GAAG,QAAQ;;;MAIhC;MACA,IAAI8G,UAAU,CAAC9F,QAAQ,EAAE;QACvB,MAAMA,QAAQ,GAAG,IAAIE,IAAI,CAAC4F,UAAU,CAAC9F,QAAQ,CAAC;QAC9C2D,UAAU,CAAC3D,QAAQ,GAAG,IAAI,CAACO,kBAAkB,CAACP,QAAQ,CAAC;;MAEzD,IAAI8F,UAAU,CAAC7F,MAAM,EAAE;QACrB,MAAMA,MAAM,GAAG,IAAIC,IAAI,CAAC4F,UAAU,CAAC7F,MAAM,CAAC;QAC1C0D,UAAU,CAAC1D,MAAM,GAAG,IAAI,CAACM,kBAAkB,CAACN,MAAM,CAAC;;MAGrD;MACA,IAAI,CAAChC,UAAU,CAACoG,KAAK,CAAC;QACpBxB,UAAU,EAAE;OACb,CAAC;MAEF;MACA,IAAI,CAAC5E,UAAU,CAACuC,UAAU,CAACmD,UAAU,CAAC;MAEtC;MACA,IAAI,IAAI,CAAClE,OAAO,KAAK,UAAU,EAAE;QAC/B,IAAI,CAACxB,UAAU,CAACuC,UAAU,CAAC;UAAEsC,MAAM,EAAE5F,SAAS,CAAC8E;QAAQ,CAAE,CAAC;OAC3D,MAAM,IAAI,IAAI,CAACvC,OAAO,KAAK,KAAK,EAAE;QACjC,IAAI,CAACxB,UAAU,CAACuC,UAAU,CAAC;UAAEsC,MAAM,EAAE5F,SAAS,CAAC+E;QAAM,CAAE,CAAC;;MAG1D;MACA,IAAI,CAAChD,0BAA0B,CAAC0E,UAAU,CAAC3E,MAAM,CAAC;MAElD;MACA,IAAI,CAACH,4BAA4B,EAAE;MAEnC;MACA,IAAI,CAACC,WAAW,EAAE;KACnB,CAAC,OAAO2B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;;EAEjE;EAEA;;;EAGMsF,iBAAiB,CAACjE,MAA8B,EAAEkE,KAAY;IAAA;IAAA;MAClEA,KAAK,CAACC,eAAe,EAAE,CAAC,CAAC;MAEzB;MACA,MAAMC,SAAS,SAAS,KAAI,CAACxI,YAAY,CAACyI,OAAO,CAC/C,gCAAgCrE,MAAM,CAACzD,IAAI,IAAI,EAC/C,iBAAiB,EACjB,IAAI,EACJ,QAAQ,EACR,YAAY,EACZ,eAAe,CAChB;MAED,IAAI6H,SAAS,EAAE;QACb,KAAI,CAAC1I,WAAW,CAAC4I,eAAe,CAACtE,MAAM,CAACuE,EAAE,CAAC,CAACzH,SAAS,CAAC;UACpDyE,IAAI,EAAE,MAAK;YACT,KAAI,CAACK,YAAY,GAAG,KAAI,CAACA,YAAY,CAAC5B,MAAM,CAACwE,CAAC,IAAIA,CAAC,CAACD,EAAE,KAAKvE,MAAM,CAACuE,EAAE,CAAC;UACvE,CAAC;UACD5F,KAAK,EAAG8C,GAAG,IAAI;YACb7C,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAE8C,GAAG,CAAC;UAC/C;SACD,CAAC;;IACH;EACH;;;uBA3jBWlG,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAAkJ;MAAAC;QAAA/G;QAAAgH;QAAAC;MAAA;MAAAC;QAAA5C;MAAA;MAAA6C;MAAAC;MAAAC;MAAAC;QAAA;UCd/B3J,8BAAuB;UAEWA;YAAA,OAAS4J,4BAAwB;UAAA,EAAC;UAC9D5J,uBAA0H;UAC1HA,4BAAM;UAAAA,qBAAK;UAAAA,iBAAO;UAClBA,qEAAwG;UACxGA,uBAA2G;UAC7GA,iBAAK;UACLA,2BAAK;UACiDA;YAAA,OAAS4J,yBAAqB;UAAA,EAAC;UACjF5J,wBAA+B;UAAAA,mCACjC;UAAAA,iBAAS;UACTA,kCAA8E;UAAxBA;YAAA,OAAS4J,iBAAa;UAAA,EAAC;UAC3E5J,wBAAiD;UAAAA,uBACnD;UAAAA,iBAAS;UACTA,mCAAuE;UAAxBA;YAAA,OAAS4J,iBAAa;UAAA,EAAC;UACpE5J,yBAAuC;UAAAA,uCACzC;UAAAA,iBAAS;UAGbA,wEA6GM;UACRA,iBAAM;UAGNA,gCAAqH;UAI3DA,kCAAY;UAAAA,iBAAK;UACnEA,8BAA6F;UAC/FA,iBAAM;UACNA,gCAAwB;UAGyBA,kCAAY;UAAAA,iBAAQ;UAC/DA,6BAAwF;UAC1FA,iBAAM;UACNA,sEAA+D;UACjEA,iBAAO;UAETA,gCAA0B;UACgDA,4BAAM;UAAAA,iBAAS;UACvFA,mCAAsH;UAAvBA;YAAA,OAAS4J,gBAAY;UAAA,EAAC;UACnH5J,wEAA8G;UAC9GA,8BACF;UAAAA,iBAAS;;;UAvJaA,eAA6F;UAA7FA,iGAA6F;UAE9GA,eAAsB;UAAtBA,2CAAsB;UACVA,eAAmF;UAAnFA,gGAAmF;UAclFA,gBAAqB;UAArBA,0CAAqB;UAyHjCA,eAA4B;UAA5BA,8CAA4B;UAKCA,eAAW;UAAXA,gCAAW;UAKAA,eAAgD;UAAhDA,sEAAgD;UACrFA,eAAe;UAAfA,oCAAe", "names": ["EventEmitter", "Validators", "ActivityTypeHelper", "LogSourceHelper", "LogSource", "ApplicationLogLevelHelper", "i0", "LogFilterComponent", "constructor", "fb", "logsService", "userService", "modalService", "toastr", "getActivityTypes", "getLogSources", "getLogLevels", "value", "label", "filterForm", "createFilterForm", "saveFilterForm", "group", "name", "ngOnInit", "loadUsers", "loadSavedFilters", "loadFilterVisibilityState", "loadCurrentFilterState", "valueChanges", "subscribe", "updateActiveFiltersIndicator", "applyFilter", "get", "period", "updateDateFieldsValidation", "fromDateControl", "toDateControl", "setValidators", "required", "clearValidators", "updateValueAndValidity", "<PERSON><PERSON><PERSON>", "logType", "filter<PERSON>son", "localStorage", "getItem", "savedFilter", "JSON", "parse", "fromDate", "toDate", "Date", "diffDays", "Math", "round", "getTime", "formatDateForInput", "patchValue", "error", "console", "toggleFilterVisibility", "isFilterVisible", "visibilityKey", "setItem", "toString", "savedVisibility", "values", "today", "endOfDay", "setHours", "sevenDaysAgo", "setDate", "getDate", "defaultFromDateStr", "defaultToDateStr", "defaultPeriod", "activeFilters", "Object", "keys", "filter", "key", "DISAdmin", "DISApi", "undefined", "reduce", "obj", "activeFilterCount", "length", "hasActiveFilters", "log", "fromDateStr", "toDateStr", "formControls", "userId", "maxResults", "source", "entityName", "entityId", "activityType", "logLevel", "category", "getUsers", "next", "users", "err", "getSavedLogFilters", "filters", "savedFilters", "formValues", "filterData", "Number", "saveFilterState", "filterChange", "emit", "refreshData", "stringify", "resetFilter", "resetValues", "reset", "info", "date", "year", "getFullYear", "month", "getMonth", "padStart", "day", "hours", "getHours", "minutes", "getMinutes", "openSaveFilterModal", "open", "saveFilter", "invalid", "filterName", "saveRequest", "isLoading", "saveLog<PERSON><PERSON>er", "response", "push", "close", "applySavedFilter", "parsedData", "deleteSavedFilter", "event", "stopPropagation", "confirmed", "confirm", "deleteLogFilter", "id", "f", "selectors", "inputs", "showLogLevelFilter", "showCategor<PERSON><PERSON><PERSON><PERSON>", "outputs", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\logs\\log-filter\\log-filter.component.ts", "C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\logs\\log-filter\\log-filter.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { LogFilterRequest, SavedLogFilterRequest, SavedLogFilterResponse, ActivityTypeHelper, LogSourceHelper, LogSource, ApplicationLogLevelHelper } from '../../models/logs.model';\nimport { LogsService } from '../../services/logs.service';\nimport { UserService } from '../../services/user.service';\nimport { User } from '../../models/user.model';\nimport { ModalService } from '../../services/modal.service';\nimport { ToastrService } from 'ngx-toastr';\n\n@Component({\n  selector: 'app-log-filter',\n  templateUrl: './log-filter.component.html',\n  styleUrls: ['./log-filter.component.css']\n})\nexport class LogFilterComponent implements OnInit {\n  @Input() logType: string = 'activity'; // activity, error, api\n  @Input() showLogLevelFilter: boolean = false; // Zobrazit filtr úrovně logování\n  @Input() showCategoryFilter: boolean = false; // Zobrazit filtr kategorie\n  @Output() filterChange = new EventEmitter<LogFilterRequest>();\n\n  filterForm: FormGroup;\n  saveFilterForm: FormGroup;\n  savedFilters: SavedLogFilterResponse[] = [];\n  users: User[] = [];\n  activityTypes = ActivityTypeHelper.getActivityTypes();\n  logSources = LogSourceHelper.getLogSources();\n  logLevels = ApplicationLogLevelHelper.getLogLevels();\n  isLoading = false;\n  error: string | null = null;\n\n  // Vlastnosti pro skrývání/zobrazování filtru\n  isFilterVisible = false; // Filtr je ve výchozím stavu skrytý\n  hasActiveFilters = false;\n  activeFilterCount = 0;\n\n  // Časová období pro dropdown\n  periodOptions = [\n    { value: 1, label: '1 den' },\n    { value: 7, label: '7 dní' },\n    { value: 30, label: '30 dní' },\n    { value: 90, label: '90 dní' },\n    { value: 'custom', label: 'Vlastní období' }\n  ];\n\n  constructor(\n    private fb: FormBuilder,\n    private logsService: LogsService,\n    private userService: UserService,\n    private modalService: ModalService,\n    private toastr: ToastrService\n  ) {\n    this.filterForm = this.createFilterForm();\n    this.saveFilterForm = this.fb.group({\n      name: ['']\n    });\n  }\n\n  ngOnInit(): void {\n    this.loadUsers();\n    this.loadSavedFilters();\n    this.loadFilterVisibilityState();\n    this.loadCurrentFilterState();\n\n    // Automatické aplikování filtru při změně formuláře\n    this.filterForm.valueChanges.subscribe(() => {\n      this.updateActiveFiltersIndicator();\n      this.applyFilter();\n    });\n\n    // Sledování změny období pro zobrazení/skrytí polí fromDate a toDate\n    this.filterForm.get('period')?.valueChanges.subscribe(period => {\n      this.updateDateFieldsValidation(period);\n    });\n  }\n\n  /**\n   * Aktualizace validace datumových polí podle vybraného období\n   */\n  private updateDateFieldsValidation(period: string | number): void {\n    const fromDateControl = this.filterForm.get('fromDate');\n    const toDateControl = this.filterForm.get('toDate');\n\n    if (period === 'custom') {\n      // Při vlastním období jsou pole povinná\n      fromDateControl?.setValidators([Validators.required]);\n      toDateControl?.setValidators([Validators.required]);\n    } else {\n      // Při přednastaveném období nejsou pole povinná\n      fromDateControl?.clearValidators();\n      toDateControl?.clearValidators();\n    }\n\n    fromDateControl?.updateValueAndValidity();\n    toDateControl?.updateValueAndValidity();\n  }\n\n  /**\n   * Načtení aktuálního stavu filtru z localStorage\n   */\n  private loadCurrentFilterState(): void {\n    try {\n      const filterKey = `current_filter_${this.logType}`;\n      const filterJson = localStorage.getItem(filterKey);\n\n      if (filterJson) {\n        const savedFilter = JSON.parse(filterJson);\n\n        // Detekce starého formátu filtru (bez pole period)\n        if (savedFilter.fromDate && savedFilter.toDate && !savedFilter.period) {\n          // Pokusíme se detekovat období podle rozdílu dat\n          const fromDate = new Date(savedFilter.fromDate);\n          const toDate = new Date(savedFilter.toDate);\n          const diffDays = Math.round((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));\n\n          // Pokud je rozdíl blízký některému z předdefinovaných období, použijeme ho\n          if (diffDays <= 1) {\n            savedFilter.period = 1;\n          } else if (diffDays <= 7) {\n            savedFilter.period = 7;\n          } else if (diffDays <= 30) {\n            savedFilter.period = 30;\n          } else if (diffDays <= 90) {\n            savedFilter.period = 90;\n          } else {\n            savedFilter.period = 'custom';\n          }\n        }\n\n        // Převod objektů Date na řetězce pro input\n        if (savedFilter.fromDate) {\n          const fromDate = new Date(savedFilter.fromDate);\n          savedFilter.fromDate = this.formatDateForInput(fromDate);\n        }\n\n        if (savedFilter.toDate) {\n          const toDate = new Date(savedFilter.toDate);\n          savedFilter.toDate = this.formatDateForInput(toDate);\n        }\n\n        // Nastavení hodnot formuláře\n        this.filterForm.patchValue(savedFilter);\n\n        // Aktualizace validace datumových polí\n        this.updateDateFieldsValidation(savedFilter.period);\n\n        // Aktualizace indikátoru aktivních filtrů\n        this.updateActiveFiltersIndicator();\n      }\n    } catch (error) {\n      console.error(`Chyba při načítání stavu filtru pro ${this.logType}`, error);\n    }\n  }\n\n  /**\n   * Přepne viditelnost filtru a uloží stav do localStorage\n   */\n  toggleFilterVisibility(): void {\n    this.isFilterVisible = !this.isFilterVisible;\n\n    // Uložení stavu viditelnosti do localStorage\n    try {\n      const visibilityKey = `filter_visibility_logs_${this.logType}`;\n      localStorage.setItem(visibilityKey, this.isFilterVisible.toString());\n    } catch (error) {\n      console.error('Chyba při ukládání stavu viditelnosti filtru do localStorage', error);\n    }\n  }\n\n  /**\n   * Načte stav viditelnosti filtru z localStorage\n   */\n  private loadFilterVisibilityState(): void {\n    try {\n      const visibilityKey = `filter_visibility_logs_${this.logType}`;\n      const savedVisibility = localStorage.getItem(visibilityKey);\n\n      if (savedVisibility !== null) {\n        this.isFilterVisible = savedVisibility === 'true';\n      }\n    } catch (error) {\n      console.error('Chyba při načítání stavu viditelnosti filtru z localStorage', error);\n    }\n  }\n\n  /**\n   * Aktualizuje indikátor aktivních filtrů\n   */\n  private updateActiveFiltersIndicator(): void {\n    const values = this.filterForm.value;\n\n    // Získání výchozích hodnot pro časové filtry\n    const today = new Date();\n    const endOfDay = new Date(today);\n    endOfDay.setHours(23, 59, 59, 999);\n\n    const sevenDaysAgo = new Date(today);\n    sevenDaysAgo.setDate(today.getDate() - 7);\n    sevenDaysAgo.setHours(0, 0, 0, 0);\n\n    // Formátování dat pro porovnání\n    const defaultFromDateStr = this.formatDateForInput(sevenDaysAgo);\n    const defaultToDateStr = this.formatDateForInput(endOfDay);\n\n    // Výchozí hodnota pro období\n    const defaultPeriod = 7;\n\n    // Odstranění prázdných hodnot a výchozích hodnot\n    const activeFilters = Object.keys(values)\n      .filter(key => {\n        // Ignorujeme maxResults s hodnotou 100, což je výchozí hodnota\n        if (key === 'maxResults' && values[key] === 100) {\n          return false;\n        }\n\n        // Ignorujeme source s výchozí hodnotou podle typu logu\n        if (key === 'source') {\n          if (this.logType === 'activity' && values[key] === LogSource.DISAdmin) {\n            return false;\n          }\n          if (this.logType === 'api' && values[key] === LogSource.DISApi) {\n            return false;\n          }\n        }\n\n        // Ignorujeme výchozí hodnotu pro období\n        if (key === 'period' && values[key] === defaultPeriod) {\n          return false;\n        }\n\n        // Při přednastaveném období ignorujeme fromDate a toDate\n        if (values.period !== 'custom' && (key === 'fromDate' || key === 'toDate')) {\n          return false;\n        }\n\n        // Při vlastním období kontrolujeme výchozí hodnoty\n        if (values.period === 'custom') {\n          if (key === 'fromDate' && values[key] === defaultFromDateStr) {\n            return false;\n          }\n          if (key === 'toDate' && values[key] === defaultToDateStr) {\n            return false;\n          }\n        }\n\n        // Ignorujeme null, prázdné řetězce a undefined\n        return values[key] !== null && values[key] !== '' && values[key] !== undefined;\n      })\n      .reduce((obj: any, key) => {\n        obj[key] = values[key];\n        return obj;\n      }, {});\n\n    this.activeFilterCount = Object.keys(activeFilters).length;\n    this.hasActiveFilters = this.activeFilterCount > 0;\n\n    // Výpis aktivních filtrů do konzole pro ladění\n    if (this.hasActiveFilters) {\n      console.log('Aktivní filtry:', activeFilters);\n    }\n  }\n\n  /**\n   * Vytvoření formuláře pro filtrování\n   */\n  private createFilterForm(): FormGroup {\n    // Nastavení výchozích hodnot pro časové filtry\n    const today = new Date();\n    const endOfDay = new Date(today);\n    endOfDay.setHours(23, 59, 59, 999);\n\n    const sevenDaysAgo = new Date(today);\n    sevenDaysAgo.setDate(today.getDate() - 7);\n    sevenDaysAgo.setHours(0, 0, 0, 0);\n\n    // Formátování dat pro input typu datetime-local\n    const fromDateStr = this.formatDateForInput(sevenDaysAgo);\n    const toDateStr = this.formatDateForInput(endOfDay);\n\n    // Základní pole pro všechny typy logů\n    const formControls: any = {\n      period: [7], // Výchozí hodnota je 7 dní\n      fromDate: [fromDateStr],\n      toDate: [toDateStr],\n      userId: [null],\n      maxResults: [100],\n      source: [null]\n    };\n\n    // Nastavení výchozí hodnoty pro zdroj podle typu logu\n    if (this.logType === 'activity') {\n      formControls.source = [LogSource.DISAdmin];\n    } else if (this.logType === 'api') {\n      formControls.source = [LogSource.DISApi];\n    }\n\n    // Přidání specifických polí podle typu logu\n    if (this.logType === 'activity' || this.logType === 'api') {\n      formControls.entityName = [''];\n      formControls.entityId = [null];\n      formControls.activityType = [''];\n    }\n\n    // Přidání polí pro error logy\n    if (this.logType === 'error') {\n      formControls.logLevel = [null];\n      formControls.category = [''];\n    }\n\n    return this.fb.group(formControls);\n  }\n\n  /**\n   * Načtení uživatelů pro filtr\n   */\n  loadUsers(): void {\n    this.userService.getUsers().subscribe({\n      next: (users) => {\n        this.users = users;\n      },\n      error: (err) => {\n        console.error('Chyba při načítání uživatelů', err);\n      }\n    });\n  }\n\n  /**\n   * Načtení uložených filtrů\n   */\n  loadSavedFilters(): void {\n    this.logsService.getSavedLogFilters(this.logType).subscribe({\n      next: (filters) => {\n        this.savedFilters = filters;\n      },\n      error: (err) => {\n        console.error('Chyba při načítání uložených filtrů', err);\n      }\n    });\n  }\n\n  /**\n   * Aplikování filtru\n   */\n  applyFilter(): void {\n    const formValues = this.filterForm.value;\n    const filterData: LogFilterRequest = { ...formValues };\n\n    // Zpracování časového období\n    if (formValues.period !== 'custom') {\n      // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období\n      const today = new Date();\n      const endOfDay = new Date(today);\n      endOfDay.setHours(23, 59, 59, 999);\n\n      const fromDate = new Date(today);\n      fromDate.setDate(today.getDate() - Number(formValues.period));\n      fromDate.setHours(0, 0, 0, 0);\n\n      filterData.fromDate = fromDate;\n      filterData.toDate = endOfDay;\n    } else {\n      // Při vlastním období použijeme zadané hodnoty\n      if (formValues.fromDate) {\n        filterData.fromDate = new Date(formValues.fromDate);\n      }\n\n      if (formValues.toDate) {\n        filterData.toDate = new Date(formValues.toDate);\n      }\n    }\n\n    // Uložení stavu filtru do localStorage\n    this.saveFilterState(filterData);\n\n    this.filterChange.emit(filterData);\n  }\n\n  /**\n   * Načtení dat podle aktuálního filtru\n   */\n  refreshData(): void {\n    // Aplikujeme aktuální filtr znovu\n    this.applyFilter();\n  }\n\n  /**\n   * Uložení stavu filtru do localStorage\n   */\n  private saveFilterState(filterData: LogFilterRequest): void {\n    try {\n      const filterKey = `current_filter_${this.logType}`;\n      localStorage.setItem(filterKey, JSON.stringify(filterData));\n    } catch (error) {\n      console.error(`Chyba při ukládání stavu filtru pro ${this.logType}`, error);\n    }\n  }\n\n  /**\n   * Resetování filtru\n   */\n  resetFilter(): void {\n    // Nastavení výchozích hodnot pro časové filtry\n    const today = new Date();\n    const endOfDay = new Date(today);\n    endOfDay.setHours(23, 59, 59, 999);\n\n    const sevenDaysAgo = new Date(today);\n    sevenDaysAgo.setDate(today.getDate() - 7);\n    sevenDaysAgo.setHours(0, 0, 0, 0);\n\n    // Formátování dat pro input typu datetime-local\n    const fromDateStr = this.formatDateForInput(sevenDaysAgo);\n    const toDateStr = this.formatDateForInput(endOfDay);\n\n    // Základní hodnoty pro reset\n    const resetValues: any = {\n      period: 7, // Výchozí hodnota je 7 dní\n      fromDate: fromDateStr,\n      toDate: toDateStr,\n      maxResults: 100\n    };\n\n    // Nastavení výchozí hodnoty pro zdroj podle typu logu\n    if (this.logType === 'activity') {\n      resetValues.source = LogSource.DISAdmin;\n    } else if (this.logType === 'api') {\n      resetValues.source = LogSource.DISApi;\n    }\n\n    this.filterForm.reset(resetValues);\n    this.updateActiveFiltersIndicator();\n    this.applyFilter();\n\n    // Zobrazení notifikace o resetování filtru\n    this.toastr.info('Filtr byl resetován', 'Reset');\n  }\n\n  /**\n   * Formátuje datum pro input typu datetime-local\n   * Format: YYYY-MM-DDThh:mm\n   */\n  private formatDateForInput(date: Date): string {\n    const year = date.getFullYear();\n    const month = (date.getMonth() + 1).toString().padStart(2, '0');\n    const day = date.getDate().toString().padStart(2, '0');\n    const hours = date.getHours().toString().padStart(2, '0');\n    const minutes = date.getMinutes().toString().padStart(2, '0');\n\n    return `${year}-${month}-${day}T${hours}:${minutes}`;\n  }\n\n  /**\n   * Otevření modálního okna pro uložení filtru\n   */\n  openSaveFilterModal(): void {\n    this.saveFilterForm.reset();\n    this.modalService.open('saveFilterModal');\n  }\n\n  /**\n   * Uložení filtru\n   */\n  saveFilter(): void {\n    if (this.saveFilterForm.invalid) {\n      return;\n    }\n\n    const filterName = this.saveFilterForm.get('name')?.value;\n    const filterData = this.filterForm.value;\n\n    const saveRequest: SavedLogFilterRequest = {\n      name: filterName,\n      logType: this.logType,\n      filterData: JSON.stringify(filterData)\n    };\n\n    this.isLoading = true;\n    this.logsService.saveLogFilter(saveRequest).subscribe({\n      next: (response) => {\n        this.savedFilters.push(response);\n        this.modalService.close('saveFilterModal');\n        this.isLoading = false;\n      },\n      error: (err) => {\n        console.error('Chyba při ukládání filtru', err);\n        this.error = 'Nepodařilo se uložit filtr';\n        this.isLoading = false;\n      }\n    });\n  }\n\n  /**\n   * Aplikování uloženého filtru\n   */\n  applySavedFilter(filter: SavedLogFilterResponse): void {\n    try {\n      const parsedData = JSON.parse(filter.filterData);\n\n      // Vytvoříme nový objekt pro formulář s řetězcovými hodnotami pro datetime-local inputy\n      const formValues: any = { ...parsedData };\n\n      // Detekce starého formátu filtru (bez pole period)\n      if (parsedData.fromDate && parsedData.toDate && !parsedData.period) {\n        // Pokusíme se detekovat období podle rozdílu dat\n        const fromDate = new Date(parsedData.fromDate);\n        const toDate = new Date(parsedData.toDate);\n        const diffDays = Math.round((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));\n\n        // Pokud je rozdíl blízký některému z předdefinovaných období, použijeme ho\n        if (diffDays <= 1) {\n          formValues.period = 1;\n        } else if (diffDays <= 7) {\n          formValues.period = 7;\n        } else if (diffDays <= 30) {\n          formValues.period = 30;\n        } else if (diffDays <= 90) {\n          formValues.period = 90;\n        } else {\n          formValues.period = 'custom';\n        }\n      }\n\n      // Převod objektů Date na řetězce pro input\n      if (parsedData.fromDate) {\n        const fromDate = new Date(parsedData.fromDate);\n        formValues.fromDate = this.formatDateForInput(fromDate);\n      }\n      if (parsedData.toDate) {\n        const toDate = new Date(parsedData.toDate);\n        formValues.toDate = this.formatDateForInput(toDate);\n      }\n\n      // Resetujeme formulář, aby se vyčistily všechny hodnoty\n      this.filterForm.reset({\n        maxResults: 100\n      });\n\n      // Nastavíme hodnoty formuláře\n      this.filterForm.patchValue(formValues);\n\n      // Zajistíme, že zdroj je správně nastaven podle typu logu\n      if (this.logType === 'activity') {\n        this.filterForm.patchValue({ source: LogSource.DISAdmin });\n      } else if (this.logType === 'api') {\n        this.filterForm.patchValue({ source: LogSource.DISApi });\n      }\n\n      // Aktualizace validace datumových polí\n      this.updateDateFieldsValidation(formValues.period);\n\n      // Aktualizujeme indikátor aktivních filtrů\n      this.updateActiveFiltersIndicator();\n\n      // Aplikujeme filtr, který převede řetězce zpět na Date objekty\n      this.applyFilter();\n    } catch (error) {\n      console.error('Chyba při aplikování uloženého filtru', error);\n    }\n  }\n\n  /**\n   * Odstranění uloženého filtru\n   */\n  async deleteSavedFilter(filter: SavedLogFilterResponse, event: Event): Promise<void> {\n    event.stopPropagation(); // Zabránění aplikování filtru při kliknutí na tlačítko smazat\n\n    // Použijeme vlastní dialog pro potvrzení místo nativního confirm\n    const confirmed = await this.modalService.confirm(\n      `Opravdu chcete smazat filtr \"${filter.name}\"?`,\n      'Odstranit filtr',\n      'OK',\n      'Zrušit',\n      'btn-danger',\n      'btn-secondary'\n    );\n\n    if (confirmed) {\n      this.logsService.deleteLogFilter(filter.id).subscribe({\n        next: () => {\n          this.savedFilters = this.savedFilters.filter(f => f.id !== filter.id);\n        },\n        error: (err) => {\n          console.error('Chyba při mazání filtru', err);\n        }\n      });\n    }\n  }\n}\n", "<div class=\"card mb-4\">\n  <div class=\"card-header d-flex justify-content-between align-items-center\">\n    <h5 class=\"mb-0 filter-title\" (click)=\"toggleFilterVisibility()\">\n      <i class=\"bi fs-5 me-2\" [ngClass]=\"{'bi-funnel': !hasActiveFilters, 'bi-funnel-fill text-primary': hasActiveFilters}\"></i>\n      <span>Filtr</span>\n      <span *ngIf=\"hasActiveFilters\" class=\"badge bg-danger ms-2 badge-smaller\">{{ activeFilterCount }}</span>\n      <i class=\"bi ms-2\" [ngClass]=\"{'bi-chevron-up': isFilterVisible, 'bi-chevron-down': !isFilterVisible}\"></i>\n    </h5>\n    <div>\n      <button class=\"btn btn-sm btn-outline-primary me-4\" (click)=\"openSaveFilterModal()\">\n        <i class=\"bi bi-save me-1\"></i>Uložit filtr\n      </button>\n      <button class=\"btn btn-sm btn-outline-secondary me-2\" (click)=\"resetFilter()\">\n        <i class=\"bi bi-arrow-counterclockwise me-1\"></i>Reset\n      </button>\n      <button class=\"btn btn-sm btn-outline-primary\" (click)=\"refreshData()\">\n        <i class=\"bi bi-arrow-repeat me-1\"></i>Načíst data\n      </button>\n    </div>\n  </div>\n  <div class=\"card-body\" *ngIf=\"isFilterVisible\">\n    <form [formGroup]=\"filterForm\">\n      <div class=\"row\">\n        <!-- Uživatel -->\n        <div class=\"col-md-6 col-lg-3 mb-3\">\n          <label for=\"userId\" class=\"form-label\">Uživatel</label>\n          <select class=\"form-select\" id=\"userId\" formControlName=\"userId\">\n            <option [ngValue]=\"null\">Všichni uživatelé</option>\n            <option *ngFor=\"let user of users\" [value]=\"user.id\">{{ user.username }}</option>\n          </select>\n        </div>\n\n        <!-- Zdroj logu -->\n        <div class=\"col-md-6 col-lg-3 mb-3\">\n          <label for=\"source\" class=\"form-label\">Zdroj</label>\n          <select class=\"form-select\" id=\"source\" formControlName=\"source\">\n            <option [ngValue]=\"null\">Všechny zdroje</option>\n            <option *ngFor=\"let source of logSources\" [ngValue]=\"source.value\">{{ source.label }}</option>\n          </select>\n        </div>\n\n        <!-- Specifická pole pro auditní logy a API logy -->\n        <ng-container *ngIf=\"logType === 'activity' || logType === 'api'\">\n          <!-- Typ aktivity -->\n          <div class=\"col-md-6 col-lg-3 mb-3\">\n            <label for=\"activityType\" class=\"form-label\">Typ aktivity</label>\n            <select class=\"form-select\" id=\"activityType\" formControlName=\"activityType\">\n              <option [ngValue]=\"''\">Všechny typy</option>\n              <option *ngFor=\"let type of activityTypes\" [value]=\"type.value\">{{ type.label }}</option>\n            </select>\n          </div>\n\n          <!-- Entita -->\n          <div class=\"col-md-6 col-lg-3 mb-3\">\n            <label for=\"entityName\" class=\"form-label\">Entita</label>\n            <input type=\"text\" class=\"form-control\" id=\"entityName\" formControlName=\"entityName\" placeholder=\"Např. customers, versions...\">\n          </div>\n\n          <!-- ID entity -->\n          <div class=\"col-md-6 col-lg-3 mb-3\">\n            <label for=\"entityId\" class=\"form-label\">ID entity</label>\n            <input type=\"number\" class=\"form-control\" id=\"entityId\" formControlName=\"entityId\">\n          </div>\n        </ng-container>\n\n        <!-- Specifická pole pro logy aplikace (error) -->\n        <ng-container *ngIf=\"logType === 'error'\">\n          <!-- Úroveň logování -->\n          <div class=\"col-md-6 col-lg-3 mb-3\" *ngIf=\"showLogLevelFilter\">\n            <label for=\"logLevel\" class=\"form-label\">Úroveň logování</label>\n            <select class=\"form-select\" id=\"logLevel\" formControlName=\"logLevel\">\n              <option [ngValue]=\"null\">Všechny úrovně</option>\n              <option *ngFor=\"let level of logLevels\" [value]=\"level.value\">{{ level.label }}</option>\n            </select>\n          </div>\n\n          <!-- Kategorie -->\n          <div class=\"col-md-6 col-lg-3 mb-3\" *ngIf=\"showCategoryFilter\">\n            <label for=\"category\" class=\"form-label\">Kategorie</label>\n            <input type=\"text\" class=\"form-control\" id=\"category\" formControlName=\"category\" placeholder=\"Např. DISAdmin.Api.Controllers...\">\n          </div>\n        </ng-container>\n\n        <!-- Počet záznamů -->\n        <div class=\"col-md-6 col-lg-3 mb-3\">\n          <label for=\"maxResults\" class=\"form-label\">Počet záznamů</label>\n          <input type=\"number\" class=\"form-control\" id=\"maxResults\" formControlName=\"maxResults\" min=\"1\" max=\"1000\">\n        </div>\n\n        <!-- Časové období -->\n        <div class=\"col-md-6 col-lg-3 mb-3\">\n          <label for=\"period\" class=\"form-label\">Časové období</label>\n          <select class=\"form-select\" id=\"period\" formControlName=\"period\">\n            <option *ngFor=\"let option of periodOptions\" [value]=\"option.value\">{{ option.label }}</option>\n          </select>\n        </div>\n\n        <!-- Datumová pole pro vlastní období -->\n        <div class=\"col-md-6 col-lg-3 mb-3\" *ngIf=\"filterForm.get('period')?.value === 'custom'\">\n          <label for=\"fromDate\" class=\"form-label\">Od</label>\n          <input type=\"datetime-local\" class=\"form-control\" id=\"fromDate\" formControlName=\"fromDate\"\n                 [ngClass]=\"{'is-invalid': filterForm.get('fromDate')?.invalid && filterForm.get('fromDate')?.touched}\">\n          <div class=\"invalid-feedback\" *ngIf=\"filterForm.get('fromDate')?.errors?.['required']\">\n            Zadejte počáteční datum\n          </div>\n        </div>\n        <div class=\"col-md-6 col-lg-3 mb-3\" *ngIf=\"filterForm.get('period')?.value === 'custom'\">\n          <label for=\"toDate\" class=\"form-label\">Do</label>\n          <input type=\"datetime-local\" class=\"form-control\" id=\"toDate\" formControlName=\"toDate\"\n                 [ngClass]=\"{'is-invalid': filterForm.get('toDate')?.invalid && filterForm.get('toDate')?.touched}\">\n          <div class=\"invalid-feedback\" *ngIf=\"filterForm.get('toDate')?.errors?.['required']\">\n            Zadejte koncové datum\n          </div>\n        </div>\n      </div>\n    </form>\n\n    <!-- Uložené filtry -->\n    <div class=\"mt-3\" *ngIf=\"savedFilters.length > 0\">\n      <h6>Uložené filtry</h6>\n      <div class=\"saved-filters\">\n        <div class=\"saved-filter\" *ngFor=\"let filter of savedFilters\" (click)=\"applySavedFilter(filter)\">\n          <span class=\"filter-name\">{{ filter.name }}</span>\n          <button class=\"btn btn-sm btn-link text-danger\" (click)=\"deleteSavedFilter(filter, $event)\">\n            <i class=\"bi bi-trash\"></i>\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modální okno pro uložení filtru -->\n<div class=\"modal fade\" id=\"saveFilterModal\" tabindex=\"-1\" aria-labelledby=\"saveFilterModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\" id=\"saveFilterModalLabel\">Uložit filtr</h5>\n        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <form [formGroup]=\"saveFilterForm\">\n          <div class=\"mb-3\">\n            <label for=\"filterName\" class=\"form-label\">Název filtru</label>\n            <input type=\"text\" class=\"form-control\" id=\"filterName\" formControlName=\"name\" required>\n          </div>\n          <div class=\"alert alert-danger\" *ngIf=\"error\">{{ error }}</div>\n        </form>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Zrušit</button>\n        <button type=\"button\" class=\"btn btn-primary\" [disabled]=\"saveFilterForm.invalid || isLoading\" (click)=\"saveFilter()\">\n          <span *ngIf=\"isLoading\" class=\"spinner-border spinner-border-sm me-1\" role=\"status\" aria-hidden=\"true\"></span>\n          Uložit\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}