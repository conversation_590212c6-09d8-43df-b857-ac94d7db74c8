"use strict";(self.webpackChunkDISAdmin_Web=self.webpackChunkDISAdmin_Web||[]).push([[177],{6177:(B,l,s)=>{s.r(l),s.d(l,{AdminModule:()=>Q});var c=s(6895),p=s(6123),u=s(433),e=s(1571),f=s(1612),v=s(7185),m=s(7556),g=s(6026),Z=s(18);function _(i,o){1&i&&(e.TgZ(0,"div",15),e._UZ(1,"i",16),e.TgZ(2,"strong"),e._uU(3,"P\u0159\xedstup odep\u0159en!"),e.qZA(),e._uU(4," Pro p\u0159\xedstup k t\xe9to funkci mus\xedte b\xfdt administr\xe1tor. "),e.qZA())}function b(i,o){1&i&&(e.TgZ(0,"div",3)(1,"div",4)(2,"h2"),e._uU(3,"Spr\xe1va serverov\xe9ho certifik\xe1tu"),e.qZA(),e.TgZ(4,"p",17),e._uU(5," Serverov\xfd certifik\xe1t je pou\u017e\xedv\xe1n pro zabezpe\u010den\xed HTTPS komunikace mezi klienty a DISApi serverem. "),e.qZA()()())}function h(i,o){1&i&&(e.TgZ(0,"div",18)(1,"div",19)(2,"span",20),e._uU(3,"Na\u010d\xedt\xe1n\xed..."),e.qZA()()())}function T(i,o){1&i&&(e.TgZ(0,"div",21),e._UZ(1,"i",16),e.TgZ(2,"strong"),e._uU(3,"Serverov\xfd certifik\xe1t nen\xed nakonfigurov\xe1n nebo neexistuje."),e.qZA(),e._uU(4," Vygenerujte nov\xfd certifik\xe1t pomoc\xed formul\xe1\u0159e n\xed\u017ee. "),e.qZA())}function A(i,o){1&i&&(e.TgZ(0,"span",30),e._uU(1,"Platn\xfd"),e.qZA())}function E(i,o){1&i&&(e.TgZ(0,"span",31),e._uU(1,"Neplatn\xfd"),e.qZA())}function x(i,o){if(1&i&&(e.TgZ(0,"div",22)(1,"div",23),e._uU(2,"Cesta k souboru:"),e.qZA(),e.TgZ(3,"div",24),e._uU(4),e.qZA()()),2&i){const t=e.oxw(2);e.xp6(4),e.Oqu(t.certificateInfo.filePath)}}function C(i,o){if(1&i&&(e.TgZ(0,"div")(1,"div",22)(2,"div",23),e._uU(3,"Subject:"),e.qZA(),e.TgZ(4,"div",24),e._uU(5),e.qZA()(),e.TgZ(6,"div",22)(7,"div",23),e._uU(8,"Vydavatel:"),e.qZA(),e.TgZ(9,"div",24),e._uU(10),e.qZA()(),e.TgZ(11,"div",22)(12,"div",23),e._uU(13,"Thumbprint:"),e.qZA(),e.TgZ(14,"div",24),e._uU(15),e.qZA()(),e.TgZ(16,"div",22)(17,"div",23),e._uU(18,"S\xe9riov\xe9 \u010d\xedslo:"),e.qZA(),e.TgZ(19,"div",24),e._uU(20),e.qZA()(),e.TgZ(21,"div",22)(22,"div",23),e._uU(23,"Platnost od:"),e.qZA(),e.TgZ(24,"div",24),e._uU(25),e.ALo(26,"date"),e.qZA()(),e.TgZ(27,"div",22)(28,"div",23),e._uU(29,"Platnost do:"),e.qZA(),e.TgZ(30,"div",25),e._uU(31),e.ALo(32,"localDate"),e.TgZ(33,"span",26),e._uU(34),e.qZA()()(),e.TgZ(35,"div",22)(36,"div",23),e._uU(37,"Obsahuje priv\xe1tn\xed kl\xed\u010d:"),e.qZA(),e.TgZ(38,"div",24),e._uU(39),e.qZA()(),e.TgZ(40,"div",22)(41,"div",23),e._uU(42,"Stav:"),e.qZA(),e.TgZ(43,"div",24),e.YNc(44,A,2,0,"span",27),e.YNc(45,E,2,0,"span",28),e.qZA()(),e.TgZ(46,"div",22)(47,"div",23),e._uU(48,"Typ \xfalo\u017ei\u0161t\u011b:"),e.qZA(),e.TgZ(49,"div",24),e._uU(50),e.qZA()(),e.TgZ(51,"div",22)(52,"div",23),e._uU(53,"Um\xedst\u011bn\xed:"),e.qZA(),e.TgZ(54,"div",24),e._uU(55),e.qZA()(),e.YNc(56,x,5,1,"div",29),e.qZA()),2&i){const t=e.oxw();e.xp6(5),e.Oqu(t.certificateInfo.subject),e.xp6(5),e.Oqu(t.certificateInfo.issuer),e.xp6(5),e.Oqu(t.certificateInfo.thumbprint),e.xp6(5),e.Oqu(t.certificateInfo.serialNumber),e.xp6(5),e.Oqu(e.xi3(26,14,t.certificateInfo.notBefore,"dd.MM.yyyy HH:mm:ss")),e.xp6(5),e.Q6J("ngClass",t.getExpirationClass()),e.xp6(1),e.hij(" ",e.xi3(32,17,t.certificateInfo.notAfter,"dd.MM.yyyy HH:mm:ss")," "),e.xp6(3),e.hij("(",t.getDaysToExpiration()," dn\xed)"),e.xp6(5),e.Oqu(t.certificateInfo.hasPrivateKey?"Ano":"Ne"),e.xp6(5),e.Q6J("ngIf",t.certificateInfo.isValid),e.xp6(1),e.Q6J("ngIf",!t.certificateInfo.isValid),e.xp6(5),e.Oqu(t.certificateInfo.storageType),e.xp6(5),e.Oqu(t.certificateInfo.storageLocation),e.xp6(1),e.Q6J("ngIf","File"===t.certificateInfo.storageType&&t.certificateInfo.filePath)}}function U(i,o){1&i&&(e.TgZ(0,"div",56),e._uU(1," Common Name je povinn\xfd. "),e.qZA())}function q(i,o){1&i&&(e.TgZ(0,"div",56),e._uU(1," Platnost mus\xed b\xfdt mezi 1 a 3650 dny. "),e.qZA())}function D(i,o){1&i&&(e.TgZ(0,"div",57)(1,"div",41),e._UZ(2,"input",58),e.TgZ(3,"label",59),e._uU(4,"CurrentUser (aktu\xe1ln\xed u\u017eivatel)"),e.qZA(),e.TgZ(5,"div",36),e._uU(6,"Certifik\xe1t bude dostupn\xfd pouze pro aktu\xe1ln\xedho u\u017eivatele. Nevy\u017eaduje administr\xe1torsk\xe1 pr\xe1va."),e.qZA()(),e.TgZ(7,"div",44),e._UZ(8,"input",60),e.TgZ(9,"label",61),e._uU(10,"LocalMachine (v\u0161ichni u\u017eivatel\xe9)"),e.qZA(),e.TgZ(11,"div",36),e._uU(12,"Certifik\xe1t bude dostupn\xfd pro v\u0161echny u\u017eivatele. Vy\u017eaduje administr\xe1torsk\xe1 pr\xe1va."),e.qZA()()())}function y(i,o){1&i&&e._UZ(0,"span",62)}function k(i,o){if(1&i){const t=e.EpF();e.TgZ(0,"div",3)(1,"div",4)(2,"div",5)(3,"div",6)(4,"h5",7),e._uU(5,"Generov\xe1n\xed nov\xe9ho certifik\xe1tu"),e.qZA()(),e.TgZ(6,"div",8)(7,"form",32),e.NdJ("ngSubmit",function(){e.CHM(t);const n=e.oxw();return e.KtG(n.generateCertificate())}),e.TgZ(8,"div",33)(9,"label",34),e._uU(10,"Common Name (CN)"),e.qZA(),e._UZ(11,"input",35),e.TgZ(12,"div",36),e._uU(13,"Dom\xe9nov\xe9 jm\xe9no nebo n\xe1zev serveru, pro kter\xfd bude certifik\xe1t vyd\xe1n. Zadejte URL adresu DISApi, na kterou p\u0159istupuje DIS aplikace (nap\u0159. disapi.disadmin.cz). Tato hodnota mus\xed p\u0159esn\u011b odpov\xeddat dom\xe9n\u011b, kterou DIS aplikace pou\u017e\xedv\xe1 pro p\u0159ipojen\xed k DISApi."),e.qZA(),e.YNc(14,U,2,0,"div",37),e.qZA(),e.TgZ(15,"div",33)(16,"label",38),e._uU(17,"Platnost (dny)"),e.qZA(),e._UZ(18,"input",39),e.TgZ(19,"div",36),e._uU(20,"Po\u010det dn\xed, po kter\xe9 bude certifik\xe1t platn\xfd. Doporu\u010deno: 365 dn\xed (1 rok)."),e.qZA(),e.YNc(21,q,2,0,"div",37),e.qZA(),e.TgZ(22,"div",33)(23,"label",40),e._uU(24,"\xdalo\u017ei\u0161t\u011b certifik\xe1tu"),e.qZA(),e.TgZ(25,"div",41),e._UZ(26,"input",42),e.TgZ(27,"label",43),e._uU(28,"Ulo\u017eit do datab\xe1ze (doporu\u010deno)"),e.qZA(),e.TgZ(29,"div",36),e._uU(30,"Certifik\xe1t bude ulo\u017een p\u0159\xedmo v datab\xe1zi. Toto je nejbezpe\u010dn\u011bj\u0161\xed a nejspolehliv\u011bj\u0161\xed zp\u016fsob."),e.qZA()(),e.TgZ(31,"div",44),e._UZ(32,"input",45),e.TgZ(33,"label",46),e._uU(34,"Ulo\u017eit jako soubor"),e.qZA(),e.TgZ(35,"div",36),e._uU(36,"Certifik\xe1t bude ulo\u017een jako soubor .pfx v adres\xe1\u0159i aplikace."),e.qZA()(),e.TgZ(37,"div",44),e._UZ(38,"input",47),e.TgZ(39,"label",48),e._uU(40,"Ulo\u017eit do \xfalo\u017ei\u0161t\u011b certifik\xe1t\u016f Windows"),e.qZA(),e.TgZ(41,"div",36),e._uU(42,"Certifik\xe1t bude ulo\u017een do \xfalo\u017ei\u0161t\u011b certifik\xe1t\u016f Windows."),e.qZA()(),e.YNc(43,D,13,0,"div",49),e.qZA(),e.TgZ(44,"div",50),e._UZ(45,"input",51),e.TgZ(46,"label",52),e._uU(47,"Aktualizovat konfiguraci"),e.qZA(),e.TgZ(48,"div",36),e._uU(49,"Konfigurace aplikace bude aktualizov\xe1na s nov\xfdm certifik\xe1tem."),e.qZA()(),e.TgZ(50,"div",21),e._UZ(51,"i",16),e.TgZ(52,"strong"),e._uU(53,"Upozorn\u011bn\xed:"),e.qZA(),e._uU(54," Generov\xe1n\xed nov\xe9ho serverov\xe9ho certifik\xe1tu m\u016f\u017ee vy\u017eadovat restart aplikace, aby se zm\u011bny projevily. "),e.qZA(),e.TgZ(55,"div",53),e._UZ(56,"i",16),e.TgZ(57,"strong"),e._uU(58,"D\u016eLE\u017dIT\xc9 VAROV\xc1N\xcd:"),e.qZA(),e._uU(59," Zm\u011bna serverov\xe9ho certifik\xe1tu zp\u016fsob\xed, \u017ee v\u0161echny existuj\xedc\xed klientsk\xe9 certifik\xe1ty p\u0159estanou fungovat! Budete muset p\u0159egenerovat v\u0161echny klientsk\xe9 certifik\xe1ty pro instance DIS. "),e.qZA(),e.TgZ(60,"button",54),e.YNc(61,y,1,0,"span",55),e._uU(62," Generovat certifik\xe1t "),e.qZA()()()()()()}if(2&i){const t=e.oxw();let r,n,a;e.xp6(7),e.Q6J("formGroup",t.generationForm),e.xp6(7),e.Q6J("ngIf",(null==(r=t.generationForm.get("commonName"))?null:r.invalid)&&(null==(r=t.generationForm.get("commonName"))?null:r.touched)),e.xp6(7),e.Q6J("ngIf",(null==(n=t.generationForm.get("validityDays"))?null:n.invalid)&&(null==(n=t.generationForm.get("validityDays"))?null:n.touched)),e.xp6(5),e.Q6J("value",2),e.xp6(6),e.Q6J("value",0),e.xp6(6),e.Q6J("value",1),e.xp6(5),e.Q6J("ngIf",1===(null==(a=t.generationForm.get("storageType"))?null:a.value)),e.xp6(17),e.Q6J("disabled",t.generationForm.invalid||t.generating),e.xp6(1),e.Q6J("ngIf",t.generating)}}function I(i,o){if(1&i){const t=e.EpF();e.TgZ(0,"div",3)(1,"div",4)(2,"div",5)(3,"div",63)(4,"h5",7),e._uU(5,"Certifik\xe1t byl \xfasp\u011b\u0161n\u011b vygenerov\xe1n"),e.qZA()(),e.TgZ(6,"div",8)(7,"div",64),e._uU(8),e.qZA(),e.TgZ(9,"div",22)(10,"div",23),e._uU(11,"Subject:"),e.qZA(),e.TgZ(12,"div",24),e._uU(13),e.qZA()(),e.TgZ(14,"div",22)(15,"div",23),e._uU(16,"Vydavatel:"),e.qZA(),e.TgZ(17,"div",24),e._uU(18),e.qZA()(),e.TgZ(19,"div",22)(20,"div",23),e._uU(21,"Thumbprint:"),e.qZA(),e.TgZ(22,"div",24),e._uU(23),e.qZA()(),e.TgZ(24,"div",22)(25,"div",23),e._uU(26,"Platnost od:"),e.qZA(),e.TgZ(27,"div",24),e._uU(28),e.ALo(29,"date"),e.qZA()(),e.TgZ(30,"div",22)(31,"div",23),e._uU(32,"Platnost do:"),e.qZA(),e.TgZ(33,"div",24),e._uU(34),e.ALo(35,"date"),e.qZA()(),e.TgZ(36,"div",22)(37,"div",23),e._uU(38,"Cesta k souboru:"),e.qZA(),e.TgZ(39,"div",24),e._uU(40),e.qZA()(),e.TgZ(41,"div",22)(42,"div",23),e._uU(43,"Heslo:"),e.qZA(),e.TgZ(44,"div",24)(45,"div",65),e._UZ(46,"input",66),e.TgZ(47,"button",67),e.NdJ("click",function(){e.CHM(t);const n=e.oxw();return e.KtG(n.togglePasswordVisibility())}),e._UZ(48,"i",68),e.qZA()()()(),e.TgZ(49,"div",69),e._UZ(50,"i",16),e.TgZ(51,"strong"),e._uU(52,"D\u016fle\u017eit\xe9:"),e.qZA(),e._uU(53," Ulo\u017ete si heslo k certifik\xe1tu na bezpe\u010dn\xe9 m\xedsto. Po zav\u0159en\xed tohoto okna ji\u017e nebude mo\u017en\xe9 heslo zobrazit. "),e.qZA()(),e.TgZ(54,"div",12)(55,"button",70),e.NdJ("click",function(){e.CHM(t);const n=e.oxw();return e.KtG(n.resetGeneratedCertificate())}),e._UZ(56,"i",71),e._uU(57," Zp\u011bt "),e.qZA()()()()()}if(2&i){const t=e.oxw();e.xp6(8),e.hij(" ",t.generatedCertificate.message," "),e.xp6(5),e.Oqu(t.generatedCertificate.subject),e.xp6(5),e.Oqu(t.generatedCertificate.issuer),e.xp6(5),e.Oqu(t.generatedCertificate.thumbprint),e.xp6(5),e.Oqu(e.xi3(29,10,t.generatedCertificate.notBefore,"dd.MM.yyyy HH:mm:ss")),e.xp6(6),e.Oqu(e.xi3(35,13,t.generatedCertificate.notAfter,"dd.MM.yyyy HH:mm:ss")),e.xp6(6),e.Oqu(t.generatedCertificate.filePath),e.xp6(6),e.Q6J("type",t.showPassword?"text":"password")("value",t.generatedCertificate.password),e.xp6(2),e.Q6J("ngClass",t.showPassword?"bi-eye-slash":"bi-eye")}}let S=(()=>{const o=class{constructor(r,n,a,d,Y){this.certificateService=r,this.formBuilder=n,this.toastr=a,this.authService=d,this.titleService=Y,this.certificateInfo=null,this.loading=!1,this.generating=!1,this.generatedCertificate=null,this.showPassword=!1,this.isAdmin=!1,this.generationForm=this.formBuilder.group({commonName:["",[u.kI.required]],validityDays:[365,[u.kI.required,u.kI.min(1),u.kI.max(3650)]],saveToStore:[!1],storeLocation:["CurrentUser"],updateConfiguration:[!0,[u.kI.required]],storageType:[2,[u.kI.required]]})}ngOnInit(){this.titleService.setTitle("Serverov\xfd certifik\xe1t"),this.isAdmin=this.authService.isAdmin(),console.log("Je u\u017eivatel admin?",this.isAdmin),this.isAdmin?this.loadCertificateInfo():this.toastr.error("Pro p\u0159\xedstup k t\xe9to funkci mus\xedte b\xfdt administr\xe1tor","P\u0159\xedstup odep\u0159en")}loadCertificateInfo(){this.loading=!0,this.certificateService.getServerCertificateInfo().subscribe({next:r=>{this.certificateInfo=r,this.loading=!1},error:r=>{this.toastr.error("Nepoda\u0159ilo se na\u010d\xedst informace o certifik\xe1tu","Chyba"),console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed informac\xed o certifik\xe1tu",r),this.loading=!1}})}generateCertificate(){this.generationForm.invalid?this.toastr.error("Formul\xe1\u0159 obsahuje chyby","Chyba"):(this.generating=!0,this.certificateService.generateServerCertificate(this.generationForm.value).subscribe({next:n=>{this.generatedCertificate=n,this.toastr.success("Certifik\xe1t byl \xfasp\u011b\u0161n\u011b vygenerov\xe1n","\xdasp\u011bch"),this.generating=!1,this.loadCertificateInfo()},error:n=>{this.toastr.error("Nepoda\u0159ilo se vygenerovat certifik\xe1t","Chyba"),console.error("Chyba p\u0159i generov\xe1n\xed certifik\xe1tu",n),this.generating=!1}}))}togglePasswordVisibility(){this.showPassword=!this.showPassword}getDaysToExpiration(){if(!this.certificateInfo)return 0;const r=new Date,a=new Date(this.certificateInfo.notAfter).getTime()-r.getTime();return Math.ceil(a/864e5)}getExpirationClass(){const r=this.getDaysToExpiration();return r<=0?"text-danger":r<=30?"text-warning":"text-success"}resetGeneratedCertificate(){this.generatedCertificate=null}};let i=o;return o.\u0275fac=function(n){return new(n||o)(e.Y36(f.s),e.Y36(u.qu),e.Y36(v._W),e.Y36(m.e),e.Y36(g.y))},o.\u0275cmp=e.Xpm({type:o,selectors:[["app-server-certificate"]],decls:19,vars:8,consts:[[1,"container"],["class","alert alert-danger mb-4",4,"ngIf"],["class","row mb-4",4,"ngIf"],[1,"row","mb-4"],[1,"col"],[1,"card"],[1,"card-header"],[1,"mb-0"],[1,"card-body"],["class","text-center",4,"ngIf"],["class","alert alert-warning",4,"ngIf"],[4,"ngIf"],[1,"card-footer"],[1,"btn","btn-primary",3,"disabled","click"],[1,"bi","bi-arrow-clockwise","me-1"],[1,"alert","alert-danger","mb-4"],[1,"bi","bi-exclamation-triangle-fill","me-2"],[1,"text-muted"],[1,"text-center"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"alert","alert-warning"],[1,"row","mb-3"],[1,"col-md-3","fw-bold"],[1,"col-md-9"],[1,"col-md-9",3,"ngClass"],[1,"ms-2"],["class","badge bg-success",4,"ngIf"],["class","badge bg-danger",4,"ngIf"],["class","row mb-3",4,"ngIf"],[1,"badge","bg-success"],[1,"badge","bg-danger"],[3,"formGroup","ngSubmit"],[1,"mb-3"],["for","commonName",1,"form-label"],["type","text","id","commonName","formControlName","commonName","placeholder","nap\u0159. disapi.disadmin.cz",1,"form-control"],[1,"form-text"],["class","text-danger",4,"ngIf"],["for","validityDays",1,"form-label"],["type","number","id","validityDays","formControlName","validityDays","min","1","max","3650",1,"form-control"],[1,"form-label"],[1,"form-check"],["type","radio","id","storeTypeDatabase","name","storageType","formControlName","storageType",1,"form-check-input",3,"value"],["for","storeTypeDatabase",1,"form-check-label"],[1,"form-check","mt-2"],["type","radio","id","storeTypeFile","name","storageType","formControlName","storageType",1,"form-check-input",3,"value"],["for","storeTypeFile",1,"form-check-label"],["type","radio","id","storeTypeCertStore","name","storageType","formControlName","storageType",1,"form-check-input",3,"value"],["for","storeTypeCertStore",1,"form-check-label"],["class","mt-2",4,"ngIf"],[1,"mb-3","form-check"],["type","checkbox","id","updateConfiguration","formControlName","updateConfiguration",1,"form-check-input"],["for","updateConfiguration",1,"form-check-label"],[1,"alert","alert-danger"],["type","submit",1,"btn","btn-primary",3,"disabled"],["class","spinner-border spinner-border-sm me-1","role","status","aria-hidden","true",4,"ngIf"],[1,"text-danger"],[1,"mt-2"],["type","radio","id","storeLocationCurrentUser","name","storeLocation","value","CurrentUser","formControlName","storeLocation",1,"form-check-input"],["for","storeLocationCurrentUser",1,"form-check-label"],["type","radio","id","storeLocationLocalMachine","name","storeLocation","value","LocalMachine","formControlName","storeLocation",1,"form-check-input"],["for","storeLocationLocalMachine",1,"form-check-label"],["role","status","aria-hidden","true",1,"spinner-border","spinner-border-sm","me-1"],[1,"card-header","bg-success","text-white"],[1,"alert","alert-success"],[1,"input-group"],["readonly","",1,"form-control",3,"type","value"],["type","button",1,"btn","btn-outline-secondary",3,"click"],[1,"bi",3,"ngClass"],[1,"alert","alert-warning","mt-3"],[1,"btn","btn-primary",3,"click"],[1,"bi","bi-arrow-left","me-1"]],template:function(n,a){1&n&&(e.TgZ(0,"div",0),e.YNc(1,_,5,0,"div",1),e.YNc(2,b,6,0,"div",2),e.TgZ(3,"div",3)(4,"div",4)(5,"div",5)(6,"div",6)(7,"h5",7),e._uU(8,"Aktu\xe1ln\xed serverov\xfd certifik\xe1t"),e.qZA()(),e.TgZ(9,"div",8),e.YNc(10,h,4,0,"div",9),e.YNc(11,T,5,0,"div",10),e.YNc(12,C,57,20,"div",11),e.qZA(),e.TgZ(13,"div",12)(14,"button",13),e.NdJ("click",function(){return a.loadCertificateInfo()}),e._UZ(15,"i",14),e._uU(16," Obnovit "),e.qZA()()()()(),e.YNc(17,k,63,9,"div",2),e.YNc(18,I,58,16,"div",2),e.qZA()),2&n&&(e.xp6(1),e.Q6J("ngIf",!a.isAdmin),e.xp6(1),e.Q6J("ngIf",a.isAdmin),e.xp6(8),e.Q6J("ngIf",a.loading),e.xp6(1),e.Q6J("ngIf",!a.loading&&!a.certificateInfo),e.xp6(1),e.Q6J("ngIf",!a.loading&&a.certificateInfo),e.xp6(2),e.Q6J("disabled",a.loading),e.xp6(3),e.Q6J("ngIf",!a.generatedCertificate),e.xp6(1),e.Q6J("ngIf",a.generatedCertificate))},dependencies:[c.mk,c.O5,u._Y,u.Fj,u.wV,u.Wl,u._,u.JJ,u.JL,u.qQ,u.Fd,u.sg,u.u,c.uU,Z.H],styles:[".card[_ngcontent-%COMP%]{border-radius:8px;box-shadow:0 2px 4px #0000001a}.card-header[_ngcontent-%COMP%]{border-top-left-radius:8px;border-top-right-radius:8px}.card-footer[_ngcontent-%COMP%]{border-bottom-left-radius:8px;border-bottom-right-radius:8px}.text-success[_ngcontent-%COMP%]{color:#28a745!important}.text-warning[_ngcontent-%COMP%]{color:#ffc107!important}.text-danger[_ngcontent-%COMP%]{color:#dc3545!important}.badge[_ngcontent-%COMP%]{font-size:.9em;padding:.4em .6em}.form-text[_ngcontent-%COMP%]{font-size:.85em;color:#6c757d}.alert[_ngcontent-%COMP%]{border-radius:6px}.alert-warning[_ngcontent-%COMP%]{background-color:#fff3cd;border-color:#ffecb5;color:#856404}.alert-success[_ngcontent-%COMP%]{background-color:#d4edda;border-color:#c3e6cb;color:#155724}@media (prefers-color-scheme: dark){.card[_ngcontent-%COMP%]{background-color:#2c3034;border-color:#373b3e}.card-header[_ngcontent-%COMP%], .card-footer[_ngcontent-%COMP%]{background-color:#212529;border-color:#373b3e}.form-control[_ngcontent-%COMP%]{background-color:#2c3034;border-color:#495057;color:#e9ecef}.form-text[_ngcontent-%COMP%]{color:#adb5bd}.alert-warning[_ngcontent-%COMP%]{background-color:#332701;border-color:#664d03;color:#ffda6a}.alert-success[_ngcontent-%COMP%]{background-color:#051b11;border-color:#0f5132;color:#75b798}}"]}),i})();var P=s(4467);function N(i,o){1&i&&(e.TgZ(0,"div",17)(1,"div",18)(2,"span",19),e._uU(3,"Na\u010d\xedt\xe1n\xed..."),e.qZA()()())}function F(i,o){if(1&i&&(e.TgZ(0,"div",20),e._uU(1),e.qZA()),2&i){const t=e.oxw();e.xp6(1),e.hij(" ",t.errorMessage," ")}}function w(i,o){if(1&i&&(e.TgZ(0,"div")(1,"p")(2,"strong"),e._uU(3,"Detaily:"),e.qZA()(),e.TgZ(4,"pre",25),e._uU(5),e.qZA()()),2&i){const t=e.oxw(3);e.xp6(5),e.Oqu(t.status.lastError.additionalInfo)}}function O(i,o){if(1&i&&(e.TgZ(0,"div",24)(1,"h5"),e._uU(2,"Posledn\xed chyba:"),e.qZA(),e.TgZ(3,"div",20)(4,"p")(5,"strong"),e._uU(6,"\u010cas:"),e.qZA(),e._uU(7),e.qZA(),e.TgZ(8,"p")(9,"strong"),e._uU(10,"Popis:"),e.qZA(),e._uU(11),e.qZA(),e.YNc(12,w,6,1,"div",14),e.qZA()()),2&i){const t=e.oxw(2);e.xp6(7),e.hij(" ",t.formatDate(t.status.lastError.timestamp),""),e.xp6(4),e.hij(" ",t.status.lastError.description,""),e.xp6(1),e.Q6J("ngIf",t.status.lastError.additionalInfo)}}function j(i,o){if(1&i&&(e.TgZ(0,"div",24)(1,"h5"),e._uU(2,"Posledn\xed p\u0159\xedstup:"),e.qZA(),e.TgZ(3,"div",6)(4,"div",11)(5,"p")(6,"strong"),e._uU(7,"\u010cas:"),e.qZA(),e._uU(8),e.qZA(),e.TgZ(9,"p")(10,"strong"),e._uU(11,"Endpoint:"),e.qZA(),e._uU(12),e.qZA(),e.TgZ(13,"p")(14,"strong"),e._uU(15,"Status:"),e.qZA(),e._uU(16),e.qZA(),e.TgZ(17,"p")(18,"strong"),e._uU(19,"IP adresa:"),e.qZA(),e._uU(20),e.qZA(),e.TgZ(21,"p")(22,"strong"),e._uU(23,"Doba odezvy:"),e.qZA(),e._uU(24),e.qZA(),e.TgZ(25,"p")(26,"strong"),e._uU(27,"User Agent:"),e.qZA(),e._uU(28),e.qZA()()()()),2&i){const t=e.oxw(2);e.xp6(8),e.hij(" ",t.formatDate(t.status.lastAccess.timestamp),""),e.xp6(4),e.AsE(" ",t.status.lastAccess.method," ",t.status.lastAccess.endpoint,""),e.xp6(4),e.hij(" ",t.status.lastAccess.statusCode,""),e.xp6(4),e.hij(" ",t.status.lastAccess.ipAddress,""),e.xp6(4),e.hij(" ",t.status.lastAccess.responseTimeMs," ms"),e.xp6(4),e.hij(" ",t.status.lastAccess.additionalInfo,"")}}function M(i,o){if(1&i&&(e.TgZ(0,"div")(1,"div",21),e._UZ(2,"div",22),e.TgZ(3,"div")(4,"h4",8),e._uU(5),e.qZA()()(),e.YNc(6,O,13,3,"div",23),e.YNc(7,j,29,7,"div",23),e.qZA()),2&i){const t=e.oxw();e.xp6(2),e.Q6J("ngClass",t.getStatusClass()),e.xp6(3),e.Oqu(t.getStatusText()),e.xp6(1),e.Q6J("ngIf",!t.status.isRunning&&t.status.lastError),e.xp6(1),e.Q6J("ngIf",t.status.lastAccess)}}let J=(()=>{const o=class{constructor(r,n){this.systemStatusService=r,this.titleService=n,this.status=null,this.loading=!1,this.error=!1,this.errorMessage=""}ngOnInit(){this.titleService.setTitle("Stav DIS API"),this.loadStatus()}loadStatus(){this.loading=!0,this.systemStatusService.getDISApiStatus().subscribe({next:r=>{this.status=r,this.loading=!1,this.error=!1},error:r=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed stavu DISApi",r),this.loading=!1,this.error=!0,this.errorMessage="Nepoda\u0159ilo se na\u010d\xedst stav DISApi"}})}getStatusClass(){return this.status?this.status.isRunning?"bg-success":"bg-danger":"bg-secondary"}getStatusText(){return this.status?this.status.isRunning?"B\u011b\u017e\xed":"Neb\u011b\u017e\xed":"Nezn\xe1m\xfd"}formatDate(r){return new Date(r).toLocaleString()}};let i=o;return o.\u0275fac=function(n){return new(n||o)(e.Y36(P.R),e.Y36(g.y))},o.\u0275cmp=e.Xpm({type:o,selectors:[["app-disapi-status-page"]],decls:46,vars:4,consts:[[1,"container"],[1,"row","mb-4"],[1,"col-12"],[1,"text-muted"],[1,"row"],[1,"col-md-8"],[1,"card"],[1,"card-header","d-flex","justify-content-between","align-items-center"],[1,"mb-0"],[1,"btn","btn-sm","btn-outline-secondary",3,"disabled","click"],[1,"bi","bi-arrow-clockwise"],[1,"card-body"],["class","text-center",4,"ngIf"],["class","alert alert-danger",4,"ngIf"],[4,"ngIf"],[1,"col-md-4"],[1,"card-header"],[1,"text-center"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"alert","alert-danger"],[1,"d-flex","align-items-center","mb-4"],[1,"status-indicator","me-3",3,"ngClass"],["class","mt-4",4,"ngIf"],[1,"mt-4"],[1,"error-details"]],template:function(n,a){1&n&&(e.TgZ(0,"div",0)(1,"div",1)(2,"div",2)(3,"h2"),e._uU(4,"Stav DIS API"),e.qZA(),e.TgZ(5,"p",3),e._uU(6," Tato str\xe1nka zobrazuje aktu\xe1ln\xed stav DIS API a informace o posledn\xedch chyb\xe1ch a p\u0159\xedstupech. "),e.qZA()()(),e.TgZ(7,"div",4)(8,"div",5)(9,"div",6)(10,"div",7)(11,"h5",8),e._uU(12,"Stav DIS API"),e.qZA(),e.TgZ(13,"button",9),e.NdJ("click",function(){return a.loadStatus()}),e._UZ(14,"i",10),e._uU(15," Obnovit "),e.qZA()(),e.TgZ(16,"div",11),e.YNc(17,N,4,0,"div",12),e.YNc(18,F,2,1,"div",13),e.YNc(19,M,8,4,"div",14),e.qZA()()(),e.TgZ(20,"div",15)(21,"div",6)(22,"div",16)(23,"h5",8),e._uU(24,"Informace"),e.qZA()(),e.TgZ(25,"div",11)(26,"p"),e._uU(27,"DIS API je rozhran\xed pro komunikaci s instancemi DIS. Poskytuje n\xe1sleduj\xedc\xed funkce:"),e.qZA(),e.TgZ(28,"ul")(29,"li"),e._uU(30,"Autentizace pomoc\xed klientsk\xfdch certifik\xe1t\u016f"),e.qZA(),e.TgZ(31,"li"),e._uU(32,"Zabezpe\u010den\xfd p\u0159enos dat"),e.qZA(),e.TgZ(33,"li"),e._uU(34,"Validace po\u017eadavk\u016f"),e.qZA(),e.TgZ(35,"li"),e._uU(36,"Logov\xe1n\xed p\u0159\xedstup\u016f"),e.qZA()(),e.TgZ(37,"p"),e._uU(38,"V p\u0159\xedpad\u011b probl\xe9m\u016f s DIS API zkontrolujte:"),e.qZA(),e.TgZ(39,"ul")(40,"li"),e._uU(41,"Platnost serverov\xe9ho certifik\xe1tu"),e.qZA(),e.TgZ(42,"li"),e._uU(43,"Konfiguraci v souboru appsettings.json"),e.qZA(),e.TgZ(44,"li"),e._uU(45,"Logy aplikace"),e.qZA()()()()()()()),2&n&&(e.xp6(13),e.Q6J("disabled",a.loading),e.xp6(4),e.Q6J("ngIf",a.loading),e.xp6(1),e.Q6J("ngIf",a.error),e.xp6(1),e.Q6J("ngIf",!a.loading&&!a.error&&a.status))},dependencies:[c.mk,c.O5],styles:[".status-indicator[_ngcontent-%COMP%]{width:20px;height:20px;border-radius:50%}.bg-success[_ngcontent-%COMP%]{background-color:#28a745}.bg-danger[_ngcontent-%COMP%]{background-color:#dc3545}.bg-secondary[_ngcontent-%COMP%]{background-color:#6c757d}.error-details[_ngcontent-%COMP%]{font-size:.9rem;max-height:300px;overflow-y:auto;background-color:#0000000d;padding:15px;border-radius:4px}"]}),i})();var z=s(4466);let Q=(()=>{const o=class{};let i=o;return o.\u0275fac=function(n){return new(n||o)},o.\u0275mod=e.oAB({type:o}),o.\u0275inj=e.cJS({imports:[c.ez,u.UX,z.m,p.Bz.forChild([{path:"server-certificate",component:S},{path:"disapi-status",component:J}])]}),i})()}}]);