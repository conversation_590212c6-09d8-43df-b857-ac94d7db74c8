import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { CustomerService } from '../../services/customer.service';
import { ContactService } from '../../services/contact.service';
import { InstanceService } from '../../services/instance.service';
import { InstanceVersionService } from '../../services/instance-version.service';
import { VersionService } from '../../services/version.service';
import { UserService } from '../../services/user.service';
import { CertificateService } from '../../services/certificate.service';
import { BreadcrumbService } from '../../services/breadcrumb.service';
import { ModalService } from '../../services/modal.service';
import { AuthService } from '../../services/auth.service';
import { ClipboardService } from '../../services/clipboard.service';
import { Customer, CustomerDetail, CustomerContact } from '../../models/customer.model';
import { Contact, CreateContactRequest, UpdateContactRequest } from '../../models/contact.model';
import { DISInstance, InstanceVersion, InstanceStatus } from '../../models/instance.model';
import { DISVersion } from '../../models/version.model';
import { User } from '../../models/user.model';
import { CertificateGenerationResponse } from '../../models/certificate.model';
import { first } from 'rxjs/operators';

@Component({
  selector: 'app-customer-detail',
  templateUrl: './customer-detail.component.html',
  styleUrls: ['./customer-detail.component.css']
})
export class CustomerDetailComponent implements OnInit {
  // Zpřístupnění enumu InstanceStatus pro šablonu
  InstanceStatus = InstanceStatus;
  customerId: number = 0;
  customer: CustomerDetail | null = null;
  contacts: Contact[] = [];
  instances: DISInstance[] = [];
  instanceVersions: { [key: number]: InstanceVersion[] } = {};
  displayedInstanceVersions: { [key: number]: InstanceVersion[] } = {};
  showAllVersions: { [key: number]: boolean } = {};
  certificateInfo: any = {};
  pendingInstanceId: number | null = null; // ID instance, kterou chceme otevřít po načtení dat
  users: User[] = [];
  versions: DISVersion[] = [];

  // Režim komponenty (vytvoření, editace, zobrazení)
  mode: 'create' | 'edit' | 'view' = 'view';

  customerForm: FormGroup;
  contactForm: FormGroup;
  instanceForm: FormGroup;
  instanceVersionForm: FormGroup;

  loading: boolean = false;
  loadingContacts: boolean = false;
  loadingInstances: boolean = false;
  loadingVersions: boolean = false;
  loadingUsers: boolean = false;

  saving: boolean = false;
  savingContact: boolean = false;
  savingInstance: boolean = false;
  savingInstanceVersion: boolean = false;

  isEditMode: boolean = false;
  isEditContactMode: boolean = false;
  isEditInstanceMode: boolean = false;
  isEditInstanceVersionMode: boolean = false;

  error: string | null = null;
  contactError: string | null = null;
  instanceError: string | null = null;
  instanceVersionError: string | null = null;

  // Callback pro akce po načtení detailu
  loadCustomerDetailCallback: (() => void) | null = null;

  // Příznak, zda je přihlášený uživatel administrátor
  isAdmin: boolean = false;

  selectedContact: Contact | null = null;
  selectedInstance: DISInstance | null = null;
  selectedInstanceForVersion: DISInstance | null = null;

  // Vygenerovaný certifikát
  generatedCertificate: CertificateGenerationResponse | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private customerService: CustomerService,
    private contactService: ContactService,
    private instanceService: InstanceService,
    private instanceVersionService: InstanceVersionService,
    private versionService: VersionService,
    private userService: UserService,
    private certificateService: CertificateService,
    private breadcrumbService: BreadcrumbService,
    private modalService: ModalService,
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private clipboardService: ClipboardService
  ) {
    // Inicializace formulářů
    this.customerForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.maxLength(200)]],
      abbreviation: ['', [Validators.required, Validators.maxLength(50)]],
      companyId: ['', Validators.maxLength(20)],
      taxId: ['', Validators.maxLength(20)],
      street: ['', [Validators.required, Validators.maxLength(255)]],
      city: ['', [Validators.required, Validators.maxLength(255)]],
      postalCode: ['', [Validators.required, Validators.maxLength(20)]],
      country: ['Česká republika', [Validators.required, Validators.maxLength(100)]],
      email: ['', [Validators.email, Validators.maxLength(255)]],
      phone: ['', Validators.maxLength(50)],
      website: ['', Validators.maxLength(255)],
      notes: ['', Validators.maxLength(500)]
    });

    this.contactForm = this.formBuilder.group({
      firstName: ['', [Validators.required, Validators.maxLength(100)]],
      lastName: ['', [Validators.required, Validators.maxLength(100)]],
      position: ['', Validators.maxLength(100)],
      email: ['', [Validators.email, Validators.maxLength(255)]],
      phone: ['', Validators.maxLength(50)],
      notes: ['', Validators.maxLength(100)],
      isPrimary: [false]
    });

    this.instanceForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.maxLength(200)]],
      serverUrl: ['', [Validators.required, Validators.maxLength(255)]],
      expirationDate: [''],
      notes: ['', Validators.maxLength(500)],
      status: ['Active'],
      blockReason: ['', Validators.maxLength(500)],
      moduleReporting: [true],
      moduleAdvancedSecurity: [false],
      moduleApiIntegration: [false],
      moduleDataExport: [false],
      moduleCustomization: [false]
    });

    this.instanceVersionForm = this.formBuilder.group({
      versionId: ['', Validators.required],
      installedByUserId: ['', Validators.required],
      notes: ['', Validators.maxLength(500)]
    });
  }

  ngOnInit(): void {
    // Kontrola, zda je uživatel administrátor
    this.isAdmin = this.authService.isAdmin();

    // Načtení verzí a uživatelů pro formuláře
    this.loadVersions();
    this.loadUsers();

    // Zjištění, zda jsme na cestě /customers/add
    if (this.router.url === '/customers/add') {
      // Jsme v režimu vytváření nového zákazníka
      this.mode = 'create';
      this.isEditMode = true;

      // Nastavení breadcrumbs pro vytvoření
      this.breadcrumbService.setBreadcrumbs([
        { label: 'Zákazníci', url: '/customers', icon: 'building-fill' },
        { label: 'Přidat zákazníka', url: '/customers/add', icon: 'plus-circle-fill' }
      ]);

      // Reset formuláře
      this.customerForm.reset({
        country: 'Česká republika'
      });
    } else {
      // Získání ID zákazníka z URL pro režim zobrazení nebo editace
      this.route.params.subscribe(params => {
        if (params['id']) {
          this.customerId = +params['id'];
          this.loadCustomerDetail(this.customerId);

          // Kontrola, zda máme otevřít detail v režimu editace
          this.route.queryParams.subscribe(queryParams => {
            console.log('Query params:', queryParams);
            if (queryParams['edit'] === 'true') {
              console.log('Edit mode detected from query params');
              this.mode = 'edit';
              // Po načtení detailu přepneme do režimu editace
              this.loadCustomerDetailCallback = () => {
                console.log('loadCustomerDetailCallback executing, customer:', this.customer);
                if (this.customer) {
                  console.log('Calling editCustomer() from callback');
                  this.editCustomer();
                } else {
                  console.log('Customer data not available in callback');
                }
              };
            } else {
              console.log('View mode set');
              this.mode = 'view';
            }
          });

          // Kontrola, zda máme otevřít detail instance (z localStorage)
          try {
            console.log('Kontroluji localStorage pro otevření detailu instance');
            const openInstanceDetailJson = localStorage.getItem('openInstanceDetail');
            console.log('Data z localStorage:', openInstanceDetailJson);

            if (openInstanceDetailJson) {
              const openInstanceDetail = JSON.parse(openInstanceDetailJson);
              console.log('Parsovaná data:', openInstanceDetail);

              // Kontrola, zda data v localStorage odpovídají aktuálnímu zákazníkovi
              // a zda nejsou starší než 10 sekund
              const currentTime = new Date().getTime();
              const dataTime = openInstanceDetail.timestamp || 0;
              const isRecent = (currentTime - dataTime) < 10000; // 10 sekund

              console.log('Aktuální customerId:', this.customerId);
              console.log('CustomerId z localStorage:', openInstanceDetail.customerId);
              console.log('Je časově aktuální:', isRecent, '(rozdíl:', currentTime - dataTime, 'ms)');

              if (openInstanceDetail.customerId === this.customerId && isRecent) {
                const instanceId = openInstanceDetail.instanceId;
                console.log('Nastavuji otevření instance ID:', instanceId);

                // Uložíme ID instance, kterou chceme otevřít
                this.pendingInstanceId = instanceId;

                // Odstraníme data z localStorage, aby se modální okno neotevíralo opakovaně
                localStorage.removeItem('openInstanceDetail');
                console.log('Data z localStorage byla odstraněna');
              } else {
                console.log('Data v localStorage neodpovídají aktuálnímu zákazníkovi nebo nejsou aktuální');
              }
            } else {
              console.log('Žádná data pro otevření instance v localStorage');
            }
          } catch (error) {
            console.error('Chyba při zpracování dat z localStorage:', error);
          }
        }
      });
    }
  }

  /**
   * Načtení detailu zákazníka
   */
  loadCustomerDetail(customerId: number): void {
    console.log('loadCustomerDetail() called with ID:', customerId);
    this.loading = true;
    this.loadingContacts = true;
    this.loadingInstances = true;

    this.customerService.getCustomer(customerId)
      .pipe(first())
      .subscribe({
        next: (customer) => {
          console.log('Customer data loaded:', customer);
          this.customer = customer;

          // Nastavení breadcrumbs
          this.breadcrumbService.setBreadcrumbs([
            { label: 'Zákazníci', url: '/customers', icon: 'building-fill' },
            { label: customer.name, url: `/customers/${customerId}`, icon: 'info-circle-fill' }
          ]);

          this.loading = false;

          // Načtení kontaktů zákazníka
          this.loadContacts(customerId);

          // Načtení instancí DIS zákazníka
          this.loadInstances(customerId);

          // Volat callback po načtení detailu, pokud existuje
          if (this.loadCustomerDetailCallback) {
            console.log('Executing loadCustomerDetailCallback');
            this.loadCustomerDetailCallback();
            this.loadCustomerDetailCallback = null; // Použít jen jednou
          }
        },
        error: (err) => {
          this.error = `Chyba při načítání zákazníka: ${err.message}`;
          this.loading = false;
          this.loadingContacts = false;
          this.loadingInstances = false;
        }
      });
  }

  /**
   * Načtení kontaktů zákazníka
   */
  loadContacts(customerId: number): void {
    this.contactService.getContactsByCustomerId(customerId)
      .pipe(first())
      .subscribe({
        next: (contacts) => {
          this.contacts = contacts;
          this.loadingContacts = false;
        },
        error: (err) => {
          this.error = `Chyba při načítání kontaktů: ${err.message}`;
          this.loadingContacts = false;
        }
      });
  }

  /**
   * Načtení instancí DIS zákazníka
   */
  loadInstances(customerId: number): void {
    this.instanceService.getInstancesByCustomerId(customerId)
      .pipe(first())
      .subscribe({
        next: (instances) => {
          this.instances = instances;
          this.loadingInstances = false;

          // Načtení verzí pro každou instanci
          instances.forEach(instance => {
            this.loadInstanceVersions(instance.id);
          });

          // Pokud máme čekající ID instance, otevřeme její detail
          if (this.pendingInstanceId) {
            console.log('Máme čekající ID instance:', this.pendingInstanceId);

            // Najdeme instanci podle ID
            const instance = instances.find(i => i.id === this.pendingInstanceId);
            console.log('Nalezená instance:', instance);

            if (instance) {
              console.log('Otevírám modální okno s detailem instance:', instance.id);

              // Počkáme, až se stránka načte
              setTimeout(() => {
                // Nastavíme vybranou instanci
                this.selectedInstanceForVersion = instance;

                // Načtení informací o certifikátu
                this.loadCertificateInfo(instance.id);

                // Otevřít modal přímo pomocí Bootstrap API
                console.log('Otevírám modální okno přímo pomocí Bootstrap API');
                const modalElement = document.getElementById('instanceDetailModal');
                if (modalElement) {
                  console.log('Modal element nalezen, otevírám...');
                  const modal = new window.bootstrap.Modal(modalElement);
                  modal.show();
                } else {
                  console.error('Modal element s ID instanceDetailModal nebyl nalezen');
                }
              }, 500);
            } else {
              console.log('Instance nebyla nalezena v seznamu načtených instancí');
            }

            // Resetujeme čekající ID instance
            this.pendingInstanceId = null;
          }
        },
        error: (err) => {
          this.error = `Chyba při načítání instancí DIS: ${err.message}`;
          this.loadingInstances = false;
        }
      });
  }

  /**
   * Načtení verzí instance
   */
  loadInstanceVersions(instanceId: number): void {
    this.instanceVersionService.getInstanceVersions(instanceId)
      .pipe(first())
      .subscribe({
        next: (versions) => {
          // Uložení všech verzí
          this.instanceVersions[instanceId] = versions;

          // Výchozí zobrazení pouze posledních 5 verzí
          this.displayedInstanceVersions[instanceId] = versions.slice(0, 5);

          // Výchozí stav zobrazení všech verzí
          this.showAllVersions[instanceId] = false;
        },
        error: (err) => {
          console.error(`Chyba při načítání verzí instance ${instanceId}:`, err);
        }
      });
  }

  /**
   * Přepnutí zobrazení všech verzí instance
   */
  toggleAllVersions(instanceId: number): void {
    this.showAllVersions[instanceId] = !this.showAllVersions[instanceId];

    if (this.showAllVersions[instanceId]) {
      // Zobrazit všechny verze
      this.displayedInstanceVersions[instanceId] = [...this.instanceVersions[instanceId]];
    } else {
      // Zobrazit pouze posledních 5 verzí
      this.displayedInstanceVersions[instanceId] = this.instanceVersions[instanceId].slice(0, 5);
    }
  }

  /**
   * Načtení informací o certifikátu instance
   */
  loadCertificateInfo(instanceId: number): void {
    this.certificateService.getInstanceCertificateInfo(instanceId)
      .pipe(first())
      .subscribe({
        next: (info) => {
          this.certificateInfo[instanceId] = info;
        },
        error: (err) => {
          console.error(`Chyba při načítání informací o certifikátu instance ${instanceId}:`, err);
        }
      });
  }

  /**
   * Načtení verzí pro formuláře
   */
  loadVersions(): void {
    this.loadingVersions = true;

    this.versionService.getVersions()
      .pipe(first())
      .subscribe({
        next: (versions) => {
          this.versions = versions;
          this.loadingVersions = false;
        },
        error: (err) => {
          console.error('Chyba při načítání verzí:', err);
          this.loadingVersions = false;
        }
      });
  }

  /**
   * Načtení uživatelů pro formuláře
   */
  loadUsers(): void {
    this.loadingUsers = true;

    this.userService.getUsers()
      .pipe(first())
      .subscribe({
        next: (users) => {
          this.users = users;
          this.loadingUsers = false;
        },
        error: (err) => {
          console.error('Chyba při načítání uživatelů:', err);
          this.loadingUsers = false;
        }
      });
  }

  /**
   * Přepnutí do režimu editace zákazníka
   */
  editCustomer(): void {
    console.log('editCustomer() called');
    if (!this.customer) {
      console.log('No customer data available');
      return;
    }

    console.log('Switching to edit mode for customer:', this.customer);
    this.isEditMode = true;
    this.mode = 'edit'; // Explicitně nastavíme režim na 'edit'

    // Naplnění formuláře daty zákazníka
    console.log('Patching form with customer data');
    this.customerForm.patchValue({
      name: this.customer.name,
      abbreviation: this.customer.abbreviation,
      companyId: this.customer.companyId,
      taxId: this.customer.taxId,
      street: this.customer.street,
      city: this.customer.city,
      postalCode: this.customer.postalCode,
      country: this.customer.country,
      email: this.customer.email,
      phone: this.customer.phone,
      website: this.customer.website,
      notes: this.customer.notes
    });
    console.log('Form after patching:', this.customerForm.value);
  }

  /**
   * Uložení změn zákazníka nebo vytvoření nového
   */
  saveCustomer(): void {
    console.log('saveCustomer() called in customer-detail.component.ts');
    console.log('Form valid:', this.customerForm.valid);
    console.log('Form values:', this.customerForm.value);
    console.log('Mode:', this.mode);
    console.log('isEditMode:', this.isEditMode);
    console.log('customer:', this.customer);

    if (this.customerForm.invalid) {
      console.log('Form is invalid, returning');
      // Označit všechna pole jako touched, aby se zobrazily chyby
      Object.keys(this.customerForm.controls).forEach(key => {
        const control = this.customerForm.get(key);
        control?.markAsTouched();
        if (control?.invalid) {
          console.log(`Field ${key} is invalid:`, control.errors);
        }
      });
      return;
    }

    console.log('Form is valid, proceeding with save');
    this.saving = true;
    const formData = this.customerForm.value;

    // Vytvoření objektu zákazníka z formuláře
    const customerData = {
      name: formData.name,
      abbreviation: formData.abbreviation,
      companyId: formData.companyId,
      taxId: formData.taxId,
      street: formData.street,
      city: formData.city,
      postalCode: formData.postalCode,
      country: formData.country,
      email: formData.email,
      phone: formData.phone,
      website: formData.website,
      notes: formData.notes
    };

    if (this.mode === 'create') {
      // Vytvoření nového zákazníka
      this.customerService.createCustomer(customerData)
        .pipe(first())
        .subscribe({
          next: (createdCustomer) => {
            this.saving = false;
            // Přesměrování na detail nově vytvořeného zákazníka
            this.router.navigate(['/customers', createdCustomer.id]);
          },
          error: (err) => {
            this.error = `Chyba při vytváření zákazníka: ${err.message}`;
            this.saving = false;
          }
        });
    } else if (this.mode === 'edit' && this.customer) {
      // Aktualizace existujícího zákazníka
      console.log('Updating customer with ID:', this.customer.id);
      console.log('Customer data to send:', customerData);

      this.customerService.updateCustomer(this.customer.id, customerData)
        .pipe(first())
        .subscribe({
          next: (response) => {
            console.log('Customer updated successfully:', response);
            this.saving = false;
            this.isEditMode = false;
            this.loadCustomerDetail(this.customerId);
          },
          error: (err) => {
            console.error('Error updating customer:', err);
            this.error = `Chyba při ukládání zákazníka: ${err.message || err.statusText || 'Neznámá chyba'}`;
            this.saving = false;
          }
        });
    }
  }

  /**
   * Zrušení editace zákazníka
   */
  cancelEdit(): void {
    if (this.mode === 'create') {
      // Při vytváření nového zákazníka se vrátíme na seznam zákazníků
      this.router.navigate(['/customers']);
    } else {
      // Při editaci existujícího zákazníka se vrátíme do režimu zobrazení
      this.isEditMode = false;
    }
  }

  /**
   * Otevření modálu pro přidání kontaktu
   */
  openAddContactModal(): void {
    this.isEditContactMode = false;
    this.selectedContact = null;
    this.contactError = null;

    // Reset formuláře
    this.contactForm.reset({
      isPrimary: false
    });

    // Otevření modálu
    this.modalService.open('contactModal');
  }

  /**
   * Otevření modálu pro editaci kontaktu
   */
  editContact(contact: Contact): void {
    this.isEditContactMode = true;
    this.selectedContact = contact;
    this.contactError = null;

    // Naplnění formuláře daty kontaktu
    this.contactForm.patchValue({
      firstName: contact.firstName,
      lastName: contact.lastName,
      position: contact.position || '',
      email: contact.email || '',
      phone: contact.phone || '',
      notes: contact.notes || '',
      isPrimary: contact.isPrimary
    });

    // Otevření modálu
    this.modalService.open('contactModal');
  }

  /**
   * Uložení kontaktu
   */
  saveContact(): void {
    if (this.contactForm.invalid) {
      return;
    }

    this.savingContact = true;
    const formData = this.contactForm.value;

    if (this.isEditContactMode && this.selectedContact) {
      // Editace existujícího kontaktu
      const updatedContact: UpdateContactRequest = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        position: formData.position,
        email: formData.email,
        phone: formData.phone,
        notes: formData.notes,
        isPrimary: formData.isPrimary
      };

      this.contactService.updateContact(this.selectedContact.id, updatedContact)
        .pipe(first())
        .subscribe({
          next: () => {
            this.savingContact = false;
            this.closeContactModal();
            this.loadContacts(this.customerId);
          },
          error: (err) => {
            this.contactError = `Chyba při ukládání kontaktu: ${err.message}`;
            this.savingContact = false;
          }
        });
    } else {
      // Přidání nového kontaktu
      const newContact: CreateContactRequest = {
        customerId: this.customerId,
        firstName: formData.firstName,
        lastName: formData.lastName,
        position: formData.position,
        email: formData.email,
        phone: formData.phone,
        notes: formData.notes,
        isPrimary: formData.isPrimary
      };

      this.contactService.createContact(newContact)
        .pipe(first())
        .subscribe({
          next: () => {
            this.savingContact = false;
            this.closeContactModal();
            this.loadContacts(this.customerId);
          },
          error: (err) => {
            this.contactError = `Chyba při ukládání kontaktu: ${err.message}`;
            this.savingContact = false;
          }
        });
    }
  }

  /**
   * Zavření modálu pro kontakty
   */
  closeContactModal(): void {
    this.modalService.close('contactModal');
  }

  /**
   * Smazání kontaktu
   */
  async deleteContact(contact: Contact): Promise<void> {
    const confirmed = await this.modalService.confirm(
      `Opravdu chcete smazat kontakt ${contact.firstName} ${contact.lastName}?`,
      'Smazání kontaktu',
      'Smazat',
      'Zrušit',
      'btn-danger',
      'btn-secondary'
    );

    if (confirmed) {
      this.contactService.deleteContact(contact.id)
        .pipe(first())
        .subscribe({
          next: () => {
            this.loadContacts(this.customerId);
          },
          error: (err) => {
            this.error = `Chyba při mazání kontaktu: ${err.message}`;
            this.modalService.alert(
              `Chyba při mazání kontaktu: ${err.message}`,
              'Chyba',
              'Zavřít',
              'btn-danger'
            );
          }
        });
    }
  }

  /**
   * Otevření modálu pro přidání instance
   */
  openAddInstanceModal(): void {
    this.isEditInstanceMode = false;
    this.selectedInstance = null;
    this.instanceError = null;

    // Reset formuláře
    this.instanceForm.reset({
      status: InstanceStatus.Active, // Použití číselné hodnoty enumu
      moduleReporting: true,
      moduleAdvancedSecurity: false,
      moduleApiIntegration: false,
      moduleDataExport: false,
      moduleCustomization: false
    });

    // Otevření modálu
    this.modalService.open('instanceModal');
  }

  /**
   * Otevření modálu pro editaci instance
   */
  editInstance(instance: DISInstance): void {
    this.isEditInstanceMode = true;
    this.selectedInstance = instance;
    this.instanceError = null;

    // Naplnění formuláře daty instance
    this.instanceForm.patchValue({
      name: instance.name,
      serverUrl: instance.serverUrl,
      expirationDate: instance.expirationDate ? new Date(instance.expirationDate).toISOString().split('T')[0] : '',
      notes: instance.notes,
      // Převod řetězcového statusu na číselnou hodnotu enumu
      status: this.getStatusEnumValue(instance.status),
      moduleReporting: instance.moduleReporting,
      moduleAdvancedSecurity: instance.moduleAdvancedSecurity,
      moduleApiIntegration: instance.moduleApiIntegration,
      moduleDataExport: instance.moduleDataExport,
      moduleCustomization: instance.moduleCustomization
    });

    // Otevření modálu
    this.modalService.open('instanceModal');
  }

  /**
   * Zobrazení detailu instance
   */
  viewInstanceDetail(instance: DISInstance): void {
    this.selectedInstanceForVersion = instance;

    // Načtení verzí instance
    this.loadInstanceVersions(instance.id);

    // Načtení informací o certifikátu
    this.loadCertificateInfo(instance.id);

    // Otevřít modal
    this.modalService.open('instanceDetailModal');
  }

  /**
   * Zavření modálu pro detail instance
   */
  closeInstanceDetailModal(): void {
    this.modalService.close('instanceDetailModal');
  }

  /**
   * Editace instance z detailu instance
   */
  editInstanceFromDetail(instance: DISInstance): void {
    // Nejprve zavřeme modál s detailem instance
    this.closeInstanceDetailModal();

    // Poté otevřeme modál pro editaci instance
    setTimeout(() => {
      this.editInstance(instance);
    }, 500); // Počkáme 500ms, aby se první modál stihl zavřít
  }

  /**
   * Zavření modálu pro přidání/úpravu instance
   */
  closeInstanceModal(): void {
    this.modalService.close('instanceModal');
  }

  /**
   * Převod řetězcové hodnoty statusu na enum InstanceStatus
   */
  private convertStatusToEnum(status: string): InstanceStatus {
    // Pokud je status číselná hodnota jako řetězec (např. "3"), převedeme ji na číslo
    if (!isNaN(Number(status))) {
      const numericStatus = Number(status);
      return numericStatus;
    }

    // Jinak zpracujeme řetězcové hodnoty
    switch (status) {
      case 'Active': return InstanceStatus.Active;
      case 'Blocked': return InstanceStatus.Blocked;
      case 'Expired': return InstanceStatus.Expired;
      case 'Trial': return InstanceStatus.Trial;
      case 'Maintenance': return InstanceStatus.Maintenance;
      default: return InstanceStatus.Active; // Výchozí hodnota je Active
    }
  }

  /**
   * Převod řetězcového statusu na číselnou hodnotu enumu
   */
  private getStatusEnumValue(status: string | InstanceStatus): InstanceStatus {
    if (typeof status === 'number') {
      return status;
    }

    switch (status) {
      case 'Active': return InstanceStatus.Active;
      case 'Blocked': return InstanceStatus.Blocked;
      case 'Expired': return InstanceStatus.Expired;
      case 'Trial': return InstanceStatus.Trial;
      case 'Maintenance': return InstanceStatus.Maintenance;
      default: return InstanceStatus.Active; // Výchozí hodnota je Active
    }
  }

  /**
   * Uložení instance
   */
  saveInstance(): void {
    if (this.instanceForm.invalid) {
      return;
    }

    this.savingInstance = true;
    const formData = this.instanceForm.value;

    console.log('Hodnoty formuláře před úpravou:', formData);

    if (this.isEditInstanceMode && this.selectedInstance) {
      // Editace existující instance
      const updatedInstance = {
        name: formData.name,
        serverUrl: formData.serverUrl,
        expirationDate: formData.expirationDate ? new Date(formData.expirationDate) : undefined,
        notes: formData.notes,
        status: this.convertStatusToEnum(formData.status),
        moduleReporting: formData.moduleReporting,
        moduleAdvancedSecurity: formData.moduleAdvancedSecurity,
        moduleApiIntegration: formData.moduleApiIntegration,
        moduleDataExport: formData.moduleDataExport,
        moduleCustomization: formData.moduleCustomization
      };

      console.log('Odesílaná data instance:', updatedInstance);

      this.instanceService.updateInstance(this.selectedInstance.id, updatedInstance)
        .pipe(first())
        .subscribe({
          next: (response) => {
            console.log('Instance úspěšně aktualizována:', response);
            this.savingInstance = false;
            this.closeInstanceModal();
            this.loadInstances(this.customerId);
          },
          error: (err) => {
            console.error('Chyba při ukládání instance:', err);
            this.instanceError = `Chyba při ukládání instance: ${err.message || err.statusText || 'Neznámá chyba'}`;
            this.savingInstance = false;
          }
        });
    } else {
      // Přidání nové instance
      const newInstance = {
        customerId: this.customerId,
        name: formData.name,
        serverUrl: formData.serverUrl,
        expirationDate: formData.expirationDate ? new Date(formData.expirationDate) : undefined,
        notes: formData.notes,
        status: this.convertStatusToEnum(formData.status),
        moduleReporting: formData.moduleReporting,
        moduleAdvancedSecurity: formData.moduleAdvancedSecurity,
        moduleApiIntegration: formData.moduleApiIntegration,
        moduleDataExport: formData.moduleDataExport,
        moduleCustomization: formData.moduleCustomization
      };

      console.log('Odesílaná data nové instance:', newInstance);

      this.instanceService.createInstance(newInstance)
        .pipe(first())
        .subscribe({
          next: (response) => {
            console.log('Instance úspěšně vytvořena:', response);
            this.savingInstance = false;
            this.closeInstanceModal();
            this.loadInstances(this.customerId);
          },
          error: (err) => {
            console.error('Chyba při vytváření instance:', err);
            this.instanceError = `Chyba při vytváření instance: ${err.message || err.statusText || 'Neznámá chyba'}`;
            this.savingInstance = false;
          }
        });
    }
  }

  /**
   * Smazání zákazníka
   */
  async deleteCustomer(): Promise<void> {
    if (!this.customer) return;

    const confirmed = await this.modalService.confirm(
      `Opravdu chcete smazat zákazníka ${this.customer.name}?`,
      'Smazání zákazníka',
      'Smazat',
      'Zrušit',
      'btn-danger',
      'btn-secondary'
    );

    if (confirmed) {
      this.loading = true;

      this.customerService.deleteCustomer(this.customer.id)
        .pipe(first())
        .subscribe({
          next: () => {
            this.router.navigate(['/customers']);
          },
          error: (err) => {
            this.error = `Chyba při mazání zákazníka: ${err.message}`;
            this.loading = false;
            this.modalService.alert(
              `Chyba při mazání zákazníka: ${err.message}`,
              'Chyba',
              'Zavřít',
              'btn-danger'
            );
          }
        });
    }
  }

  /**
   * Návrat na seznam zákazníků
   */
  goBack(): void {
    this.router.navigate(['/customers']);
  }

  /**
   * Kopírování API klíče do schránky
   */
  async copyApiKey(inputElement: HTMLInputElement): Promise<void> {
    await this.clipboardService.copyFromInput(
      inputElement,
      'API klíč byl zkopírován do schránky',
      'Nepodařilo se zkopírovat API klíč'
    );
  }

  /**
   * Generování nového certifikátu pro instanci
   */
  async generateCertificate(instanceId: number): Promise<void> {
    const confirmed = await this.modalService.confirm(
      'Opravdu chcete vygenerovat nový certifikát pro tuto instanci? Pokud instance již má certifikát, bude revokovaný.',
      'Generování certifikátu',
      'Generovat',
      'Zrušit',
      'btn-success',
      'btn-secondary'
    );

    if (!confirmed) {
      return;
    }

    this.certificateService.generateCertificate(instanceId).subscribe({
      next: (response) => {
        // Uložení vygenerovaného certifikátu - vytvoříme kopii, aby se zajistilo, že se data nepřepíší
        // Zkontrolujeme, zda heslo existuje v odpovědi, jinak použijeme výchozí heslo
        const certificatePassword = response.password || 'password';

        this.generatedCertificate = {
          certificate: response.certificate,
          privateKey: response.privateKey,
          thumbprint: response.thumbprint,
          expirationDate: response.expirationDate,
          password: certificatePassword,
          certificatePassword: certificatePassword
        };

        // Aktualizace informací o certifikátu
        this.loadCertificateInfo(instanceId);

        // Explicitní nastavení hesla pro zobrazení v modálním okně
        const passwordElement = document.getElementById('certificatePassword');
        if (passwordElement && this.generatedCertificate) {
          passwordElement.textContent = this.generatedCertificate.password || 'Heslo není k dispozici';
        }

        // Zobrazení modálního okna s informacemi o certifikátu
        this.modalService.open('certificateGeneratedModal');
      },
      error: (err) => {
        console.error('Chyba při generování certifikátu', err);
        this.modalService.alert(
          `Chyba při generování certifikátu: ${err.error?.message || err.message || 'Neznámá chyba'}`,
          'Chyba',
          'Zavřít',
          'btn-danger'
        );
      }
    });
  }

  /**
   * Stažení vygenerovaného certifikátu
   */
  downloadCertificate(): void {
    if (!this.generatedCertificate) {
      return;
    }

    // Vytvoření a stažení souboru .pfx
    const blob = this.base64ToBlob(this.generatedCertificate.privateKey, 'application/x-pkcs12');
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `certificate_${this.selectedInstanceForVersion?.name || 'instance'}.pfx`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }

  /**
   * Pomocná metoda pro konverzi Base64 na Blob
   */
  private base64ToBlob(base64: string, contentType: string): Blob {
    const byteCharacters = atob(base64);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += 512) {
      const slice = byteCharacters.slice(offset, offset + 512);
      const byteNumbers = new Array(slice.length);

      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    return new Blob(byteArrays, { type: contentType });
  }

  /**
   * Zavření modálního okna s certifikátem
   */
  closeCertificateModal(): void {
    this.modalService.close('certificateGeneratedModal');
  }

  /**
   * Otevření modálního okna pro přidání verze instance
   */
  openAddInstanceVersionModal(): void {
    if (!this.selectedInstanceForVersion) {
      console.error('Není vybrána žádná instance');
      return;
    }

    this.isEditInstanceVersionMode = false;
    this.instanceVersionError = null;

    // Získání ID aktuálně přihlášeného uživatele
    const currentUserId = this.authService.getCurrentUserId();

    // Reset formuláře s předvyplněným aktuálním uživatelem
    this.instanceVersionForm.reset({
      versionId: '',
      installedByUserId: currentUserId, // Předvyplnění aktuálního uživatele
      notes: ''
    });

    // Otevření modálu
    this.modalService.open('instanceVersionModal');
  }

  /**
   * Zavření modálního okna pro přidání/úpravu verze instance
   */
  closeInstanceVersionModal(): void {
    this.modalService.close('instanceVersionModal');
  }

  /**
   * Uložení verze instance
   */
  saveInstanceVersion(): void {
    if (this.instanceVersionForm.invalid) {
      // Označit všechna pole jako touched, aby se zobrazily chyby
      Object.keys(this.instanceVersionForm.controls).forEach(key => {
        const control = this.instanceVersionForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    if (!this.selectedInstanceForVersion) {
      this.instanceVersionError = 'Není vybrána žádná instance.';
      return;
    }

    this.savingInstanceVersion = true;
    const formData = this.instanceVersionForm.value;

    // Vytvoření nové verze instance
    const newInstanceVersion = {
      versionId: formData.versionId,
      installedByUserId: formData.installedByUserId,
      notes: formData.notes || ''
    };

    this.instanceVersionService.addInstanceVersion(this.selectedInstanceForVersion.id, newInstanceVersion)
      .pipe(first())
      .subscribe({
        next: (response: InstanceVersion) => {
          console.log('Verze instance úspěšně vytvořena:', response);
          this.savingInstanceVersion = false;
          this.closeInstanceVersionModal();

          // Aktualizace seznamu verzí instance
          this.loadInstanceVersions(this.selectedInstanceForVersion!.id);
        },
        error: (err: any) => {
          console.error('Chyba při vytváření verze instance:', err);
          this.instanceVersionError = `Chyba při vytváření verze instance: ${err.message || err.statusText || 'Neznámá chyba'}`;
          this.savingInstanceVersion = false;
        }
      });
  }

  /**
   * Testovací metoda pro zobrazení hesla
   */
  testPassword(): void {
    if (this.generatedCertificate) {
      this.modalService.alert(
        `Heslo k certifikátu: <strong>${this.generatedCertificate.password}</strong>`,
        'Heslo k certifikátu',
        'Zavřít',
        'btn-primary'
      );
    } else {
      this.modalService.alert(
        'Certifikát není k dispozici',
        'Informace',
        'Zavřít',
        'btn-secondary'
      );
    }
  }

  /**
   * Pomocná metoda pro získání jména statusu instance
   */
  getInstanceStatusName(status: InstanceStatus | string): string {
    if (typeof status === 'string') {
      switch (status) {
        case 'Active': return 'Aktivní';
        case 'Blocked': return 'Blokovaná';
        case 'Expired': return 'Expirovaná';
        case 'Trial': return 'Zkušební';
        case 'Maintenance': return 'Údržba';
        default: return status;
      }
    } else {
      switch (status) {
        case InstanceStatus.Active: return 'Aktivní';
        case InstanceStatus.Blocked: return 'Blokovaná';
        case InstanceStatus.Expired: return 'Expirovaná';
        case InstanceStatus.Trial: return 'Zkušební';
        case InstanceStatus.Maintenance: return 'Údržba';
        default: return String(status);
      }
    }
  }

  /**
   * Pomocná metoda pro získání třídy pro status instance
   */
  getInstanceStatusClass(status: InstanceStatus | string): string {
    if (typeof status === 'string') {
      switch (status) {
        case 'Active': return 'bg-success';
        case 'Blocked': return 'bg-danger';
        case 'Expired': return 'bg-warning text-dark';
        case 'Trial': return 'bg-info text-dark';
        case 'Maintenance': return 'bg-secondary';
        default: return 'bg-secondary';
      }
    } else {
      switch (status) {
        case InstanceStatus.Active: return 'bg-success';
        case InstanceStatus.Blocked: return 'bg-danger';
        case InstanceStatus.Expired: return 'bg-warning text-dark';
        case InstanceStatus.Trial: return 'bg-info text-dark';
        case InstanceStatus.Maintenance: return 'bg-secondary';
        default: return 'bg-secondary';
      }
    }
  }
}
