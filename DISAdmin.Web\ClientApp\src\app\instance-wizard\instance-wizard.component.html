<div class="container mt-4">
  <div class="card">
    <div class="card-header bg-primary text-white">
      <h4 class="mb-0"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vytvořením instance DIS</h4>
    </div>
    <div class="card-body">
      <div *ngIf="error" class="alert alert-danger mb-4">
        {{ error }}
      </div>

      <!-- <PERSON><PERSON>y průvodce -->
      <div class="wizard-steps mb-4">
        <div class="step" [ngClass]="getStepClass(1)">
          <div class="step-number">1</div>
          <div class="step-title">Zákazník</div>
        </div>
        <div class="step" [ngClass]="getStepClass(2)">
          <div class="step-number">2</div>
          <div class="step-title">Instance</div>
        </div>
        <div class="step" [ngClass]="getStepClass(3)">
          <div class="step-number">3</div>
          <div class="step-title">Moduly</div>
        </div>
        <div class="step" [ngClass]="getStepClass(4)">
          <div class="step-number">4</div>
          <div class="step-title">Certifikát</div>
        </div>
      </div>

      <!-- Krok 1: Výběr zákazníka -->
      <div *ngIf="currentStep === 1">
        <h5 class="mb-3">Vyberte zákazníka</h5>
        <form [formGroup]="customerForm">
          <div class="mb-3">
            <label for="customerId" class="form-label required">Zákazník</label>
            <select id="customerId" formControlName="customerId" class="form-select" [ngClass]="{'is-invalid': customerForm.get('customerId')?.invalid && customerForm.get('customerId')?.touched}">
              <option value="">-- Vyberte zákazníka --</option>
              <option *ngFor="let customer of customers" [value]="customer.id">{{ customer.name }}</option>
            </select>
            <div *ngIf="customerForm.get('customerId')?.invalid && customerForm.get('customerId')?.touched" class="invalid-feedback">
              Vyberte zákazníka
            </div>
          </div>
        </form>
      </div>

      <!-- Krok 2: Informace o instanci -->
      <div *ngIf="currentStep === 2">
        <h5 class="mb-3">Informace o instanci</h5>
        <form [formGroup]="instanceForm">
          <div class="mb-3">
            <label for="name" class="form-label required">Název instance</label>
            <input type="text" id="name" formControlName="name" class="form-control" [ngClass]="{'is-invalid': instanceForm.get('name')?.invalid && instanceForm.get('name')?.touched}">
            <div *ngIf="instanceForm.get('name')?.invalid && instanceForm.get('name')?.touched" class="invalid-feedback">
              <span *ngIf="instanceForm.get('name')?.errors?.['required']">Zadejte název instance</span>
              <span *ngIf="instanceForm.get('name')?.errors?.['maxlength']">Název instance nesmí být delší než 200 znaků</span>
            </div>
          </div>
          <div class="mb-3">
            <label for="serverUrl" class="form-label required">URL serveru</label>
            <input type="text" id="serverUrl" formControlName="serverUrl" class="form-control" [ngClass]="{'is-invalid': instanceForm.get('serverUrl')?.invalid && instanceForm.get('serverUrl')?.touched}">
            <div *ngIf="instanceForm.get('serverUrl')?.invalid && instanceForm.get('serverUrl')?.touched" class="invalid-feedback">
              <span *ngIf="instanceForm.get('serverUrl')?.errors?.['required']">Zadejte URL serveru</span>
              <span *ngIf="instanceForm.get('serverUrl')?.errors?.['maxlength']">URL serveru nesmí být delší než 255 znaků</span>
            </div>
          </div>

          <div class="mb-3">
            <label for="expirationDate" class="form-label">Datum expirace</label>
            <input type="date" id="expirationDate" formControlName="expirationDate" class="form-control">
          </div>
          <div class="mb-3">
            <label for="status" class="form-label">Status</label>
            <select id="status" formControlName="status" class="form-select">
              <option [value]="InstanceStatus.Active">Aktivní</option>
              <option [value]="InstanceStatus.Trial">Trial</option>
              <option [value]="InstanceStatus.Maintenance">Údržba</option>
              <option [value]="InstanceStatus.Blocked">Blokováno</option>
              <option [value]="InstanceStatus.Expired">Expirováno</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="notes" class="form-label">Poznámky</label>
            <textarea id="notes" formControlName="notes" class="form-control" rows="3" [ngClass]="{'is-invalid': instanceForm.get('notes')?.invalid && instanceForm.get('notes')?.touched}"></textarea>
            <div *ngIf="instanceForm.get('notes')?.invalid && instanceForm.get('notes')?.touched" class="invalid-feedback">
              <span *ngIf="instanceForm.get('notes')?.errors?.['maxlength']">Poznámky nesmí být delší než 500 znaků</span>
            </div>
          </div>
        </form>
      </div>

      <!-- Krok 3: Moduly -->
      <div *ngIf="currentStep === 3">
        <h5 class="mb-3">Povolené moduly</h5>
        <form [formGroup]="modulesForm">
          <div class="mb-3 form-check">
            <input type="checkbox" id="moduleReporting" formControlName="moduleReporting" class="form-check-input">
            <label for="moduleReporting" class="form-check-label">Reporting</label>
          </div>
          <div class="mb-3 form-check">
            <input type="checkbox" id="moduleAdvancedSecurity" formControlName="moduleAdvancedSecurity" class="form-check-input">
            <label for="moduleAdvancedSecurity" class="form-check-label">Rozšířené zabezpečení</label>
          </div>
          <div class="mb-3 form-check">
            <input type="checkbox" id="moduleApiIntegration" formControlName="moduleApiIntegration" class="form-check-input">
            <label for="moduleApiIntegration" class="form-check-label">API integrace</label>
          </div>
          <div class="mb-3 form-check">
            <input type="checkbox" id="moduleDataExport" formControlName="moduleDataExport" class="form-check-input">
            <label for="moduleDataExport" class="form-check-label">Export dat</label>
          </div>
          <div class="mb-3 form-check">
            <input type="checkbox" id="moduleCustomization" formControlName="moduleCustomization" class="form-check-input">
            <label for="moduleCustomization" class="form-check-label">Přizpůsobení</label>
          </div>
        </form>
      </div>

      <!-- Krok 4: Certifikát -->
      <div *ngIf="currentStep === 4">
        <h5 class="mb-3">Certifikát</h5>
        <form [formGroup]="certificateForm">
          <div class="mb-3 form-check">
            <input type="checkbox" id="generateCertificate" formControlName="generateCertificate" class="form-check-input">
            <label for="generateCertificate" class="form-check-label">Vygenerovat certifikát pro instanci</label>
          </div>
          <div class="alert alert-info">
            <i class="bi bi-info-circle-fill me-2"></i>
            Certifikát bude vygenerován automaticky po vytvoření instance. Bude platný po dobu 1 roku.
          </div>
        </form>
      </div>

      <!-- Navigační tlačítka -->
      <div class="d-flex justify-content-between mt-4">
        <button class="btn btn-secondary" (click)="previousStep()" *ngIf="currentStep > 1" [disabled]="loading">
          <i class="bi bi-arrow-left me-2"></i>Zpět
        </button>
        <div>
          <button class="btn btn-primary me-2" (click)="nextStep()" *ngIf="currentStep < totalSteps" [disabled]="loading">
            Další<i class="bi bi-arrow-right ms-2"></i>
          </button>
          <button class="btn btn-success" (click)="createInstance()" *ngIf="currentStep === totalSteps" [disabled]="loading">
            <i class="bi bi-check-circle-fill me-2"></i>Vytvořit instanci
            <span *ngIf="loading" class="spinner-border spinner-border-sm ms-2" role="status" aria-hidden="true"></span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal s potvrzením úspěšného vytvoření -->
<div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-success text-white">
        <h5 class="modal-title" id="successModalLabel">Instance vytvořena</h5>
        <button type="button" class="btn-close btn-close-white" (click)="closeSuccessModal()" aria-label="Zavřít"></button>
      </div>
      <div class="modal-body">
        <div class="alert alert-success">
          <i class="bi bi-check-circle-fill me-2"></i>
          Instance byla úspěšně vytvořena.
        </div>

        <!-- Odkaz na certifikát -->
        <div *ngIf="generatedCertificate" class="mt-3">
          <div class="alert alert-info">
            <i class="bi bi-info-circle-fill me-2"></i>
            Pro instanci byl vygenerován certifikát. Můžete ho stáhnout kliknutím na tlačítko níže.
          </div>
          <button class="btn btn-primary" (click)="showCertificateModal()">
            <i class="bi bi-info-circle me-2"></i>Zobrazit certifikát
          </button>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeSuccessModal()">Zavřít</button>
        <button type="button" class="btn btn-primary" (click)="goToCustomers()">
          Přejít na seznam zákazníků
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Sdílená komponenta pro zobrazení vygenerovaného certifikátu -->
<app-certificate-modal
  [certificate]="generatedCertificate"
  [instanceName]="instanceForm.value.name || ''"
  modalId="certificateInfoModal"
  (close)="closeCertificateModal()"
  (download)="downloadCertificate()">
</app-certificate-modal>