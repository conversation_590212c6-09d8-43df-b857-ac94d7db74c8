import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { CustomerService } from '../services/customer.service';
import { InstanceService } from '../services/instance.service';
import { CertificateService } from '../services/certificate.service';
import { ModalService } from '../services/modal.service';
import { Customer } from '../models/customer.model';
import { InstanceStatus } from '../models/instance.model';

@Component({
  selector: 'app-instance-wizard',
  templateUrl: './instance-wizard.component.html',
  styleUrls: ['./instance-wizard.component.css']
})
export class InstanceWizardComponent implements OnInit {
  currentStep: number = 1;
  totalSteps: number = 4;

  customers: Customer[] = [];
  loading: boolean = false;
  error: string | null = null;

  // Export enum pro použití v template
  InstanceStatus = InstanceStatus;

  customerForm: FormGroup;
  instanceForm: FormGroup;
  modulesForm: FormGroup;
  certificateForm: FormGroup;

  createdInstanceId: number | null = null;
  generatedCertificate: any = null;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private customerService: CustomerService,
    private instanceService: InstanceService,
    private certificateService: CertificateService,
    private modalService: ModalService
  ) {
    this.customerForm = this.fb.group({
      customerId: ['', Validators.required]
    });

    this.instanceForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(200)]],
      serverUrl: ['', [Validators.required, Validators.maxLength(255)]],
      expirationDate: [''],
      status: [InstanceStatus.Active, Validators.required],
      notes: ['', Validators.maxLength(500)]
    });

    this.modulesForm = this.fb.group({
      moduleReporting: [true],
      moduleAdvancedSecurity: [false],
      moduleApiIntegration: [false],
      moduleDataExport: [false],
      moduleCustomization: [false]
    });

    this.certificateForm = this.fb.group({
      generateCertificate: [true]
    });
  }

  ngOnInit(): void {
    this.loadCustomers();
  }

  loadCustomers(): void {
    this.loading = true;
    this.customerService.getCustomers().subscribe({
      next: (customers) => {
        this.customers = customers;
        this.loading = false;
      },
      error: (err) => {
        console.error('Chyba při načítání zákazníků', err);
        this.error = 'Chyba při načítání zákazníků';
        this.loading = false;
      }
    });
  }

  nextStep(): void {
    if (this.currentStep < this.totalSteps) {
      // Validace aktuálního kroku
      if (this.currentStep === 1 && this.customerForm.invalid) {
        this.customerForm.markAllAsTouched();
        return;
      }

      if (this.currentStep === 2 && this.instanceForm.invalid) {
        this.instanceForm.markAllAsTouched();
        return;
      }

      this.currentStep++;
    }
  }

  previousStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  createInstance(): void {
    if (this.customerForm.invalid || this.instanceForm.invalid || this.modulesForm.invalid) {
      return;
    }

    this.loading = true;
    this.error = null;

    const customerId = this.customerForm.value.customerId;
    const instanceFormValue = this.instanceForm.value;

    // Převod prázdného stringu na null pro expirationDate a správný převod status
    const instanceData = {
      ...instanceFormValue,
      ...this.modulesForm.value,
      customerId,
      expirationDate: instanceFormValue.expirationDate && instanceFormValue.expirationDate.trim() !== ''
        ? new Date(instanceFormValue.expirationDate)
        : null,
      status: Number(instanceFormValue.status) // Zajistíme, že status je číslo
    };

    this.instanceService.createInstance(instanceData).subscribe({
      next: (response) => {
        this.createdInstanceId = response.id;

        if (this.certificateForm.value.generateCertificate && this.createdInstanceId) {
          this.generateCertificate(this.createdInstanceId);
        } else {
          this.loading = false;
          this.showSuccessModal();
        }
      },
      error: (err) => {
        console.error('Chyba při vytváření instance - detailní informace:', {
          error: err,
          status: err.status,
          statusText: err.statusText,
          message: err.error?.message || err.message,
          url: err.url,
          instanceData: instanceData,
          timestamp: new Date().toISOString()
        });

        // Zobrazení detailnější chybové zprávy uživateli
        let errorMessage = 'Chyba při vytváření instance';

        if (err.error?.message) {
          errorMessage = err.error.message;

          // Pokud jsou k dispozici validační chyby, zobrazíme je
          if (err.error.errors) {
            const validationErrors = Object.keys(err.error.errors)
              .map(key => err.error.errors[key].join(', '))
              .join('; ');
            errorMessage += ': ' + validationErrors;
          }
        } else if (err.status === 0) {
          errorMessage = 'Chyba připojení k serveru';
        } else if (err.status >= 500) {
          errorMessage = 'Chyba serveru při vytváření instance';
        } else if (err.status === 400) {
          errorMessage = 'Neplatná data pro vytvoření instance';
        } else if (err.status === 404) {
          errorMessage = 'Zákazník nebyl nalezen';
        }

        this.error = errorMessage;
        this.loading = false;
      }
    });
  }

  generateCertificate(instanceId: number): void {
    this.certificateService.generateCertificate(instanceId).subscribe({
      next: (response) => {
        // Uložení vygenerovaného certifikátu - vytvoříme kopii, aby se zajistilo, že se data nepřepíší
        // Zkontrolujeme, zda heslo existuje v odpovědi, jinak použijeme výchozí heslo
        const certificatePassword = response.password || 'password';

        this.generatedCertificate = {
          certificate: response.certificate,
          privateKey: response.privateKey,
          thumbprint: response.thumbprint,
          expirationDate: response.expirationDate,
          password: certificatePassword,
          certificatePassword: certificatePassword
        };

        // Explicitní nastavení hesla pro zobrazení v modálním okně
        setTimeout(() => {
          const passwordElement = document.getElementById('wizardCertificatePassword');
          if (passwordElement && this.generatedCertificate) {
            passwordElement.textContent = this.generatedCertificate.password || 'Heslo není k dispozici';
          }
        }, 100);

        this.loading = false;
        this.showSuccessModal();
      },
      error: (err) => {
        console.error('Chyba při generování certifikátu', err);
        this.error = 'Instance byla vytvořena, ale došlo k chybě při generování certifikátu';
        this.loading = false;
        this.showSuccessModal();
      }
    });
  }

  showSuccessModal(): void {
    this.modalService.open('successModal');
  }

  closeSuccessModal(): void {
    this.modalService.close('successModal');
  }

  closeCertificateModal(): void {
    this.modalService.close('certificateInfoModal');
  }

  showCertificateModal(): void {
    this.modalService.open('certificateInfoModal');
  }

  downloadCertificate(): void {
    if (!this.generatedCertificate) {
      return;
    }

    // Vytvoření a stažení souboru .pfx
    const blob = this.base64ToBlob(this.generatedCertificate.privateKey, 'application/x-pkcs12');
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `certificate-${this.createdInstanceId}.pfx`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }

  /**
   * Pomocná metoda pro konverzi Base64 na Blob
   */
  private base64ToBlob(base64: string, contentType: string): Blob {
    const byteCharacters = atob(base64);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += 512) {
      const slice = byteCharacters.slice(offset, offset + 512);
      const byteNumbers = new Array(slice.length);

      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    return new Blob(byteArrays, { type: contentType });
  }

  goToCustomers(): void {
    this.closeSuccessModal();
    this.router.navigate(['/customers']);
  }

  getStepClass(step: number): string {
    if (step === this.currentStep) {
      return 'active';
    } else if (step < this.currentStep) {
      return 'completed';
    } else {
      return '';
    }
  }
}
