<!-- Filtr -->
<app-log-filter
  logType="error"
  [showLogLevelFilter]="true"
  [showCategoryFilter]="true"
  (filterChange)="onFilterChange($event)">
</app-log-filter>

<!-- <PERSON><PERSON><PERSON> logů -->
<div class="table-responsive">
  <table class="table table-hover mb-0">
    <thead class="table-header-override dark-header">
      <tr class="dark-header-row">
            <th>Čas</th>
            <th>Úroveň</th>
            <th>Zpráva</th>
            <th>Kategorie</th>
            <th>Zdroj</th>
            <th>Cesta</th>
            <th>Metoda</th>
            <th>Kód</th>
            <th>Uživatel</th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="isLoading">
            <td colspan="10" class="text-center py-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Načítání...</span>
              </div>
            </td>
          </tr>
          <tr *ngIf="!isLoading && error">
            <td colspan="10" class="text-center text-danger py-4">
              {{ error }}
            </td>
          </tr>
          <tr *ngIf="!isLoading && !error && logs.length === 0">
            <td colspan="10" class="text-center py-4">
              Nebyly nalezeny žádné logy aplikace
            </td>
          </tr>
          <tr *ngFor="let log of logs; let i = index">
            <td (click)="showLogDetail(log)" class="cursor-pointer">{{ log.timestamp | date:'dd.MM.yyyy HH:mm:ss' }}</td>
            <td (click)="showLogDetail(log)" class="cursor-pointer">
              <span class="badge rounded-pill {{ getLogLevelClass(log.logLevel) }}">
                {{ getLocalizedLogLevel(log.logLevel) }}
              </span>
            </td>
            <td (click)="showLogDetail(log)" class="text-truncate cursor-pointer" style="max-width: 250px;">{{ log.message }}</td>
            <td (click)="showLogDetail(log)" class="text-truncate cursor-pointer" style="max-width: 150px;">{{ log.category || '-' }}</td>
            <td (click)="showLogDetail(log)" class="cursor-pointer">
              <span class="badge rounded-pill {{ getSourceClass(log.source) }}">
                {{ getLocalizedSource(log.source) }}
              </span>
            </td>
            <td (click)="showLogDetail(log)" class="text-truncate cursor-pointer" style="max-width: 150px;">{{ log.requestPath || '-' }}</td>
            <td (click)="showLogDetail(log)" class="cursor-pointer">{{ log.requestMethod || '-' }}</td>
            <td (click)="showLogDetail(log)" class="cursor-pointer">
              <span *ngIf="log.statusCode" class="badge rounded-pill {{ getStatusCodeClass(log.statusCode) }}">
                {{ log.statusCode }}
              </span>
              <span *ngIf="!log.statusCode">-</span>
            </td>
            <td (click)="showLogDetail(log)" class="cursor-pointer">{{ log.username || 'Systém' }}</td>
            <td>
              <button class="btn btn-sm btn-outline-info" (click)="showLogDetail(log)" title="Zobrazit detail">
                <i class="bi bi-info-circle"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

<!-- Modální okno s detailem logu -->
<div class="modal fade" id="errorLogDetailModal" tabindex="-1" aria-labelledby="errorLogDetailModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="errorLogDetailModalLabel">Detail logu chyby</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Zavřít" (click)="closeLogDetail()"></button>
      </div>
      <div class="modal-body" *ngIf="selectedLog">
        <div class="row">
          <div class="col-md-6 mb-3">
            <h6>Základní informace</h6>
            <table class="table table-sm">
              <tr>
                <th>ID:</th>
                <td>{{ selectedLog.id }}</td>
              </tr>
              <tr>
                <th>Čas:</th>
                <td>{{ selectedLog.timestamp | date:'dd.MM.yyyy HH:mm:ss' }}</td>
              </tr>
              <tr>
                <th>Uživatel:</th>
                <td>{{ selectedLog.username || 'Systém' }}</td>
              </tr>
              <tr>
                <th>IP adresa:</th>
                <td>{{ selectedLog.ipAddress }}</td>
              </tr>
              <tr>
                <th>Zdroj:</th>
                <td>
                  <span class="badge rounded-pill {{ getSourceClass(selectedLog.source) }}">
                    {{ getLocalizedSource(selectedLog.source) }}
                  </span>
                </td>
              </tr>
            </table>
          </div>
          <div class="col-md-6 mb-3">
            <h6>Detaily požadavku</h6>
            <table class="table table-sm">
              <tr>
                <th>Cesta:</th>
                <td>{{ selectedLog.requestPath || '-' }}</td>
              </tr>
              <tr>
                <th>Metoda:</th>
                <td>{{ selectedLog.requestMethod || '-' }}</td>
              </tr>
              <tr>
                <th>Stavový kód:</th>
                <td>
                  <span *ngIf="selectedLog.statusCode" class="badge rounded-pill {{ getStatusCodeClass(selectedLog.statusCode) }}">
                    {{ selectedLog.statusCode }}
                  </span>
                  <span *ngIf="!selectedLog.statusCode">-</span>
                </td>
              </tr>
            </table>
          </div>
        </div>

        <div class="row">
          <div class="col-12 mb-3">
            <h6>Zpráva chyby</h6>
            <div class="card">
              <div class="card-body">
                <p class="mb-0">{{ selectedLog.message }}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="row" *ngIf="selectedLog.stackTrace">
          <div class="col-12 mb-3">
            <h6>Stack Trace</h6>
            <div class="card">
              <div class="card-body">
                <pre class="mb-0 stack-trace">{{ selectedLog.stackTrace }}</pre>
              </div>
            </div>
          </div>
        </div>

        <div class="row" *ngIf="selectedLog.additionalInfo">
          <div class="col-12">
            <h6>Dodatečné informace</h6>
            <div class="card">
              <div class="card-body">
                <pre class="mb-0">{{ selectedLog.additionalInfo }}</pre>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" (click)="closeLogDetail()">Zavřít</button>
      </div>
    </div>
  </div>
</div>
