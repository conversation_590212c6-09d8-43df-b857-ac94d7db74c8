import { Component, OnInit, AfterViewInit } from '@angular/core';
import { ErrorLogResponse, LogFilterRequest, LogSourceHelper, LogSource, ApplicationLogLevelHelper } from '../../models/logs.model';
import { LogsService } from '../../services/logs.service';
import { ModalService } from '../../services/modal.service';

@Component({
  selector: 'app-error-logs',
  templateUrl: './error-logs.component.html',
  styleUrls: ['../logs-common.css']
})
export class ErrorLogsComponent implements OnInit, AfterViewInit {
  logs: ErrorLogResponse[] = [];
  currentFilter: LogFilterRequest = {
    maxResults: 100,
    // Pro logy chyb neomezujeme zdroj, chceme vidět chyby ze všech zdrojů
  };
  isLoading = false;
  error: string | null = null;
  selectedLog: ErrorLogResponse | null = null;

  constructor(
    private logsService: LogsService,
    private modalService: ModalService
  ) { }

  ngOnInit(): void {
    // Načtení aktuálního stavu filtru z localStorage
    this.loadCurrentFilterState();
  }

  ngAfterViewInit(): void {
    // Načtení logů až po inicializaci komponenty
    setTimeout(() => {
      this.loadLogs();
    }, 0);
  }

  /**
   * Načtení aktuálního stavu filtru z localStorage
   */
  private loadCurrentFilterState(): void {
    try {
      const filterKey = 'current_filter_error';
      const filterJson = localStorage.getItem(filterKey);

      if (filterJson) {
        const savedFilter = JSON.parse(filterJson);

        // Převod řetězcových hodnot na objekty Date
        if (savedFilter.fromDate) {
          savedFilter.fromDate = new Date(savedFilter.fromDate);
        }

        if (savedFilter.toDate) {
          savedFilter.toDate = new Date(savedFilter.toDate);
        }

        // Nastavení hodnot filtru
        this.currentFilter = { ...this.currentFilter, ...savedFilter };
        console.log('Načten filtr pro logy chyb:', this.currentFilter);
      }
    } catch (error) {
      console.error('Chyba při načítání stavu filtru pro logy chyb', error);
    }
  }

  /**
   * Načtení logů podle aktuálního filtru
   */
  loadLogs(): void {
    this.isLoading = true;
    this.error = null;

    this.logsService.getErrorLogs(this.currentFilter).subscribe({
      next: (logs) => {
        this.logs = logs;
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Chyba při načítání logů chyb', err);
        this.error = 'Nepodařilo se načíst logy chyb';
        this.isLoading = false;
      }
    });
  }

  /**
   * Aktualizace filtru a načtení logů
   */
  onFilterChange(filter: LogFilterRequest): void {
    this.currentFilter = filter;
    this.loadLogs();
  }

  /**
   * Zobrazení detailu logu
   */
  showLogDetail(log: ErrorLogResponse): void {
    this.selectedLog = log;
    // Otevření modálního okna
    this.modalService.open('errorLogDetailModal');
  }

  /**
   * Zavření detailu logu
   */
  closeLogDetail(): void {
    this.selectedLog = null;
    // Zavření modálního okna
    this.modalService.close('errorLogDetailModal');
  }

  /**
   * Získání lokalizovaného názvu zdroje logu
   */
  getLocalizedSource(source: string): string {
    switch (source) {
      case 'DISAdmin': return 'DISAdmin';
      case 'DISApi': return 'DIS API';
      default: return source;
    }
  }

  /**
   * Získání CSS třídy pro zdroj logu
   */
  getSourceClass(source: string): string {
    switch (source) {
      case 'DISAdmin': return 'bg-primary';
      case 'DISApi': return 'bg-info';
      default: return 'bg-secondary';
    }
  }

  /**
   * Získání CSS třídy pro stavový kód
   */
  getStatusCodeClass(statusCode: number): string {
    if (!statusCode) return 'bg-secondary';

    if (statusCode >= 500) {
      return 'bg-danger';
    } else if (statusCode >= 400) {
      return 'bg-warning';
    } else if (statusCode >= 300) {
      return 'bg-info';
    } else if (statusCode >= 200) {
      return 'bg-success';
    } else {
      return 'bg-secondary';
    }
  }

  /**
   * Získání lokalizovaného názvu úrovně logování
   */
  getLocalizedLogLevel(logLevel: string): string {
    return ApplicationLogLevelHelper.getLocalizedName(logLevel);
  }

  /**
   * Získání CSS třídy pro úroveň logování
   */
  getLogLevelClass(logLevel: string): string {
    return ApplicationLogLevelHelper.getLogLevelClass(logLevel);
  }
}
