<div class="card mb-4">
  <div class="card-header d-flex justify-content-between align-items-center">
    <h5 class="mb-0 filter-title" (click)="toggleFilterVisibility()">
      <i class="bi fs-5 me-2" [ngClass]="{'bi-funnel': !hasActiveFilters, 'bi-funnel-fill text-primary': hasActiveFilters}"></i>
      <span>Filtr</span>
      <span *ngIf="hasActiveFilters" class="badge bg-danger ms-2 badge-smaller">{{ activeFilterCount }}</span>
      <i class="bi ms-2" [ngClass]="{'bi-chevron-up': isFilterVisible, 'bi-chevron-down': !isFilterVisible}"></i>
    </h5>
    <div>
      <button class="btn btn-sm btn-outline-primary me-4" (click)="openSaveFilterModal()">
        <i class="bi bi-save me-1"></i>Uložit filtr
      </button>
      <button class="btn btn-sm btn-outline-secondary me-2" (click)="resetFilter()">
        <i class="bi bi-arrow-counterclockwise me-1"></i>Reset
      </button>
      <button class="btn btn-sm btn-outline-primary" (click)="refreshData()">
        <i class="bi bi-arrow-repeat me-1"></i>Načíst data
      </button>
    </div>
  </div>
  <div class="card-body" *ngIf="isFilterVisible">
    <form [formGroup]="filterForm">
      <div class="row">
        <!-- Uživatel -->
        <div class="col-md-6 col-lg-3 mb-3">
          <label for="userId" class="form-label">Uživatel</label>
          <select class="form-select" id="userId" formControlName="userId">
            <option [ngValue]="null">Všichni uživatelé</option>
            <option *ngFor="let user of users" [value]="user.id">{{ user.username }}</option>
          </select>
        </div>

        <!-- Zdroj logu -->
        <div class="col-md-6 col-lg-3 mb-3">
          <label for="source" class="form-label">Zdroj</label>
          <select class="form-select" id="source" formControlName="source">
            <option [ngValue]="null">Všechny zdroje</option>
            <option *ngFor="let source of logSources" [ngValue]="source.value">{{ source.label }}</option>
          </select>
        </div>

        <!-- Specifická pole pro auditní logy a API logy -->
        <ng-container *ngIf="logType === 'activity' || logType === 'api'">
          <!-- Typ aktivity -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="activityType" class="form-label">Typ aktivity</label>
            <select class="form-select" id="activityType" formControlName="activityType">
              <option [ngValue]="''">Všechny typy</option>
              <option *ngFor="let type of activityTypes" [value]="type.value">{{ type.label }}</option>
            </select>
          </div>

          <!-- Entita -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="entityName" class="form-label">Entita</label>
            <input type="text" class="form-control" id="entityName" formControlName="entityName" placeholder="Např. customers, versions...">
          </div>

          <!-- ID entity -->
          <div class="col-md-6 col-lg-3 mb-3">
            <label for="entityId" class="form-label">ID entity</label>
            <input type="number" class="form-control" id="entityId" formControlName="entityId">
          </div>
        </ng-container>

        <!-- Specifická pole pro logy aplikace (error) -->
        <ng-container *ngIf="logType === 'error'">
          <!-- Úroveň logování -->
          <div class="col-md-6 col-lg-3 mb-3" *ngIf="showLogLevelFilter">
            <label for="logLevel" class="form-label">Úroveň logování</label>
            <select class="form-select" id="logLevel" formControlName="logLevel">
              <option [ngValue]="null">Všechny úrovně</option>
              <option *ngFor="let level of logLevels" [value]="level.value">{{ level.label }}</option>
            </select>
          </div>

          <!-- Kategorie -->
          <div class="col-md-6 col-lg-3 mb-3" *ngIf="showCategoryFilter">
            <label for="category" class="form-label">Kategorie</label>
            <input type="text" class="form-control" id="category" formControlName="category" placeholder="Např. DISAdmin.Api.Controllers...">
          </div>
        </ng-container>

        <!-- Počet záznamů -->
        <div class="col-md-6 col-lg-3 mb-3">
          <label for="maxResults" class="form-label">Počet záznamů</label>
          <input type="number" class="form-control" id="maxResults" formControlName="maxResults" min="1" max="1000">
        </div>

        <!-- Časové období -->
        <div class="col-md-6 col-lg-3 mb-3">
          <label for="period" class="form-label">Časové období</label>
          <select class="form-select" id="period" formControlName="period">
            <option *ngFor="let option of periodOptions" [value]="option.value">{{ option.label }}</option>
          </select>
        </div>

        <!-- Datumová pole pro vlastní období -->
        <div class="col-md-6 col-lg-3 mb-3" *ngIf="filterForm.get('period')?.value === 'custom'">
          <label for="fromDate" class="form-label">Od</label>
          <input type="datetime-local" class="form-control" id="fromDate" formControlName="fromDate"
                 [ngClass]="{'is-invalid': filterForm.get('fromDate')?.invalid && filterForm.get('fromDate')?.touched}">
          <div class="invalid-feedback" *ngIf="filterForm.get('fromDate')?.errors?.['required']">
            Zadejte počáteční datum
          </div>
        </div>
        <div class="col-md-6 col-lg-3 mb-3" *ngIf="filterForm.get('period')?.value === 'custom'">
          <label for="toDate" class="form-label">Do</label>
          <input type="datetime-local" class="form-control" id="toDate" formControlName="toDate"
                 [ngClass]="{'is-invalid': filterForm.get('toDate')?.invalid && filterForm.get('toDate')?.touched}">
          <div class="invalid-feedback" *ngIf="filterForm.get('toDate')?.errors?.['required']">
            Zadejte koncové datum
          </div>
        </div>
      </div>
    </form>

    <!-- Uložené filtry -->
    <div class="mt-3" *ngIf="savedFilters.length > 0">
      <h6>Uložené filtry</h6>
      <div class="saved-filters">
        <div class="saved-filter" *ngFor="let filter of savedFilters" (click)="applySavedFilter(filter)">
          <span class="filter-name">{{ filter.name }}</span>
          <button class="btn btn-sm btn-link text-danger" (click)="deleteSavedFilter(filter, $event)">
            <i class="bi bi-trash"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modální okno pro uložení filtru -->
<div class="modal fade" id="saveFilterModal" tabindex="-1" aria-labelledby="saveFilterModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="saveFilterModalLabel">Uložit filtr</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Zavřít"></button>
      </div>
      <div class="modal-body">
        <form [formGroup]="saveFilterForm">
          <div class="mb-3">
            <label for="filterName" class="form-label">Název filtru</label>
            <input type="text" class="form-control" id="filterName" formControlName="name" required>
          </div>
          <div class="alert alert-danger" *ngIf="error">{{ error }}</div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Zrušit</button>
        <button type="button" class="btn btn-primary" [disabled]="saveFilterForm.invalid || isLoading" (click)="saveFilter()">
          <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
          Uložit
        </button>
      </div>
    </div>
  </div>
</div>
