import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { LogFilterRequest, SavedLogFilterRequest, SavedLogFilterResponse, ActivityTypeHelper, LogSourceHelper, LogSource, ApplicationLogLevelHelper } from '../../models/logs.model';
import { LogsService } from '../../services/logs.service';
import { UserService } from '../../services/user.service';
import { User } from '../../models/user.model';
import { ModalService } from '../../services/modal.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-log-filter',
  templateUrl: './log-filter.component.html',
  styleUrls: ['./log-filter.component.css']
})
export class LogFilterComponent implements OnInit {
  @Input() logType: string = 'activity'; // activity, error, api
  @Input() showLogLevelFilter: boolean = false; // Zobrazit filtr úrovně logování
  @Input() showCategoryFilter: boolean = false; // Zobrazit filtr kategorie
  @Output() filterChange = new EventEmitter<LogFilterRequest>();

  filterForm: FormGroup;
  saveFilterForm: FormGroup;
  savedFilters: SavedLogFilterResponse[] = [];
  users: User[] = [];
  activityTypes = ActivityTypeHelper.getActivityTypes();
  logSources = LogSourceHelper.getLogSources();
  logLevels = ApplicationLogLevelHelper.getLogLevels();
  isLoading = false;
  error: string | null = null;

  // Vlastnosti pro skrývání/zobrazování filtru
  isFilterVisible = false; // Filtr je ve výchozím stavu skrytý
  hasActiveFilters = false;
  activeFilterCount = 0;

  // Časová období pro dropdown
  periodOptions = [
    { value: 1, label: '1 den' },
    { value: 7, label: '7 dní' },
    { value: 30, label: '30 dní' },
    { value: 90, label: '90 dní' },
    { value: 'custom', label: 'Vlastní období' }
  ];

  constructor(
    private fb: FormBuilder,
    private logsService: LogsService,
    private userService: UserService,
    private modalService: ModalService,
    private toastr: ToastrService
  ) {
    this.filterForm = this.createFilterForm();
    this.saveFilterForm = this.fb.group({
      name: ['']
    });
  }

  ngOnInit(): void {
    this.loadUsers();
    this.loadSavedFilters();
    this.loadFilterVisibilityState();
    this.loadCurrentFilterState();

    // Automatické aplikování filtru při změně formuláře
    this.filterForm.valueChanges.subscribe(() => {
      this.updateActiveFiltersIndicator();
      this.applyFilter();
    });

    // Sledování změny období pro zobrazení/skrytí polí fromDate a toDate
    this.filterForm.get('period')?.valueChanges.subscribe(period => {
      this.updateDateFieldsValidation(period);
    });
  }

  /**
   * Aktualizace validace datumových polí podle vybraného období
   */
  private updateDateFieldsValidation(period: string | number): void {
    const fromDateControl = this.filterForm.get('fromDate');
    const toDateControl = this.filterForm.get('toDate');

    if (period === 'custom') {
      // Při vlastním období jsou pole povinná
      fromDateControl?.setValidators([Validators.required]);
      toDateControl?.setValidators([Validators.required]);
    } else {
      // Při přednastaveném období nejsou pole povinná
      fromDateControl?.clearValidators();
      toDateControl?.clearValidators();
    }

    fromDateControl?.updateValueAndValidity();
    toDateControl?.updateValueAndValidity();
  }

  /**
   * Načtení aktuálního stavu filtru z localStorage
   */
  private loadCurrentFilterState(): void {
    try {
      const filterKey = `current_filter_${this.logType}`;
      const filterJson = localStorage.getItem(filterKey);

      if (filterJson) {
        const savedFilter = JSON.parse(filterJson);

        // Detekce starého formátu filtru (bez pole period)
        if (savedFilter.fromDate && savedFilter.toDate && !savedFilter.period) {
          // Pokusíme se detekovat období podle rozdílu dat
          const fromDate = new Date(savedFilter.fromDate);
          const toDate = new Date(savedFilter.toDate);
          const diffDays = Math.round((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));

          // Pokud je rozdíl blízký některému z předdefinovaných období, použijeme ho
          if (diffDays <= 1) {
            savedFilter.period = 1;
          } else if (diffDays <= 7) {
            savedFilter.period = 7;
          } else if (diffDays <= 30) {
            savedFilter.period = 30;
          } else if (diffDays <= 90) {
            savedFilter.period = 90;
          } else {
            savedFilter.period = 'custom';
          }
        }

        // Převod objektů Date na řetězce pro input
        if (savedFilter.fromDate) {
          const fromDate = new Date(savedFilter.fromDate);
          savedFilter.fromDate = this.formatDateForInput(fromDate);
        }

        if (savedFilter.toDate) {
          const toDate = new Date(savedFilter.toDate);
          savedFilter.toDate = this.formatDateForInput(toDate);
        }

        // Nastavení hodnot formuláře
        this.filterForm.patchValue(savedFilter);

        // Aktualizace validace datumových polí
        this.updateDateFieldsValidation(savedFilter.period);

        // Aktualizace indikátoru aktivních filtrů
        this.updateActiveFiltersIndicator();
      }
    } catch (error) {
      console.error(`Chyba při načítání stavu filtru pro ${this.logType}`, error);
    }
  }

  /**
   * Přepne viditelnost filtru a uloží stav do localStorage
   */
  toggleFilterVisibility(): void {
    this.isFilterVisible = !this.isFilterVisible;

    // Uložení stavu viditelnosti do localStorage
    try {
      const visibilityKey = `filter_visibility_logs_${this.logType}`;
      localStorage.setItem(visibilityKey, this.isFilterVisible.toString());
    } catch (error) {
      console.error('Chyba při ukládání stavu viditelnosti filtru do localStorage', error);
    }
  }

  /**
   * Načte stav viditelnosti filtru z localStorage
   */
  private loadFilterVisibilityState(): void {
    try {
      const visibilityKey = `filter_visibility_logs_${this.logType}`;
      const savedVisibility = localStorage.getItem(visibilityKey);

      if (savedVisibility !== null) {
        this.isFilterVisible = savedVisibility === 'true';
      }
    } catch (error) {
      console.error('Chyba při načítání stavu viditelnosti filtru z localStorage', error);
    }
  }

  /**
   * Aktualizuje indikátor aktivních filtrů
   */
  private updateActiveFiltersIndicator(): void {
    const values = this.filterForm.value;

    // Získání výchozích hodnot pro časové filtry
    const today = new Date();
    const endOfDay = new Date(today);
    endOfDay.setHours(23, 59, 59, 999);

    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 7);
    sevenDaysAgo.setHours(0, 0, 0, 0);

    // Formátování dat pro porovnání
    const defaultFromDateStr = this.formatDateForInput(sevenDaysAgo);
    const defaultToDateStr = this.formatDateForInput(endOfDay);

    // Výchozí hodnota pro období
    const defaultPeriod = 7;

    // Odstranění prázdných hodnot a výchozích hodnot
    const activeFilters = Object.keys(values)
      .filter(key => {
        // Ignorujeme maxResults s hodnotou 100, což je výchozí hodnota
        if (key === 'maxResults' && values[key] === 100) {
          return false;
        }

        // Ignorujeme source s výchozí hodnotou podle typu logu
        if (key === 'source') {
          if (this.logType === 'activity' && values[key] === LogSource.DISAdmin) {
            return false;
          }
          if (this.logType === 'api' && values[key] === LogSource.DISApi) {
            return false;
          }
        }

        // Ignorujeme výchozí hodnotu pro období
        if (key === 'period' && values[key] === defaultPeriod) {
          return false;
        }

        // Při přednastaveném období ignorujeme fromDate a toDate
        if (values.period !== 'custom' && (key === 'fromDate' || key === 'toDate')) {
          return false;
        }

        // Při vlastním období kontrolujeme výchozí hodnoty
        if (values.period === 'custom') {
          if (key === 'fromDate' && values[key] === defaultFromDateStr) {
            return false;
          }
          if (key === 'toDate' && values[key] === defaultToDateStr) {
            return false;
          }
        }

        // Ignorujeme null, prázdné řetězce a undefined
        return values[key] !== null && values[key] !== '' && values[key] !== undefined;
      })
      .reduce((obj: any, key) => {
        obj[key] = values[key];
        return obj;
      }, {});

    this.activeFilterCount = Object.keys(activeFilters).length;
    this.hasActiveFilters = this.activeFilterCount > 0;

    // Výpis aktivních filtrů do konzole pro ladění
    if (this.hasActiveFilters) {
      console.log('Aktivní filtry:', activeFilters);
    }
  }

  /**
   * Vytvoření formuláře pro filtrování
   */
  private createFilterForm(): FormGroup {
    // Nastavení výchozích hodnot pro časové filtry
    const today = new Date();
    const endOfDay = new Date(today);
    endOfDay.setHours(23, 59, 59, 999);

    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 7);
    sevenDaysAgo.setHours(0, 0, 0, 0);

    // Formátování dat pro input typu datetime-local
    const fromDateStr = this.formatDateForInput(sevenDaysAgo);
    const toDateStr = this.formatDateForInput(endOfDay);

    // Základní pole pro všechny typy logů
    const formControls: any = {
      period: [7], // Výchozí hodnota je 7 dní
      fromDate: [fromDateStr],
      toDate: [toDateStr],
      userId: [null],
      maxResults: [100],
      source: [null]
    };

    // Nastavení výchozí hodnoty pro zdroj podle typu logu
    if (this.logType === 'activity') {
      formControls.source = [LogSource.DISAdmin];
    } else if (this.logType === 'api') {
      formControls.source = [LogSource.DISApi];
    }

    // Přidání specifických polí podle typu logu
    if (this.logType === 'activity' || this.logType === 'api') {
      formControls.entityName = [''];
      formControls.entityId = [null];
      formControls.activityType = [''];
    }

    // Přidání polí pro error logy
    if (this.logType === 'error') {
      formControls.logLevel = [null];
      formControls.category = [''];
    }

    return this.fb.group(formControls);
  }

  /**
   * Načtení uživatelů pro filtr
   */
  loadUsers(): void {
    this.userService.getUsers().subscribe({
      next: (users) => {
        this.users = users;
      },
      error: (err) => {
        console.error('Chyba při načítání uživatelů', err);
      }
    });
  }

  /**
   * Načtení uložených filtrů
   */
  loadSavedFilters(): void {
    this.logsService.getSavedLogFilters(this.logType).subscribe({
      next: (filters) => {
        this.savedFilters = filters;
      },
      error: (err) => {
        console.error('Chyba při načítání uložených filtrů', err);
      }
    });
  }

  /**
   * Aplikování filtru
   */
  applyFilter(): void {
    const formValues = this.filterForm.value;
    const filterData: LogFilterRequest = { ...formValues };

    // Zpracování časového období
    if (formValues.period !== 'custom') {
      // Pokud není vybráno vlastní období, vypočítáme datum podle vybraného období
      const today = new Date();
      const endOfDay = new Date(today);
      endOfDay.setHours(23, 59, 59, 999);

      const fromDate = new Date(today);
      fromDate.setDate(today.getDate() - Number(formValues.period));
      fromDate.setHours(0, 0, 0, 0);

      filterData.fromDate = fromDate;
      filterData.toDate = endOfDay;
    } else {
      // Při vlastním období použijeme zadané hodnoty
      if (formValues.fromDate) {
        filterData.fromDate = new Date(formValues.fromDate);
      }

      if (formValues.toDate) {
        filterData.toDate = new Date(formValues.toDate);
      }
    }

    // Uložení stavu filtru do localStorage
    this.saveFilterState(filterData);

    this.filterChange.emit(filterData);
  }

  /**
   * Načtení dat podle aktuálního filtru
   */
  refreshData(): void {
    // Aplikujeme aktuální filtr znovu
    this.applyFilter();
  }

  /**
   * Uložení stavu filtru do localStorage
   */
  private saveFilterState(filterData: LogFilterRequest): void {
    try {
      const filterKey = `current_filter_${this.logType}`;
      localStorage.setItem(filterKey, JSON.stringify(filterData));
    } catch (error) {
      console.error(`Chyba při ukládání stavu filtru pro ${this.logType}`, error);
    }
  }

  /**
   * Resetování filtru
   */
  resetFilter(): void {
    // Nastavení výchozích hodnot pro časové filtry
    const today = new Date();
    const endOfDay = new Date(today);
    endOfDay.setHours(23, 59, 59, 999);

    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 7);
    sevenDaysAgo.setHours(0, 0, 0, 0);

    // Formátování dat pro input typu datetime-local
    const fromDateStr = this.formatDateForInput(sevenDaysAgo);
    const toDateStr = this.formatDateForInput(endOfDay);

    // Základní hodnoty pro reset
    const resetValues: any = {
      period: 7, // Výchozí hodnota je 7 dní
      fromDate: fromDateStr,
      toDate: toDateStr,
      maxResults: 100
    };

    // Nastavení výchozí hodnoty pro zdroj podle typu logu
    if (this.logType === 'activity') {
      resetValues.source = LogSource.DISAdmin;
    } else if (this.logType === 'api') {
      resetValues.source = LogSource.DISApi;
    }

    this.filterForm.reset(resetValues);
    this.updateActiveFiltersIndicator();
    this.applyFilter();

    // Zobrazení notifikace o resetování filtru
    this.toastr.info('Filtr byl resetován', 'Reset');
  }

  /**
   * Formátuje datum pro input typu datetime-local
   * Format: YYYY-MM-DDThh:mm
   */
  private formatDateForInput(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}`;
  }

  /**
   * Otevření modálního okna pro uložení filtru
   */
  openSaveFilterModal(): void {
    this.saveFilterForm.reset();
    this.modalService.open('saveFilterModal');
  }

  /**
   * Uložení filtru
   */
  saveFilter(): void {
    if (this.saveFilterForm.invalid) {
      return;
    }

    const filterName = this.saveFilterForm.get('name')?.value;
    const filterData = this.filterForm.value;

    const saveRequest: SavedLogFilterRequest = {
      name: filterName,
      logType: this.logType,
      filterData: JSON.stringify(filterData)
    };

    this.isLoading = true;
    this.logsService.saveLogFilter(saveRequest).subscribe({
      next: (response) => {
        this.savedFilters.push(response);
        this.modalService.close('saveFilterModal');
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Chyba při ukládání filtru', err);
        this.error = 'Nepodařilo se uložit filtr';
        this.isLoading = false;
      }
    });
  }

  /**
   * Aplikování uloženého filtru
   */
  applySavedFilter(filter: SavedLogFilterResponse): void {
    try {
      const parsedData = JSON.parse(filter.filterData);

      // Vytvoříme nový objekt pro formulář s řetězcovými hodnotami pro datetime-local inputy
      const formValues: any = { ...parsedData };

      // Detekce starého formátu filtru (bez pole period)
      if (parsedData.fromDate && parsedData.toDate && !parsedData.period) {
        // Pokusíme se detekovat období podle rozdílu dat
        const fromDate = new Date(parsedData.fromDate);
        const toDate = new Date(parsedData.toDate);
        const diffDays = Math.round((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));

        // Pokud je rozdíl blízký některému z předdefinovaných období, použijeme ho
        if (diffDays <= 1) {
          formValues.period = 1;
        } else if (diffDays <= 7) {
          formValues.period = 7;
        } else if (diffDays <= 30) {
          formValues.period = 30;
        } else if (diffDays <= 90) {
          formValues.period = 90;
        } else {
          formValues.period = 'custom';
        }
      }

      // Převod objektů Date na řetězce pro input
      if (parsedData.fromDate) {
        const fromDate = new Date(parsedData.fromDate);
        formValues.fromDate = this.formatDateForInput(fromDate);
      }
      if (parsedData.toDate) {
        const toDate = new Date(parsedData.toDate);
        formValues.toDate = this.formatDateForInput(toDate);
      }

      // Resetujeme formulář, aby se vyčistily všechny hodnoty
      this.filterForm.reset({
        maxResults: 100
      });

      // Nastavíme hodnoty formuláře
      this.filterForm.patchValue(formValues);

      // Zajistíme, že zdroj je správně nastaven podle typu logu
      if (this.logType === 'activity') {
        this.filterForm.patchValue({ source: LogSource.DISAdmin });
      } else if (this.logType === 'api') {
        this.filterForm.patchValue({ source: LogSource.DISApi });
      }

      // Aktualizace validace datumových polí
      this.updateDateFieldsValidation(formValues.period);

      // Aktualizujeme indikátor aktivních filtrů
      this.updateActiveFiltersIndicator();

      // Aplikujeme filtr, který převede řetězce zpět na Date objekty
      this.applyFilter();
    } catch (error) {
      console.error('Chyba při aplikování uloženého filtru', error);
    }
  }

  /**
   * Odstranění uloženého filtru
   */
  async deleteSavedFilter(filter: SavedLogFilterResponse, event: Event): Promise<void> {
    event.stopPropagation(); // Zabránění aplikování filtru při kliknutí na tlačítko smazat

    // Použijeme vlastní dialog pro potvrzení místo nativního confirm
    const confirmed = await this.modalService.confirm(
      `Opravdu chcete smazat filtr "${filter.name}"?`,
      'Odstranit filtr',
      'OK',
      'Zrušit',
      'btn-danger',
      'btn-secondary'
    );

    if (confirmed) {
      this.logsService.deleteLogFilter(filter.id).subscribe({
        next: () => {
          this.savedFilters = this.savedFilters.filter(f => f.id !== filter.id);
        },
        error: (err) => {
          console.error('Chyba při mazání filtru', err);
        }
      });
    }
  }
}
