import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { LogFilterComponent } from './log-filter/log-filter.component';
import { ActivityLogsComponent } from './activity-logs/activity-logs.component';
import { ErrorLogsComponent } from './error-logs/error-logs.component';
import { ApiLogsComponent } from './api-logs/api-logs.component';
import { TabItem } from '../shared/tab-navigation/tab-navigation.component';

@Component({
  selector: 'app-logs',
  templateUrl: './logs.component.html',
  styleUrls: ['./logs.component.css']
})
export class LogsComponent implements OnInit {
  activeTab: string = 'activity';

  // Definice záložek
  tabs: TabItem[] = [
    { id: 'activity', label: 'Auditní logy', icon: 'activity' },
    { id: 'error', label: 'Logy aplikace', icon: 'exclamation-triangle' },
    { id: 'api', label: 'Logy DIS API', icon: 'hdd-network' }
  ];

  // Ukládání stavu filtrů pro jednotlivé záložky
  private activityFilterState: any = null;
  private errorFilterState: any = null;
  private apiFilterState: any = null;

  constructor(
    private router: Router,
    private route: ActivatedRoute
  ) { }

  ngOnInit(): void {
    // Získání aktivní záložky z URL parametru nebo z localStorage
    this.route.queryParams.subscribe(params => {
      if (params['tab']) {
        // Priorita: URL parametr
        this.activeTab = params['tab'];
        // Uložení do localStorage pro budoucí použití
        this.saveActiveTabToLocalStorage(params['tab']);
      } else {
        // Pokud není v URL, zkusíme načíst z localStorage
        const lastActiveTab = this.loadActiveTabFromLocalStorage();
        if (lastActiveTab) {
          this.activeTab = lastActiveTab;
          // Aktualizace URL parametru
          this.router.navigate([], {
            relativeTo: this.route,
            queryParams: { tab: lastActiveTab },
            queryParamsHandling: 'merge'
          });
        }
      }
    });
  }

  /**
   * Změna aktivní záložky
   */
  changeTab(tab: any): void {
    // Uložení stavu aktuálního filtru před přepnutím záložky
    this.saveCurrentFilterState();

    // Převod parametru na string, pokud není
    const tabId = typeof tab === 'string' ? tab : String(tab);
    this.activeTab = tabId;

    // Uložení aktivní záložky do localStorage
    this.saveActiveTabToLocalStorage(tabId);

    // Aktualizace URL parametru
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { tab: tabId },
      queryParamsHandling: 'merge'
    });
  }

  /**
   * Uložení stavu aktuálního filtru
   */
  private saveCurrentFilterState(): void {
    // Uložení stavu filtru podle aktuální záložky
    switch (this.activeTab) {
      case 'activity':
        this.activityFilterState = this.getFilterState('activity');
        break;
      case 'error':
        this.errorFilterState = this.getFilterState('error');
        break;
      case 'api':
        this.apiFilterState = this.getFilterState('api');
        break;
    }
  }

  /**
   * Získání stavu filtru pro daný typ logu
   */
  private getFilterState(logType: string): any {
    // Získání stavu filtru z localStorage
    try {
      const filterKey = `current_filter_${logType}`;
      const filterJson = localStorage.getItem(filterKey);
      if (filterJson) {
        return JSON.parse(filterJson);
      }
    } catch (error) {
      console.error(`Chyba při načítání stavu filtru pro ${logType}`, error);
    }
    return null;
  }

  /**
   * Uložení aktivní záložky do localStorage
   */
  private saveActiveTabToLocalStorage(tabId: string): void {
    try {
      localStorage.setItem('logs_active_tab', tabId);
      console.log(`Uložena aktivní záložka do localStorage: ${tabId}`);
    } catch (error) {
      console.error('Chyba při ukládání aktivní záložky do localStorage:', error);
    }
  }

  /**
   * Načtení aktivní záložky z localStorage
   */
  private loadActiveTabFromLocalStorage(): string | null {
    try {
      const activeTab = localStorage.getItem('logs_active_tab');
      console.log(`Načtena aktivní záložka z localStorage: ${activeTab}`);
      return activeTab;
    } catch (error) {
      console.error('Chyba při načítání aktivní záložky z localStorage:', error);
      return null;
    }
  }
}
