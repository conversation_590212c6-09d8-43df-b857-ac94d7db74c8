export interface ActivityLogResponse {
  id: number;
  userId?: number;
  username?: string;
  timestamp: Date;
  activityType: string;
  entityName: string;
  entityId?: number;
  description: string;
  ipAddress: string;
  additionalInfo?: string;
  source: number; // Změna ze string na number pro explicitní zdroj

  // Nové sloupce pro API přístupy
  method?: string;
  endpoint?: string;
  statusCode?: number;
  responseTimeMs?: number;
}

export interface ErrorLogResponse {
  id: number;
  timestamp: Date;
  message: string;
  stackTrace?: string;
  source: string; // Zůstává string, protože ErrorLog.Source je string v backendu
  requestPath?: string;
  requestMethod?: string;
  statusCode?: number;
  userId?: number;
  username?: string;
  ipAddress: string;
  additionalInfo?: string;
  logLevel: string; // Nová vlastnost pro úroveň logování
  category?: string; // Nová vlastnost pro kategorii loggeru
}

export interface LogFilterRequest {
  userId?: number;
  username?: string;
  fromDate?: Date;
  toDate?: Date;
  maxResults?: number;
  entityName?: string;
  entityId?: number;
  activityType?: string;
  ipAddress?: string;
  source?: number; // Změna ze string na number pro explicitní zdroj
  logLevel?: number; // Nový filtr pro úroveň logování
  category?: string; // Nový filtr pro kategorii loggeru
}

export interface SavedLogFilterRequest {
  name: string;
  logType: string; // activity, error, api
  filterData: string; // JSON serializovaný LogFilterRequest
}

export interface SavedLogFilterResponse {
  id: number;
  userId: number;
  name: string;
  logType: string;
  filterData: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum LogSource {
  Undefined = 0,
  DISAdmin = 1,
  DISApi = 2
}

export enum ApplicationLogLevel {
  Trace = 0,
  Debug = 1,
  Information = 2,
  Warning = 3,
  Error = 4,
  Critical = 5
}

export enum ActivityTypeEnum {
  Login = 'Login',
  Logout = 'Logout',
  Create = 'Create',
  Update = 'Update',
  Delete = 'Delete',
  Export = 'Export',
  Import = 'Import',
  PasswordChange = 'PasswordChange',
  ApiAccess = 'ApiAccess',
  Other = 'Other'
}

// Pomocná třída pro překlad typů aktivit do češtiny
export class ActivityTypeHelper {
  static getLocalizedName(type: string): string {
    switch (type) {
      case 'Login': return 'Přihlášení';
      case 'Logout': return 'Odhlášení';
      case 'Create': return 'Vytvoření';
      case 'Update': return 'Úprava';
      case 'Delete': return 'Smazání';
      case 'Export': return 'Export';
      case 'Import': return 'Import';
      case 'PasswordChange': return 'Změna hesla';
      case 'ApiAccess': return 'Přístup k API';
      case 'Other': return 'Ostatní';
      default: return type;
    }
  }

  static getActivityTypes(): { value: string, label: string }[] {
    return [
      { value: 'Login', label: 'Přihlášení' },
      { value: 'Logout', label: 'Odhlášení' },
      { value: 'Create', label: 'Vytvoření' },
      { value: 'Update', label: 'Úprava' },
      { value: 'Delete', label: 'Smazání' },
      { value: 'Export', label: 'Export' },
      { value: 'Import', label: 'Import' },
      { value: 'PasswordChange', label: 'Změna hesla' },
      { value: 'ApiAccess', label: 'Přístup k API' },
      { value: 'Other', label: 'Ostatní' }
    ];
  }
}

// Pomocná třída pro překlad zdrojů logů do češtiny
export class LogSourceHelper {
  static getLocalizedName(source: number): string {
    switch (source) {
      case LogSource.DISAdmin: return 'DISAdmin';
      case LogSource.DISApi: return 'DIS API';
      case LogSource.Undefined: return 'Neurčeno';
      default: return 'Neznámý';
    }
  }

  static getLogSources(): { value: number, label: string }[] {
    return [
      { value: LogSource.DISAdmin, label: 'DISAdmin' },
      { value: LogSource.DISApi, label: 'DIS API' },
      { value: LogSource.Undefined, label: 'Neurčeno' }
    ];
  }
}

// Pomocná třída pro práci s úrovněmi logování
export class ApplicationLogLevelHelper {
  static getLocalizedName(logLevel: string): string {
    switch (logLevel) {
      case 'Trace': return 'Trasování';
      case 'Debug': return 'Ladění';
      case 'Information': return 'Informace';
      case 'Warning': return 'Varování';
      case 'Error': return 'Chyba';
      case 'Critical': return 'Kritická';
      default: return logLevel;
    }
  }

  static getLogLevels(): { value: number, label: string }[] {
    return [
      { value: ApplicationLogLevel.Trace, label: 'Trasování' },
      { value: ApplicationLogLevel.Debug, label: 'Ladění' },
      { value: ApplicationLogLevel.Information, label: 'Informace' },
      { value: ApplicationLogLevel.Warning, label: 'Varování' },
      { value: ApplicationLogLevel.Error, label: 'Chyba' },
      { value: ApplicationLogLevel.Critical, label: 'Kritická' }
    ];
  }

  static getLogLevelClass(logLevel: string): string {
    switch (logLevel) {
      case 'Trace': return 'bg-secondary';
      case 'Debug': return 'bg-info';
      case 'Information': return 'bg-primary';
      case 'Warning': return 'bg-warning';
      case 'Error': return 'bg-danger';
      case 'Critical': return 'bg-dark';
      default: return 'bg-light';
    }
  }
}

export interface PerformanceMetricResponse {
  id: number;
  instanceId: number;
  instanceName: string;
  className: string;
  methodName: string;
  parameters?: string;
  totalCount: number;
  nonZeroCount: number;
  min: number;
  max: number;
  avg: number;
  median: number;
  percentil95: number;
  percentil99: number;
  stdDev: number;
  timestamp: Date;
  versionNumber?: string;
}

export interface PerformanceStatisticsResponse {
  className: string;
  methodName: string;
  averageExecutionTimeMs: number;
  medianExecutionTimeMs: number;
  minExecutionTimeMs: number;
  maxExecutionTimeMs: number;
  percentil95: number;
  percentil99: number;
  stdDev: number;
  totalCount: number;
  nonZeroCount: number;
}
