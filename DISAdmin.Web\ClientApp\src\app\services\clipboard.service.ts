import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';

@Injectable({
  providedIn: 'root'
})
export class ClipboardService {

  constructor(private toastr: ToastrService) { }

  /**
   * Zkopíruje text do schr<PERSON>ky s automatickým fallback a toast notifikací
   * @param text Text k zkopírování
   * @param successMessage Zpráva při <PERSON> (volitelná)
   * @param errorMessage Zpráva při chybě (volitelná)
   * @returns Promise<boolean> - true při <PERSON>, false při chybě
   */
  async copyToClipboard(
    text: string, 
    successMessage: string = 'Text byl zkopírován do schr<PERSON>ky',
    errorMessage: string = 'Nepodařilo se zkopírovat text do schránky'
  ): Promise<boolean> {
    
    if (!text || text.trim() === '') {
      this.toastr.error('Není co kopírovat', 'Chy<PERSON>');
      return false;
    }

    try {
      // Pokusíme se použít moderní Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
        this.toastr.success(successMessage, 'Úspěch');
        return true;
      } else {
        // Fallback pro starší prohlížeče nebo nezabezpečené kontexty
        return this.fallbackCopyToClipboard(text, successMessage, errorMessage);
      }
    } catch (err) {
      console.error('Clipboard API selhalo:', err);
      // Pokus o fallback
      return this.fallbackCopyToClipboard(text, successMessage, errorMessage);
    }
  }

  /**
   * Fallback metoda pro kopírování do schránky pomocí execCommand
   * @param text Text k zkopírování
   * @param successMessage Zpráva při úspěchu
   * @param errorMessage Zpráva při chybě
   * @returns boolean - true při úspěchu, false při chybě
   */
  private fallbackCopyToClipboard(
    text: string, 
    successMessage: string, 
    errorMessage: string
  ): boolean {
    try {
      // Vytvoříme dočasný textarea element
      const textArea = document.createElement('textarea');
      textArea.value = text;
      
      // Nastavíme styly pro skrytí elementu
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      textArea.style.width = '2em';
      textArea.style.height = '2em';
      textArea.style.padding = '0';
      textArea.style.border = 'none';
      textArea.style.outline = 'none';
      textArea.style.boxShadow = 'none';
      textArea.style.background = 'transparent';
      
      document.body.appendChild(textArea);
      
      // Označíme text a zkopírujeme
      textArea.focus();
      textArea.select();
      textArea.setSelectionRange(0, text.length); // Pro mobilní zařízení
      
      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);
      
      if (successful) {
        this.toastr.success(successMessage, 'Úspěch');
        return true;
      } else {
        throw new Error('execCommand returned false');
      }
    } catch (err) {
      console.error('Fallback kopírování selhalo:', err);
      this.toastr.error(`${errorMessage}. Zkopírujte text prosím ručně.`, 'Chyba');
      return false;
    }
  }

  /**
   * Zkopíruje text z input elementu do schránky
   * @param inputElement HTML input element
   * @param successMessage Zpráva při úspěchu (volitelná)
   * @param errorMessage Zpráva při chybě (volitelná)
   * @returns Promise<boolean> - true při úspěchu, false při chybě
   */
  async copyFromInput(
    inputElement: HTMLInputElement,
    successMessage: string = 'Text byl zkopírován do schránky',
    errorMessage: string = 'Nepodařilo se zkopírovat text do schránky'
  ): Promise<boolean> {
    
    const text = inputElement.value;
    
    if (!text || text.trim() === '') {
      this.toastr.error('Input element neobsahuje žádný text', 'Chyba');
      return false;
    }

    try {
      // Pokusíme se použít moderní Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
        this.toastr.success(successMessage, 'Úspěch');
        return true;
      } else {
        // Fallback s použitím input elementu
        return this.fallbackCopyFromInput(inputElement, successMessage, errorMessage);
      }
    } catch (err) {
      console.error('Clipboard API selhalo:', err);
      // Pokus o fallback
      return this.fallbackCopyFromInput(inputElement, successMessage, errorMessage);
    }
  }

  /**
   * Fallback metoda pro kopírování z input elementu
   * @param inputElement HTML input element
   * @param successMessage Zpráva při úspěchu
   * @param errorMessage Zpráva při chybě
   * @returns boolean - true při úspěchu, false při chybě
   */
  private fallbackCopyFromInput(
    inputElement: HTMLInputElement,
    successMessage: string,
    errorMessage: string
  ): boolean {
    try {
      // Označíme text v input elementu
      inputElement.focus();
      inputElement.select();
      inputElement.setSelectionRange(0, inputElement.value.length); // Pro mobilní zařízení
      
      const successful = document.execCommand('copy');
      
      if (successful) {
        this.toastr.success(successMessage, 'Úspěch');
        return true;
      } else {
        throw new Error('execCommand returned false');
      }
    } catch (err) {
      console.error('Fallback kopírování z input selhalo:', err);
      this.toastr.error(`${errorMessage}. Zkopírujte text prosím ručně.`, 'Chyba');
      return false;
    }
  }

  /**
   * Kontrola, zda je Clipboard API dostupné
   * @returns boolean - true pokud je Clipboard API dostupné
   */
  isClipboardApiAvailable(): boolean {
    return !!(navigator.clipboard && window.isSecureContext);
  }
}
