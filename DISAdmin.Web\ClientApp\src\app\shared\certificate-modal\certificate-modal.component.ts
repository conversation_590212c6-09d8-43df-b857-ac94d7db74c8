import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CertificateGenerationResponse } from '../../models/certificate.model';
import { ClipboardService } from '../../services/clipboard.service';

@Component({
  selector: 'app-certificate-modal',
  templateUrl: './certificate-modal.component.html',
  styleUrls: ['./certificate-modal.component.css']
})
export class CertificateModalComponent {
  @Input() certificate: CertificateGenerationResponse | null = null;
  @Input() instanceName: string = '';
  @Input() modalId: string = 'certificateModal';
  @Output() close = new EventEmitter<void>();
  @Output() download = new EventEmitter<void>();

  // Příznak pro zobrazení potvrzení o zkopírování
  passwordCopied: boolean = false;

  constructor(private clipboardService: ClipboardService) {}

  /**
   * Zavření modálního okna
   */
  closeModal(): void {
    this.close.emit();
  }

  /**
   * Stažení certifikátu
   */
  downloadCertificate(): void {
    this.download.emit();
  }

  /**
   * Zkopírování hesla certifikátu do schránky
   */
  async copyPasswordToClipboard(): Promise<void> {
    if (!this.certificate || !this.certificate.password) {
      return;
    }

    const success = await this.clipboardService.copyToClipboard(
      this.certificate.password,
      'Heslo certifikátu bylo zkopírováno do schránky',
      'Nepodařilo se zkopírovat heslo certifikátu'
    );

    if (success) {
      // Nastavení příznaku pro zobrazení potvrzení
      this.passwordCopied = true;

      // Po 3 sekundách skryjeme potvrzení
      setTimeout(() => {
        this.passwordCopied = false;
      }, 3000);
    }
  }
}
