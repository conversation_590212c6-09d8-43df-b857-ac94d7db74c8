using Microsoft.AspNetCore.Mvc;
using DISAdmin.Core.Converters;
using DISAdmin.Core.Data.Entities;

namespace DISAdmin.Web.Extensions;

/// <summary>
/// Extension metody pro konfiguraci JSON options
/// </summary>
public static class JsonOptionsExtensions
{
    /// <summary>
    /// Přidá všechny custom JSON convertery pro DISAdmin aplikaci
    /// </summary>
    /// <param name="options">JSON options</param>
    public static void AddDISAdminConverters(this JsonOptions options)
    {
        // Converter pro nullable DateTime (prázdné stringy → null)
        options.JsonSerializerOptions.Converters.Add(new NullableDateTimeConverter());
        
        // Generické enum convertery pro všechny enum typy v aplikaci
        options.JsonSerializerOptions.Converters.Add(new EnumConverter<InstanceStatus>());
        options.JsonSerializerOptions.Converters.Add(new EnumConverter<ChangeType>());
        options.JsonSerializerOptions.Converters.Add(new EnumConverter<LogSeverity>());
        options.JsonSerializerOptions.Converters.Add(new EnumConverter<ActivityType>());
        options.JsonSerializerOptions.Converters.Add(new EnumConverter<LogSource>());
        options.JsonSerializerOptions.Converters.Add(new EnumConverter<ApplicationLogLevel>());
        
        // Nullable enum convertery (pokud budou potřeba)
        // options.JsonSerializerOptions.Converters.Add(new NullableEnumConverter<InstanceStatus>());
    }
    
    /// <summary>
    /// Přidá converter pro konkrétní enum typ
    /// </summary>
    /// <typeparam name="TEnum">Typ enum</typeparam>
    /// <param name="options">JSON options</param>
    public static void AddEnumConverter<TEnum>(this JsonOptions options) where TEnum : struct, Enum
    {
        options.JsonSerializerOptions.Converters.Add(new EnumConverter<TEnum>());
    }
    
    /// <summary>
    /// Přidá converter pro nullable enum typ
    /// </summary>
    /// <typeparam name="TEnum">Typ enum</typeparam>
    /// <param name="options">JSON options</param>
    public static void AddNullableEnumConverter<TEnum>(this JsonOptions options) where TEnum : struct, Enum
    {
        options.JsonSerializerOptions.Converters.Add(new NullableEnumConverter<TEnum>());
    }
}
