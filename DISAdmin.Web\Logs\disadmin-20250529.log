2025-05-29 15:16:11.337 +02:00 [WRN] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: C:\Users\<USER>\Documents\VSCodeProjects\DISAdminAugment\DISAdmin.Web\wwwroot. Static files may be unavailable.
2025-05-29 15:16:11.392 +02:00 [INF] Program: DISAdmin aplikace se spouští...
2025-05-29 15:16:11.395 +02:00 [INF] Program: Test Information zpráva pro file logging
2025-05-29 15:16:11.398 +02:00 [WRN] Program: Test Warning zpráva pro file logging
2025-05-29 15:16:11.400 +02:00 [ERR] Program: Test Error zpráva pro file logging
2025-05-29 15:16:11.403 +02:00 [FTL] Program: Test Critical zpráva pro file logging
2025-05-29 15:16:11.405 +02:00 [INF] Program: File logging test dokončen. Logy by m<PERSON><PERSON> b<PERSON>t ul<PERSON> v: C:\Users\<USER>\Documents\VSCodeProjects\DISAdminAugment\DISAdmin.Web\Logs
2025-05-29 15:16:11.430 +02:00 [INF] Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager: User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-29 15:16:11.821 +02:00 [INF] DISAdmin.Api.Services.UnhandledExceptionService: UnhandledExceptionService byl spuštěn
2025-05-29 15:16:13.799 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 15:16:13.820 +02:00 [INF] Microsoft.EntityFrameworkCore.Migrations: Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-05-29 15:16:13.851 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-05-29 15:16:13.953 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-05-29 15:16:13.970 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 15:16:13.976 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-05-29 15:16:13.989 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-05-29 15:16:14.005 +02:00 [INF] Microsoft.EntityFrameworkCore.Migrations: No migrations were applied. The database is already up to date.
2025-05-29 15:16:14.013 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-05-29 15:16:14.312 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-05-29 15:16:14.325 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Alerting Background Service is starting
2025-05-29 15:16:14.327 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Alerting Background Service is running check at: "2025-05-29T15:16:14.3270110+02:00"
2025-05-29 15:16:14.570 +02:00 [INF] DISAdmin.Api.BackgroundServices.RealtimeMetricsBackgroundService: Realtime metrics background service is starting
2025-05-29 15:16:14.578 +02:00 [INF] DISAdmin.Api.BackgroundServices.CertificateRotationBackgroundService: Služba pro automatickou rotaci certifikátů byla spuštěna
2025-05-29 15:16:14.579 +02:00 [INF] DISAdmin.Api.BackgroundServices.CertificateRotationBackgroundService: Spouštění kontroly expirujících certifikátů
2025-05-29 15:16:14.583 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[ClientCertificateThumbprint] IS NOT NULL AND [d].[CertificateExpirationDate] > GETUTCDATE() AND [d].[CertificateExpirationDate] < DATEADD(day, CAST(30.0E0 AS int), GETUTCDATE())
2025-05-29 15:16:14.586 +02:00 [INF] DISAdmin.Core.Services.CertificateRotationService: Kontrola expirujících certifikátů
2025-05-29 15:16:14.602 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked expiring certificates
2025-05-29 15:16:14.627 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[FailedCertificateValidationCount] > 5
2025-05-29 15:16:14.661 +02:00 [WRN] Microsoft.EntityFrameworkCore.Query: The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-05-29 15:16:14.678 +02:00 [INF] DISAdmin.Api.BackgroundServices.AnomalyDetectionBackgroundService: Služba pro detekci anomálií byla spuštěna
2025-05-29 15:16:14.679 +02:00 [INF] DISAdmin.Api.BackgroundServices.AnomalyDetectionBackgroundService: Spouštění detekce anomálií
2025-05-29 15:16:14.684 +02:00 [INF] DISAdmin.Core.Services.AnomalyDetectionService: Spuštění detekce anomálií v přístupech k API
2025-05-29 15:16:14.687 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[EnableAutoRotation], [c].[LastUpdated], [c].[LastUpdatedBy], [c].[NewCertificateValidityDays], [c].[NotificationEmails], [c].[NotificationRepeatDays], [c].[NotifyDaysBeforeExpiration], [c].[RotateDaysBeforeExpiration], [c].[SendExpirationNotifications]
FROM [CertificateRotationSettings] AS [c]
2025-05-29 15:16:14.713 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status]
FROM [DISInstances] AS [d]
WHERE [d].[Status] = 0
2025-05-29 15:16:14.767 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[ClientCertificateThumbprint] IS NOT NULL AND [d].[CertificateExpirationDate] IS NOT NULL
2025-05-29 15:16:14.823 +02:00 [WRN] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: C:\Users\<USER>\Documents\VSCodeProjects\DISAdminAugment\DISAdmin.Web\wwwroot. Static files may be unavailable.
2025-05-29 15:16:14.871 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (7ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 15:16:14.887 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (18ms) [Parameters=[@__instance_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Alerts] AS [a]
        WHERE [a].[InstanceId] = @__instance_Id_0 AND [a].[AlertType] = 1 AND [a].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-05-29 15:16:14.894 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (67ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 15:16:14.902 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[@__instance_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Alerts] AS [a]
        WHERE [a].[InstanceId] = @__instance_Id_0 AND [a].[AlertType] = 1 AND [a].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-05-29 15:16:14.908 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked failed connection attempts
2025-05-29 15:16:14.917 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (13ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 15:16:14.918 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 15:16:14.930 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 15:16:14.938 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 15:16:14.945 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 15:16:14.955 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 15:16:14.958 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[Description], [s].[EventType], [s].[InstanceId], [s].[IpAddress], [s].[IsResolved], [s].[Resolution], [s].[ResolvedAt], [s].[ResolvedBy], [s].[Resource], [s].[Severity], [s].[Timestamp], [s].[Username]
FROM [SecurityEvents] AS [s]
WHERE [s].[EventType] = 1 AND [s].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE()) AND [s].[IsResolved] = CAST(0 AS bit)
2025-05-29 15:16:14.962 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 15:16:14.966 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked suspicious activities
2025-05-29 15:16:14.972 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 15:16:14.978 +02:00 [INF] DISAdmin.Api.BackgroundServices.CertificateRotationBackgroundService: Kontrola expirujících certifikátů dokončena
2025-05-29 15:16:14.981 +02:00 [WRN] Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServer: The ASP.NET Core developer certificate is not trusted. For information about trusting the ASP.NET Core developer certificate, see https://aka.ms/aspnet/https-trust-dev-cert
2025-05-29 15:16:15.013 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (88ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 15:16:15.035 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[InstanceId], AVG(CAST([p].[Avg] AS float)) AS [AvgResponseTime], AVG(CAST([p].[Max] AS float)) AS [MaxResponseTime], AVG(CAST([p].[Percentil95] AS float)) AS [P95ResponseTime]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())
GROUP BY [p].[InstanceId]
2025-05-29 15:16:15.040 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked method performance
2025-05-29 15:16:15.048 +02:00 [INF] Microsoft.Hosting.Lifetime: Now listening on: https://localhost:7029
2025-05-29 15:16:15.051 +02:00 [INF] Microsoft.Hosting.Lifetime: Now listening on: http://localhost:5167
2025-05-29 15:16:15.053 +02:00 [INF] Microsoft.Hosting.Lifetime: Application started. Press Ctrl+C to shut down.
2025-05-29 15:16:15.055 +02:00 [INF] Microsoft.Hosting.Lifetime: Hosting environment: Development
2025-05-29 15:16:15.056 +02:00 [INF] Microsoft.Hosting.Lifetime: Content root path: C:\Users\<USER>\Documents\VSCodeProjects\DISAdminAugment\DISAdmin.Web
2025-05-29 15:16:15.061 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[InstanceId], COUNT(*) AS [CallsCount]
FROM [DiagnosticLogs] AS [d]
WHERE [d].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())
GROUP BY [d].[InstanceId]
2025-05-29 15:16:15.069 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked API calls count
2025-05-29 15:16:15.093 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[InstanceId], COUNT(*) AS [TotalCalls], COUNT(CASE
    WHEN [d].[ResponseStatusCode] >= 400 THEN 1
END) AS [ErrorCalls]
FROM [DiagnosticLogs] AS [d]
WHERE [d].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())
GROUP BY [d].[InstanceId]
2025-05-29 15:16:15.098 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked error rate
2025-05-29 15:16:15.103 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AlertType], [a].[Description], [a].[InstanceId], [a].[IsNotificationSent], [a].[IsResolved], [a].[Resolution], [a].[ResolvedAt], [a].[ResolvedBy], [a].[Severity], [a].[Timestamp]
FROM [Alerts] AS [a]
WHERE [a].[InstanceId] = @__8__locals1_instanceId_0 AND [a].[AlertType] = 6 AND [a].[Description] LIKE N'%mimo pracovní dobu%' AND [a].[Timestamp] >= DATEADD(day, CAST(-7.0E0 AS int), GETUTCDATE())
2025-05-29 15:16:15.124 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[EnableIpWhitelisting] = CAST(1 AS bit)
2025-05-29 15:16:15.127 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked unauthorized IP access
2025-05-29 15:16:15.153 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (10ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 15:16:15.154 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (12ms) [Parameters=[@__cutoffTime_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[IpAddress], COUNT(*) AS [Count]
FROM [ActivityLogs] AS [a]
WHERE [a].[ActivityType] = 0 AND [a].[Timestamp] > @__cutoffTime_0
GROUP BY [a].[IpAddress]
HAVING COUNT(*) > 10
2025-05-29 15:16:15.160 +02:00 [INF] DISAdmin.Core.Services.AnomalyDetectionService: Spuštění detekce anomálií v přihlašování uživatelů
2025-05-29 15:16:15.177 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[@__startDate_0='?' (DbType = DateTime2), @__endDate_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [l].[Id], [l].[IpAddress], [l].[Success], [l].[Timestamp], [l].[UserAgent], [l].[UserId], [l].[Username]
FROM [LoginAttempts] AS [l]
WHERE [l].[Timestamp] >= @__startDate_0 AND [l].[Timestamp] <= @__endDate_1
ORDER BY [l].[Timestamp]
2025-05-29 15:16:15.178 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (14ms) [Parameters=[@__cutoffTime_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[IpAddress], COUNT(*) AS [Count]
FROM [ActivityLogs] AS [a]
WHERE [a].[ActivityType] = 8 AND [a].[Timestamp] > @__cutoffTime_0
GROUP BY [a].[IpAddress]
2025-05-29 15:16:15.194 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[LastKnownIpAddress]
FROM [DISInstances] AS [d]
WHERE [d].[LastKnownIpAddress] IS NOT NULL
2025-05-29 15:16:15.207 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Spuštění detekce anomálií ve výkonnostních metrikách
2025-05-29 15:16:15.212 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status]
FROM [DISInstances] AS [d]
WHERE [d].[Status] = 0
2025-05-29 15:16:15.214 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (8ms) [Parameters=[@__cutoffTime_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityName] = N'Certificate' AND [a].[Timestamp] > @__cutoffTime_0
2025-05-29 15:16:15.231 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 15:16:15.236 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Nedostatek dat pro analýzu výkonnostních metrik pro instanci 2
2025-05-29 15:16:15.244 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 15:16:15.248 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Nedostatek dat pro analýzu výkonnostních metrik pro instanci 3
2025-05-29 15:16:15.252 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 15:16:15.296 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Detected 0 suspicious events
2025-05-29 15:16:15.313 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 15:16:15.318 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Nedostatek dat pro analýzu výkonnostních metrik pro instanci 8
2025-05-29 15:16:15.320 +02:00 [INF] DISAdmin.Api.BackgroundServices.AnomalyDetectionBackgroundService: Detekce anomálií dokončena
2025-05-29 15:16:18.863 +02:00 [INF] Microsoft.AspNetCore.SpaProxy.SpaProxyLaunchManager: No SPA development server running at http://localhost:4200 found.
2025-05-29 15:51:22.552 +02:00 [WRN] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: C:\Users\<USER>\Documents\VSCodeProjects\DISAdminAugment\DISAdmin.Web\wwwroot. Static files may be unavailable.
2025-05-29 15:51:22.641 +02:00 [INF] Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager: User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-29 15:51:22.701 +02:00 [INF] DISAdmin.Api.Services.UnhandledExceptionService: UnhandledExceptionService byl spuštěn
2025-05-29 15:51:24.806 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 15:51:24.830 +02:00 [INF] Microsoft.EntityFrameworkCore.Migrations: Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-05-29 15:51:24.874 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (37ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-05-29 15:51:24.995 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-05-29 15:51:25.011 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 15:51:25.018 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-05-29 15:51:25.034 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-05-29 15:51:25.050 +02:00 [INF] Microsoft.EntityFrameworkCore.Migrations: No migrations were applied. The database is already up to date.
2025-05-29 15:51:25.058 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-05-29 15:51:25.408 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-05-29 15:51:25.420 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Alerting Background Service is starting
2025-05-29 15:51:25.422 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Alerting Background Service is running check at: "2025-05-29T15:51:25.4222178+02:00"
2025-05-29 15:51:25.691 +02:00 [INF] DISAdmin.Api.BackgroundServices.RealtimeMetricsBackgroundService: Realtime metrics background service is starting
2025-05-29 15:51:25.694 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[ClientCertificateThumbprint] IS NOT NULL AND [d].[CertificateExpirationDate] > GETUTCDATE() AND [d].[CertificateExpirationDate] < DATEADD(day, CAST(30.0E0 AS int), GETUTCDATE())
2025-05-29 15:51:25.706 +02:00 [INF] DISAdmin.Api.BackgroundServices.CertificateRotationBackgroundService: Služba pro automatickou rotaci certifikátů byla spuštěna
2025-05-29 15:51:25.708 +02:00 [INF] DISAdmin.Api.BackgroundServices.CertificateRotationBackgroundService: Spouštění kontroly expirujících certifikátů
2025-05-29 15:51:25.712 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked expiring certificates
2025-05-29 15:51:25.712 +02:00 [INF] DISAdmin.Core.Services.CertificateRotationService: Kontrola expirujících certifikátů
2025-05-29 15:51:25.742 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[FailedCertificateValidationCount] > 5
2025-05-29 15:51:25.796 +02:00 [WRN] Microsoft.EntityFrameworkCore.Query: The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-05-29 15:51:25.814 +02:00 [INF] DISAdmin.Api.BackgroundServices.AnomalyDetectionBackgroundService: Služba pro detekci anomálií byla spuštěna
2025-05-29 15:51:25.816 +02:00 [INF] DISAdmin.Api.BackgroundServices.AnomalyDetectionBackgroundService: Spouštění detekce anomálií
2025-05-29 15:51:25.821 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[EnableAutoRotation], [c].[LastUpdated], [c].[LastUpdatedBy], [c].[NewCertificateValidityDays], [c].[NotificationEmails], [c].[NotificationRepeatDays], [c].[NotifyDaysBeforeExpiration], [c].[RotateDaysBeforeExpiration], [c].[SendExpirationNotifications]
FROM [CertificateRotationSettings] AS [c]
2025-05-29 15:51:25.822 +02:00 [INF] DISAdmin.Core.Services.AnomalyDetectionService: Spuštění detekce anomálií v přístupech k API
2025-05-29 15:51:25.856 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status]
FROM [DISInstances] AS [d]
WHERE [d].[Status] = 0
2025-05-29 15:51:25.890 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[ClientCertificateThumbprint] IS NOT NULL AND [d].[CertificateExpirationDate] IS NOT NULL
2025-05-29 15:51:25.964 +02:00 [WRN] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: C:\Users\<USER>\Documents\VSCodeProjects\DISAdminAugment\DISAdmin.Web\wwwroot. Static files may be unavailable.
2025-05-29 15:51:26.013 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (7ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 15:51:26.019 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (60ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 15:51:26.023 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (16ms) [Parameters=[@__instance_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Alerts] AS [a]
        WHERE [a].[InstanceId] = @__instance_Id_0 AND [a].[AlertType] = 1 AND [a].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-05-29 15:51:26.038 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[@__instance_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Alerts] AS [a]
        WHERE [a].[InstanceId] = @__instance_Id_0 AND [a].[AlertType] = 1 AND [a].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-05-29 15:51:26.044 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked failed connection attempts
2025-05-29 15:51:26.045 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (15ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 15:51:26.062 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 15:51:26.065 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (11ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 15:51:26.069 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 15:51:26.078 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 15:51:26.080 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[Description], [s].[EventType], [s].[InstanceId], [s].[IpAddress], [s].[IsResolved], [s].[Resolution], [s].[ResolvedAt], [s].[ResolvedBy], [s].[Resource], [s].[Severity], [s].[Timestamp], [s].[Username]
FROM [SecurityEvents] AS [s]
WHERE [s].[EventType] = 1 AND [s].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE()) AND [s].[IsResolved] = CAST(0 AS bit)
2025-05-29 15:51:26.084 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 15:51:26.087 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked suspicious activities
2025-05-29 15:51:26.093 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 15:51:26.101 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 15:51:26.108 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 15:51:26.113 +02:00 [INF] DISAdmin.Api.BackgroundServices.CertificateRotationBackgroundService: Kontrola expirujících certifikátů dokončena
2025-05-29 15:51:26.114 +02:00 [WRN] Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServer: The ASP.NET Core developer certificate is not trusted. For information about trusting the ASP.NET Core developer certificate, see https://aka.ms/aspnet/https-trust-dev-cert
2025-05-29 15:51:26.167 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AlertType], [a].[Description], [a].[InstanceId], [a].[IsNotificationSent], [a].[IsResolved], [a].[Resolution], [a].[ResolvedAt], [a].[ResolvedBy], [a].[Severity], [a].[Timestamp]
FROM [Alerts] AS [a]
WHERE [a].[InstanceId] = @__8__locals1_instanceId_0 AND [a].[AlertType] = 6 AND [a].[Description] LIKE N'%mimo pracovní dobu%' AND [a].[Timestamp] >= DATEADD(day, CAST(-7.0E0 AS int), GETUTCDATE())
2025-05-29 15:51:26.169 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[InstanceId], AVG(CAST([p].[Avg] AS float)) AS [AvgResponseTime], AVG(CAST([p].[Max] AS float)) AS [MaxResponseTime], AVG(CAST([p].[Percentil95] AS float)) AS [P95ResponseTime]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())
GROUP BY [p].[InstanceId]
2025-05-29 15:51:26.177 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked method performance
2025-05-29 15:51:26.189 +02:00 [INF] Microsoft.Hosting.Lifetime: Now listening on: https://localhost:7029
2025-05-29 15:51:26.195 +02:00 [INF] Microsoft.Hosting.Lifetime: Now listening on: http://localhost:5167
2025-05-29 15:51:26.202 +02:00 [INF] Microsoft.Hosting.Lifetime: Application started. Press Ctrl+C to shut down.
2025-05-29 15:51:26.205 +02:00 [INF] Microsoft.Hosting.Lifetime: Hosting environment: Development
2025-05-29 15:51:26.205 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[InstanceId], COUNT(*) AS [CallsCount]
FROM [DiagnosticLogs] AS [d]
WHERE [d].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())
GROUP BY [d].[InstanceId]
2025-05-29 15:51:26.207 +02:00 [INF] Microsoft.Hosting.Lifetime: Content root path: C:\Users\<USER>\Documents\VSCodeProjects\DISAdminAugment\DISAdmin.Web
2025-05-29 15:51:26.214 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked API calls count
2025-05-29 15:51:26.231 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (10ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 15:51:26.242 +02:00 [INF] DISAdmin.Core.Services.AnomalyDetectionService: Spuštění detekce anomálií v přihlašování uživatelů
2025-05-29 15:51:26.253 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[InstanceId], COUNT(*) AS [TotalCalls], COUNT(CASE
    WHEN [d].[ResponseStatusCode] >= 400 THEN 1
END) AS [ErrorCalls]
FROM [DiagnosticLogs] AS [d]
WHERE [d].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())
GROUP BY [d].[InstanceId]
2025-05-29 15:51:26.259 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked error rate
2025-05-29 15:51:26.263 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[@__startDate_0='?' (DbType = DateTime2), @__endDate_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [l].[Id], [l].[IpAddress], [l].[Success], [l].[Timestamp], [l].[UserAgent], [l].[UserId], [l].[Username]
FROM [LoginAttempts] AS [l]
WHERE [l].[Timestamp] >= @__startDate_0 AND [l].[Timestamp] <= @__endDate_1
ORDER BY [l].[Timestamp]
2025-05-29 15:51:26.298 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[EnableIpWhitelisting] = CAST(1 AS bit)
2025-05-29 15:51:26.308 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked unauthorized IP access
2025-05-29 15:51:26.310 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Spuštění detekce anomálií ve výkonnostních metrikách
2025-05-29 15:51:26.313 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status]
FROM [DISInstances] AS [d]
WHERE [d].[Status] = 0
2025-05-29 15:51:26.339 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 15:51:26.344 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (16ms) [Parameters=[@__cutoffTime_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[IpAddress], COUNT(*) AS [Count]
FROM [ActivityLogs] AS [a]
WHERE [a].[ActivityType] = 0 AND [a].[Timestamp] > @__cutoffTime_0
GROUP BY [a].[IpAddress]
HAVING COUNT(*) > 10
2025-05-29 15:51:26.345 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Nedostatek dat pro analýzu výkonnostních metrik pro instanci 2
2025-05-29 15:51:26.354 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 15:51:26.358 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Nedostatek dat pro analýzu výkonnostních metrik pro instanci 3
2025-05-29 15:51:26.362 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 15:51:26.372 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (16ms) [Parameters=[@__cutoffTime_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[IpAddress], COUNT(*) AS [Count]
FROM [ActivityLogs] AS [a]
WHERE [a].[ActivityType] = 8 AND [a].[Timestamp] > @__cutoffTime_0
GROUP BY [a].[IpAddress]
2025-05-29 15:51:26.385 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[LastKnownIpAddress]
FROM [DISInstances] AS [d]
WHERE [d].[LastKnownIpAddress] IS NOT NULL
2025-05-29 15:51:26.410 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (8ms) [Parameters=[@__cutoffTime_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityName] = N'Certificate' AND [a].[Timestamp] > @__cutoffTime_0
2025-05-29 15:51:26.425 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 15:51:26.430 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Nedostatek dat pro analýzu výkonnostních metrik pro instanci 8
2025-05-29 15:51:26.432 +02:00 [INF] DISAdmin.Api.BackgroundServices.AnomalyDetectionBackgroundService: Detekce anomálií dokončena
2025-05-29 15:51:26.489 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Detected 0 suspicious events
2025-05-29 15:51:30.016 +02:00 [INF] Microsoft.AspNetCore.SpaProxy.SpaProxyLaunchManager: No SPA development server running at http://localhost:4200 found.
2025-05-29 15:52:33.545 +02:00 [INF] Microsoft.AspNetCore.SpaProxy.SpaProxyLaunchManager: SPA development server running at 'http://localhost:4200'
2025-05-29 15:57:24.719 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/users - application/json null
2025-05-29 15:57:24.719 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/filters/logs-activity - null null
2025-05-29 15:57:24.719 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/logs/activity?fromDate=2025-05-15T22:00:00.000Z&toDate=2025-05-23T21:59:59.999Z&maxResults=100&source=1 - null null
2025-05-29 15:57:24.865 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (7ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:57:24.865 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (7ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:57:24.865 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (7ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:57:24.945 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.LogsController.GetActivityLogs (DISAdmin.Api)'
2025-05-29 15:57:24.945 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:57:24.946 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:57:24.970 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetAllUsers", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers(System.String, System.String, System.String, System.String) on controller DISAdmin.Api.Controllers.UsersController (DISAdmin.Api).
2025-05-29 15:57:24.970 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetUserFilters", controller = "Filters"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[DISAdmin.Api.Controllers.FilterDto]]] GetUserFilters(System.String) on controller DISAdmin.Api.Controllers.FiltersController (DISAdmin.Api).
2025-05-29 15:57:24.970 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetActivityLogs", controller = "Logs"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetActivityLogs(DISAdmin.Api.Models.LogFilterRequest) on controller DISAdmin.Api.Controllers.LogsController (DISAdmin.Api).
2025-05-29 15:57:24.996 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsHidden] = CAST(0 AS bit)
2025-05-29 15:57:25.007 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[@__entityType_0='?' (Size = 100), @__userId_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[EntityType], [s].[FilterJson], [s].[IsDefault], [s].[IsShared], [s].[LastUsedAt], [s].[Name], [s].[UserId]
FROM [SavedFilters] AS [s]
WHERE [s].[EntityType] = @__entityType_0 AND ([s].[UserId] = @__userId_1 OR [s].[IsShared] = CAST(1 AS bit))
ORDER BY [s].[IsDefault] DESC, [s].[LastUsedAt] DESC, [s].[Name]
2025-05-29 15:57:25.019 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[DISAdmin.Api.Controllers.FilterDto, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:57:25.039 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.User, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.UserResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:57:25.064 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api) in 88.0118ms
2025-05-29 15:57:25.064 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api) in 82.3963ms
2025-05-29 15:57:25.067 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:57:25.070 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:57:25.083 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/filters/logs-activity - 200 null application/json; charset=utf-8 366.0181ms
2025-05-29 15:57:25.083 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/users - 200 null application/json; charset=utf-8 366.0182ms
2025-05-29 15:57:25.104 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (85ms) [Parameters=[@__p_3='?' (DbType = Int32), @__fromDate_0='?' (DbType = DateTime2), @__toDate_1='?' (DbType = DateTime2), @__source_Value_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Id], [a0].[ActivityType], [a0].[AdditionalInfo], [a0].[Description], [a0].[Endpoint], [a0].[EntityId], [a0].[EntityName], [a0].[IpAddress], [a0].[Method], [a0].[ResponseTimeMs], [a0].[Source], [a0].[StatusCode], [a0].[Timestamp], [a0].[UserId], [a0].[Username], [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM (
    SELECT TOP(@__p_3) [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
    FROM [ActivityLogs] AS [a]
    WHERE [a].[Timestamp] >= @__fromDate_0 AND [a].[Timestamp] <= @__toDate_1 AND [a].[Source] = @__source_Value_2
    ORDER BY [a].[Timestamp] DESC
) AS [a0]
LEFT JOIN [Users] AS [u] ON [a0].[UserId] = [u].[Id]
ORDER BY [a0].[Timestamp] DESC
2025-05-29 15:57:25.118 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.ActivityLog, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.ActivityLogResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:57:25.128 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.LogsController.GetActivityLogs (DISAdmin.Api) in 146.883ms
2025-05-29 15:57:25.131 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.LogsController.GetActivityLogs (DISAdmin.Api)'
2025-05-29 15:57:25.134 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/logs/activity?fromDate=2025-05-15T22:00:00.000Z&toDate=2025-05-23T21:59:59.999Z&maxResults=100&source=1 - 200 null application/json; charset=utf-8 417.1205ms
2025-05-29 15:57:32.949 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/filters/logs-error - null null
2025-05-29 15:57:32.949 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/users - application/json null
2025-05-29 15:57:32.968 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:57:32.971 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:57:32.979 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:57:32.979 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:57:32.981 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetUserFilters", controller = "Filters"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[DISAdmin.Api.Controllers.FilterDto]]] GetUserFilters(System.String) on controller DISAdmin.Api.Controllers.FiltersController (DISAdmin.Api).
2025-05-29 15:57:32.984 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetAllUsers", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers(System.String, System.String, System.String, System.String) on controller DISAdmin.Api.Controllers.UsersController (DISAdmin.Api).
2025-05-29 15:57:32.994 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__entityType_0='?' (Size = 100), @__userId_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[EntityType], [s].[FilterJson], [s].[IsDefault], [s].[IsShared], [s].[LastUsedAt], [s].[Name], [s].[UserId]
FROM [SavedFilters] AS [s]
WHERE [s].[EntityType] = @__entityType_0 AND ([s].[UserId] = @__userId_1 OR [s].[IsShared] = CAST(1 AS bit))
ORDER BY [s].[IsDefault] DESC, [s].[LastUsedAt] DESC, [s].[Name]
2025-05-29 15:57:32.995 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsHidden] = CAST(0 AS bit)
2025-05-29 15:57:32.999 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[DISAdmin.Api.Controllers.FilterDto, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:57:33.003 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.User, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.UserResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:57:33.005 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api) in 16.7281ms
2025-05-29 15:57:33.007 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api) in 15.716ms
2025-05-29 15:57:33.010 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:57:33.012 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:57:33.015 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/filters/logs-error - 200 null application/json; charset=utf-8 66.1841ms
2025-05-29 15:57:33.017 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/users - 200 null application/json; charset=utf-8 68.2068ms
2025-05-29 15:57:33.056 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/logs/errors?fromDate=2025-05-15T22:00:00.000Z&toDate=2025-05-23T21:59:59.999Z&maxResults=100 - null null
2025-05-29 15:57:33.064 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:57:33.069 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api)'
2025-05-29 15:57:33.078 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetErrorLogs", controller = "Logs"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetErrorLogs(DISAdmin.Api.Models.LogFilterRequest) on controller DISAdmin.Api.Controllers.LogsController (DISAdmin.Api).
2025-05-29 15:57:33.130 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (15ms) [Parameters=[@__p_2='?' (DbType = Int32), @__fromDate_0='?' (DbType = DateTime2), @__toDate_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [e0].[Id], [e0].[AdditionalInfo], [e0].[Category], [e0].[IpAddress], [e0].[LogLevel], [e0].[Message], [e0].[RequestMethod], [e0].[RequestPath], [e0].[Source], [e0].[StackTrace], [e0].[StatusCode], [e0].[Timestamp], [e0].[UserId], [e0].[Username], [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM (
    SELECT TOP(@__p_2) [e].[Id], [e].[AdditionalInfo], [e].[Category], [e].[IpAddress], [e].[LogLevel], [e].[Message], [e].[RequestMethod], [e].[RequestPath], [e].[Source], [e].[StackTrace], [e].[StatusCode], [e].[Timestamp], [e].[UserId], [e].[Username]
    FROM [ErrorLogs] AS [e]
    WHERE [e].[Timestamp] >= @__fromDate_0 AND [e].[Timestamp] <= @__toDate_1
    ORDER BY [e].[Timestamp] DESC
) AS [e0]
LEFT JOIN [Users] AS [u] ON [e0].[UserId] = [u].[Id]
ORDER BY [e0].[Timestamp] DESC
2025-05-29 15:57:33.207 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.ErrorLog, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.ErrorLogResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:57:33.219 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api) in 137.5711ms
2025-05-29 15:57:33.222 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api)'
2025-05-29 15:57:33.227 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/logs/errors?fromDate=2025-05-15T22:00:00.000Z&toDate=2025-05-23T21:59:59.999Z&maxResults=100 - 200 null application/json; charset=utf-8 171.2967ms
2025-05-29 15:58:14.715 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/users - application/json null
2025-05-29 15:58:14.716 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/filters/logs-api - null null
2025-05-29 15:58:14.722 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/logs/activity?fromDate=2025-05-15T22:00:00.000Z&toDate=2025-05-23T21:59:59.999Z&maxResults=100&source=2 - null null
2025-05-29 15:58:14.730 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (7ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:58:14.731 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:58:14.734 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:58:14.738 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:58:14.743 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:58:14.748 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.LogsController.GetActivityLogs (DISAdmin.Api)'
2025-05-29 15:58:14.749 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetAllUsers", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers(System.String, System.String, System.String, System.String) on controller DISAdmin.Api.Controllers.UsersController (DISAdmin.Api).
2025-05-29 15:58:14.752 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetUserFilters", controller = "Filters"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[DISAdmin.Api.Controllers.FilterDto]]] GetUserFilters(System.String) on controller DISAdmin.Api.Controllers.FiltersController (DISAdmin.Api).
2025-05-29 15:58:14.755 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetActivityLogs", controller = "Logs"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetActivityLogs(DISAdmin.Api.Models.LogFilterRequest) on controller DISAdmin.Api.Controllers.LogsController (DISAdmin.Api).
2025-05-29 15:58:14.765 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsHidden] = CAST(0 AS bit)
2025-05-29 15:58:14.769 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[@__entityType_0='?' (Size = 100), @__userId_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[EntityType], [s].[FilterJson], [s].[IsDefault], [s].[IsShared], [s].[LastUsedAt], [s].[Name], [s].[UserId]
FROM [SavedFilters] AS [s]
WHERE [s].[EntityType] = @__entityType_0 AND ([s].[UserId] = @__userId_1 OR [s].[IsShared] = CAST(1 AS bit))
ORDER BY [s].[IsDefault] DESC, [s].[LastUsedAt] DESC, [s].[Name]
2025-05-29 15:58:14.773 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.User, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.UserResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:58:14.777 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[DISAdmin.Api.Controllers.FilterDto, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:58:14.779 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api) in 19.9365ms
2025-05-29 15:58:14.781 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api) in 17.9759ms
2025-05-29 15:58:14.783 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:58:14.786 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:58:14.788 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/users - 200 null application/json; charset=utf-8 72.8555ms
2025-05-29 15:58:14.791 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/filters/logs-api - 200 null application/json; charset=utf-8 74.6995ms
2025-05-29 15:58:14.883 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (112ms) [Parameters=[@__p_3='?' (DbType = Int32), @__fromDate_0='?' (DbType = DateTime2), @__toDate_1='?' (DbType = DateTime2), @__source_Value_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Id], [a0].[ActivityType], [a0].[AdditionalInfo], [a0].[Description], [a0].[Endpoint], [a0].[EntityId], [a0].[EntityName], [a0].[IpAddress], [a0].[Method], [a0].[ResponseTimeMs], [a0].[Source], [a0].[StatusCode], [a0].[Timestamp], [a0].[UserId], [a0].[Username], [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM (
    SELECT TOP(@__p_3) [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
    FROM [ActivityLogs] AS [a]
    WHERE [a].[Timestamp] >= @__fromDate_0 AND [a].[Timestamp] <= @__toDate_1 AND [a].[Source] = @__source_Value_2
    ORDER BY [a].[Timestamp] DESC
) AS [a0]
LEFT JOIN [Users] AS [u] ON [a0].[UserId] = [u].[Id]
ORDER BY [a0].[Timestamp] DESC
2025-05-29 15:58:14.888 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.ActivityLog, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.ActivityLogResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:58:14.891 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.LogsController.GetActivityLogs (DISAdmin.Api) in 124.1267ms
2025-05-29 15:58:14.893 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.LogsController.GetActivityLogs (DISAdmin.Api)'
2025-05-29 15:58:14.895 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/logs/activity?fromDate=2025-05-15T22:00:00.000Z&toDate=2025-05-23T21:59:59.999Z&maxResults=100&source=2 - 200 null application/json; charset=utf-8 173.0579ms
2025-05-29 15:58:16.198 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/users - application/json null
2025-05-29 15:58:16.199 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/filters/logs-error - null null
2025-05-29 15:58:16.208 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:58:16.209 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (0ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:58:16.212 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:58:16.216 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:58:16.217 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetAllUsers", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers(System.String, System.String, System.String, System.String) on controller DISAdmin.Api.Controllers.UsersController (DISAdmin.Api).
2025-05-29 15:58:16.220 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetUserFilters", controller = "Filters"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[DISAdmin.Api.Controllers.FilterDto]]] GetUserFilters(System.String) on controller DISAdmin.Api.Controllers.FiltersController (DISAdmin.Api).
2025-05-29 15:58:16.226 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsHidden] = CAST(0 AS bit)
2025-05-29 15:58:16.230 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.User, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.UserResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:58:16.230 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[@__entityType_0='?' (Size = 100), @__userId_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[EntityType], [s].[FilterJson], [s].[IsDefault], [s].[IsShared], [s].[LastUsedAt], [s].[Name], [s].[UserId]
FROM [SavedFilters] AS [s]
WHERE [s].[EntityType] = @__entityType_0 AND ([s].[UserId] = @__userId_1 OR [s].[IsShared] = CAST(1 AS bit))
ORDER BY [s].[IsDefault] DESC, [s].[LastUsedAt] DESC, [s].[Name]
2025-05-29 15:58:16.233 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api) in 9.2856ms
2025-05-29 15:58:16.236 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[DISAdmin.Api.Controllers.FilterDto, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:58:16.238 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:58:16.241 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api) in 13.8108ms
2025-05-29 15:58:16.242 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/users - 200 null application/json; charset=utf-8 44.0074ms
2025-05-29 15:58:16.244 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:58:16.251 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/filters/logs-error - 200 null application/json; charset=utf-8 51.8067ms
2025-05-29 15:58:16.259 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/logs/errors?fromDate=2025-05-15T22:00:00.000Z&toDate=2025-05-23T21:59:59.999Z&maxResults=100 - null null
2025-05-29 15:58:16.265 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:58:16.269 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api)'
2025-05-29 15:58:16.271 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetErrorLogs", controller = "Logs"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetErrorLogs(DISAdmin.Api.Models.LogFilterRequest) on controller DISAdmin.Api.Controllers.LogsController (DISAdmin.Api).
2025-05-29 15:58:16.286 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (11ms) [Parameters=[@__p_2='?' (DbType = Int32), @__fromDate_0='?' (DbType = DateTime2), @__toDate_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [e0].[Id], [e0].[AdditionalInfo], [e0].[Category], [e0].[IpAddress], [e0].[LogLevel], [e0].[Message], [e0].[RequestMethod], [e0].[RequestPath], [e0].[Source], [e0].[StackTrace], [e0].[StatusCode], [e0].[Timestamp], [e0].[UserId], [e0].[Username], [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM (
    SELECT TOP(@__p_2) [e].[Id], [e].[AdditionalInfo], [e].[Category], [e].[IpAddress], [e].[LogLevel], [e].[Message], [e].[RequestMethod], [e].[RequestPath], [e].[Source], [e].[StackTrace], [e].[StatusCode], [e].[Timestamp], [e].[UserId], [e].[Username]
    FROM [ErrorLogs] AS [e]
    WHERE [e].[Timestamp] >= @__fromDate_0 AND [e].[Timestamp] <= @__toDate_1
    ORDER BY [e].[Timestamp] DESC
) AS [e0]
LEFT JOIN [Users] AS [u] ON [e0].[UserId] = [u].[Id]
ORDER BY [e0].[Timestamp] DESC
2025-05-29 15:58:16.336 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.ErrorLog, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.ErrorLogResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:58:16.340 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api) in 65.5218ms
2025-05-29 15:58:16.342 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api)'
2025-05-29 15:58:16.350 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/logs/errors?fromDate=2025-05-15T22:00:00.000Z&toDate=2025-05-23T21:59:59.999Z&maxResults=100 - 200 null application/json; charset=utf-8 91.2861ms
2025-05-29 15:58:30.703 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/logs/errors?fromDate=2025-05-21T22:00:00.000Z&toDate=2025-05-29T21:59:59.999Z&maxResults=100 - null null
2025-05-29 15:58:30.710 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:58:30.715 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api)'
2025-05-29 15:58:30.718 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetErrorLogs", controller = "Logs"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetErrorLogs(DISAdmin.Api.Models.LogFilterRequest) on controller DISAdmin.Api.Controllers.LogsController (DISAdmin.Api).
2025-05-29 15:58:30.725 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__p_2='?' (DbType = Int32), @__fromDate_0='?' (DbType = DateTime2), @__toDate_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [e0].[Id], [e0].[AdditionalInfo], [e0].[Category], [e0].[IpAddress], [e0].[LogLevel], [e0].[Message], [e0].[RequestMethod], [e0].[RequestPath], [e0].[Source], [e0].[StackTrace], [e0].[StatusCode], [e0].[Timestamp], [e0].[UserId], [e0].[Username], [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM (
    SELECT TOP(@__p_2) [e].[Id], [e].[AdditionalInfo], [e].[Category], [e].[IpAddress], [e].[LogLevel], [e].[Message], [e].[RequestMethod], [e].[RequestPath], [e].[Source], [e].[StackTrace], [e].[StatusCode], [e].[Timestamp], [e].[UserId], [e].[Username]
    FROM [ErrorLogs] AS [e]
    WHERE [e].[Timestamp] >= @__fromDate_0 AND [e].[Timestamp] <= @__toDate_1
    ORDER BY [e].[Timestamp] DESC
) AS [e0]
LEFT JOIN [Users] AS [u] ON [e0].[UserId] = [u].[Id]
ORDER BY [e0].[Timestamp] DESC
2025-05-29 15:58:30.731 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.ErrorLog, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.ErrorLogResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:58:30.735 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api) in 12.4269ms
2025-05-29 15:58:30.737 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api)'
2025-05-29 15:58:30.740 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/logs/errors?fromDate=2025-05-21T22:00:00.000Z&toDate=2025-05-29T21:59:59.999Z&maxResults=100 - 200 null application/json; charset=utf-8 36.6747ms
2025-05-29 15:58:33.250 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/logs/errors?fromDate=2025-05-21T22:00:00.000Z&toDate=2025-05-29T21:59:59.999Z&maxResults=100 - null null
2025-05-29 15:58:33.257 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:58:33.261 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api)'
2025-05-29 15:58:33.263 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetErrorLogs", controller = "Logs"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetErrorLogs(DISAdmin.Api.Models.LogFilterRequest) on controller DISAdmin.Api.Controllers.LogsController (DISAdmin.Api).
2025-05-29 15:58:33.269 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__p_2='?' (DbType = Int32), @__fromDate_0='?' (DbType = DateTime2), @__toDate_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [e0].[Id], [e0].[AdditionalInfo], [e0].[Category], [e0].[IpAddress], [e0].[LogLevel], [e0].[Message], [e0].[RequestMethod], [e0].[RequestPath], [e0].[Source], [e0].[StackTrace], [e0].[StatusCode], [e0].[Timestamp], [e0].[UserId], [e0].[Username], [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM (
    SELECT TOP(@__p_2) [e].[Id], [e].[AdditionalInfo], [e].[Category], [e].[IpAddress], [e].[LogLevel], [e].[Message], [e].[RequestMethod], [e].[RequestPath], [e].[Source], [e].[StackTrace], [e].[StatusCode], [e].[Timestamp], [e].[UserId], [e].[Username]
    FROM [ErrorLogs] AS [e]
    WHERE [e].[Timestamp] >= @__fromDate_0 AND [e].[Timestamp] <= @__toDate_1
    ORDER BY [e].[Timestamp] DESC
) AS [e0]
LEFT JOIN [Users] AS [u] ON [e0].[UserId] = [u].[Id]
ORDER BY [e0].[Timestamp] DESC
2025-05-29 15:58:33.274 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.ErrorLog, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.ErrorLogResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:58:33.276 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api) in 10.0095ms
2025-05-29 15:58:33.279 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api)'
2025-05-29 15:58:33.281 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/logs/errors?fromDate=2025-05-21T22:00:00.000Z&toDate=2025-05-29T21:59:59.999Z&maxResults=100 - 200 null application/json; charset=utf-8 31.6313ms
2025-05-29 15:58:35.718 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/logs/errors?fromDate=2025-05-21T22:00:00.000Z&toDate=2025-05-29T21:59:59.999Z&maxResults=100 - null null
2025-05-29 15:58:35.724 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:58:35.728 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api)'
2025-05-29 15:58:35.730 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetErrorLogs", controller = "Logs"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetErrorLogs(DISAdmin.Api.Models.LogFilterRequest) on controller DISAdmin.Api.Controllers.LogsController (DISAdmin.Api).
2025-05-29 15:58:35.736 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__p_2='?' (DbType = Int32), @__fromDate_0='?' (DbType = DateTime2), @__toDate_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [e0].[Id], [e0].[AdditionalInfo], [e0].[Category], [e0].[IpAddress], [e0].[LogLevel], [e0].[Message], [e0].[RequestMethod], [e0].[RequestPath], [e0].[Source], [e0].[StackTrace], [e0].[StatusCode], [e0].[Timestamp], [e0].[UserId], [e0].[Username], [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM (
    SELECT TOP(@__p_2) [e].[Id], [e].[AdditionalInfo], [e].[Category], [e].[IpAddress], [e].[LogLevel], [e].[Message], [e].[RequestMethod], [e].[RequestPath], [e].[Source], [e].[StackTrace], [e].[StatusCode], [e].[Timestamp], [e].[UserId], [e].[Username]
    FROM [ErrorLogs] AS [e]
    WHERE [e].[Timestamp] >= @__fromDate_0 AND [e].[Timestamp] <= @__toDate_1
    ORDER BY [e].[Timestamp] DESC
) AS [e0]
LEFT JOIN [Users] AS [u] ON [e0].[UserId] = [u].[Id]
ORDER BY [e0].[Timestamp] DESC
2025-05-29 15:58:35.742 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.ErrorLog, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.ErrorLogResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:58:35.745 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api) in 11.3033ms
2025-05-29 15:58:35.748 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api)'
2025-05-29 15:58:35.750 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/logs/errors?fromDate=2025-05-21T22:00:00.000Z&toDate=2025-05-29T21:59:59.999Z&maxResults=100 - 200 null application/json; charset=utf-8 32.4083ms
2025-05-29 15:59:00.363 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/users - application/json null
2025-05-29 15:59:00.365 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/logs/activity?fromDate=2025-05-15T22:00:00.000Z&toDate=2025-05-23T21:59:59.999Z&maxResults=100&source=1 - null null
2025-05-29 15:59:00.366 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/filters/logs-activity - null null
2025-05-29 15:59:00.376 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:00.377 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:00.382 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:00.386 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:59:00.391 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.LogsController.GetActivityLogs (DISAdmin.Api)'
2025-05-29 15:59:00.396 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:59:00.397 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetAllUsers", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers(System.String, System.String, System.String, System.String) on controller DISAdmin.Api.Controllers.UsersController (DISAdmin.Api).
2025-05-29 15:59:00.399 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetActivityLogs", controller = "Logs"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetActivityLogs(DISAdmin.Api.Models.LogFilterRequest) on controller DISAdmin.Api.Controllers.LogsController (DISAdmin.Api).
2025-05-29 15:59:00.401 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetUserFilters", controller = "Filters"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[DISAdmin.Api.Controllers.FilterDto]]] GetUserFilters(System.String) on controller DISAdmin.Api.Controllers.FiltersController (DISAdmin.Api).
2025-05-29 15:59:00.408 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsHidden] = CAST(0 AS bit)
2025-05-29 15:59:00.417 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.User, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.UserResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:00.418 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[@__entityType_0='?' (Size = 100), @__userId_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[EntityType], [s].[FilterJson], [s].[IsDefault], [s].[IsShared], [s].[LastUsedAt], [s].[Name], [s].[UserId]
FROM [SavedFilters] AS [s]
WHERE [s].[EntityType] = @__entityType_0 AND ([s].[UserId] = @__userId_1 OR [s].[IsShared] = CAST(1 AS bit))
ORDER BY [s].[IsDefault] DESC, [s].[LastUsedAt] DESC, [s].[Name]
2025-05-29 15:59:00.420 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api) in 15.0377ms
2025-05-29 15:59:00.424 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[DISAdmin.Api.Controllers.FilterDto, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:00.426 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:59:00.428 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api) in 15.8191ms
2025-05-29 15:59:00.430 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/users - 200 null application/json; charset=utf-8 66.6669ms
2025-05-29 15:59:00.432 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:59:00.440 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/filters/logs-activity - 200 null application/json; charset=utf-8 73.5605ms
2025-05-29 15:59:00.499 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (89ms) [Parameters=[@__p_3='?' (DbType = Int32), @__fromDate_0='?' (DbType = DateTime2), @__toDate_1='?' (DbType = DateTime2), @__source_Value_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Id], [a0].[ActivityType], [a0].[AdditionalInfo], [a0].[Description], [a0].[Endpoint], [a0].[EntityId], [a0].[EntityName], [a0].[IpAddress], [a0].[Method], [a0].[ResponseTimeMs], [a0].[Source], [a0].[StatusCode], [a0].[Timestamp], [a0].[UserId], [a0].[Username], [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM (
    SELECT TOP(@__p_3) [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
    FROM [ActivityLogs] AS [a]
    WHERE [a].[Timestamp] >= @__fromDate_0 AND [a].[Timestamp] <= @__toDate_1 AND [a].[Source] = @__source_Value_2
    ORDER BY [a].[Timestamp] DESC
) AS [a0]
LEFT JOIN [Users] AS [u] ON [a0].[UserId] = [u].[Id]
ORDER BY [a0].[Timestamp] DESC
2025-05-29 15:59:00.506 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.ActivityLog, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.ActivityLogResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:00.509 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.LogsController.GetActivityLogs (DISAdmin.Api) in 100.4005ms
2025-05-29 15:59:00.511 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.LogsController.GetActivityLogs (DISAdmin.Api)'
2025-05-29 15:59:00.513 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/logs/activity?fromDate=2025-05-15T22:00:00.000Z&toDate=2025-05-23T21:59:59.999Z&maxResults=100&source=1 - 200 null application/json; charset=utf-8 148.3112ms
2025-05-29 15:59:01.816 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/users - application/json null
2025-05-29 15:59:01.817 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/filters/logs-error - null null
2025-05-29 15:59:01.824 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:01.827 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:01.831 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:59:01.835 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:59:01.837 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetAllUsers", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers(System.String, System.String, System.String, System.String) on controller DISAdmin.Api.Controllers.UsersController (DISAdmin.Api).
2025-05-29 15:59:01.839 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetUserFilters", controller = "Filters"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[DISAdmin.Api.Controllers.FilterDto]]] GetUserFilters(System.String) on controller DISAdmin.Api.Controllers.FiltersController (DISAdmin.Api).
2025-05-29 15:59:01.844 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsHidden] = CAST(0 AS bit)
2025-05-29 15:59:01.848 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__entityType_0='?' (Size = 100), @__userId_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[EntityType], [s].[FilterJson], [s].[IsDefault], [s].[IsShared], [s].[LastUsedAt], [s].[Name], [s].[UserId]
FROM [SavedFilters] AS [s]
WHERE [s].[EntityType] = @__entityType_0 AND ([s].[UserId] = @__userId_1 OR [s].[IsShared] = CAST(1 AS bit))
ORDER BY [s].[IsDefault] DESC, [s].[LastUsedAt] DESC, [s].[Name]
2025-05-29 15:59:01.852 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.User, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.UserResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:01.857 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[DISAdmin.Api.Controllers.FilterDto, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:01.859 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api) in 16.7883ms
2025-05-29 15:59:01.862 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api) in 15.2622ms
2025-05-29 15:59:01.864 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:59:01.867 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:59:01.869 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/users - 200 null application/json; charset=utf-8 52.7604ms
2025-05-29 15:59:01.871 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/filters/logs-error - 200 null application/json; charset=utf-8 53.532ms
2025-05-29 15:59:01.883 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/logs/errors?fromDate=2025-05-21T22:00:00.000Z&toDate=2025-05-29T21:59:59.999Z&maxResults=100 - null null
2025-05-29 15:59:01.889 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:01.894 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api)'
2025-05-29 15:59:01.896 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetErrorLogs", controller = "Logs"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetErrorLogs(DISAdmin.Api.Models.LogFilterRequest) on controller DISAdmin.Api.Controllers.LogsController (DISAdmin.Api).
2025-05-29 15:59:01.905 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[@__p_2='?' (DbType = Int32), @__fromDate_0='?' (DbType = DateTime2), @__toDate_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [e0].[Id], [e0].[AdditionalInfo], [e0].[Category], [e0].[IpAddress], [e0].[LogLevel], [e0].[Message], [e0].[RequestMethod], [e0].[RequestPath], [e0].[Source], [e0].[StackTrace], [e0].[StatusCode], [e0].[Timestamp], [e0].[UserId], [e0].[Username], [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM (
    SELECT TOP(@__p_2) [e].[Id], [e].[AdditionalInfo], [e].[Category], [e].[IpAddress], [e].[LogLevel], [e].[Message], [e].[RequestMethod], [e].[RequestPath], [e].[Source], [e].[StackTrace], [e].[StatusCode], [e].[Timestamp], [e].[UserId], [e].[Username]
    FROM [ErrorLogs] AS [e]
    WHERE [e].[Timestamp] >= @__fromDate_0 AND [e].[Timestamp] <= @__toDate_1
    ORDER BY [e].[Timestamp] DESC
) AS [e0]
LEFT JOIN [Users] AS [u] ON [e0].[UserId] = [u].[Id]
ORDER BY [e0].[Timestamp] DESC
2025-05-29 15:59:01.911 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.ErrorLog, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.ErrorLogResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:01.914 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api) in 14.9099ms
2025-05-29 15:59:01.916 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api)'
2025-05-29 15:59:01.918 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/logs/errors?fromDate=2025-05-21T22:00:00.000Z&toDate=2025-05-29T21:59:59.999Z&maxResults=100 - 200 null application/json; charset=utf-8 35.9009ms
2025-05-29 15:59:02.502 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/users - application/json null
2025-05-29 15:59:02.504 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/filters/logs-api - null null
2025-05-29 15:59:02.505 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/logs/activity?fromDate=2025-05-15T22:00:00.000Z&toDate=2025-05-23T21:59:59.999Z&maxResults=100&source=2 - null null
2025-05-29 15:59:02.509 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:02.513 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (0ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:02.518 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:02.520 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:59:02.524 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:59:02.528 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.LogsController.GetActivityLogs (DISAdmin.Api)'
2025-05-29 15:59:02.529 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetAllUsers", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers(System.String, System.String, System.String, System.String) on controller DISAdmin.Api.Controllers.UsersController (DISAdmin.Api).
2025-05-29 15:59:02.531 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetUserFilters", controller = "Filters"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[DISAdmin.Api.Controllers.FilterDto]]] GetUserFilters(System.String) on controller DISAdmin.Api.Controllers.FiltersController (DISAdmin.Api).
2025-05-29 15:59:02.533 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetActivityLogs", controller = "Logs"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetActivityLogs(DISAdmin.Api.Models.LogFilterRequest) on controller DISAdmin.Api.Controllers.LogsController (DISAdmin.Api).
2025-05-29 15:59:02.537 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsHidden] = CAST(0 AS bit)
2025-05-29 15:59:02.541 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__entityType_0='?' (Size = 100), @__userId_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[EntityType], [s].[FilterJson], [s].[IsDefault], [s].[IsShared], [s].[LastUsedAt], [s].[Name], [s].[UserId]
FROM [SavedFilters] AS [s]
WHERE [s].[EntityType] = @__entityType_0 AND ([s].[UserId] = @__userId_1 OR [s].[IsShared] = CAST(1 AS bit))
ORDER BY [s].[IsDefault] DESC, [s].[LastUsedAt] DESC, [s].[Name]
2025-05-29 15:59:02.546 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.User, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.UserResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:02.550 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[DISAdmin.Api.Controllers.FilterDto, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:02.551 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api) in 15.8205ms
2025-05-29 15:59:02.553 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api) in 14.256ms
2025-05-29 15:59:02.555 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:59:02.557 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:59:02.560 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/users - 200 null application/json; charset=utf-8 57.253ms
2025-05-29 15:59:02.561 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/filters/logs-api - 200 null application/json; charset=utf-8 57.6861ms
2025-05-29 15:59:02.610 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (67ms) [Parameters=[@__p_3='?' (DbType = Int32), @__fromDate_0='?' (DbType = DateTime2), @__toDate_1='?' (DbType = DateTime2), @__source_Value_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Id], [a0].[ActivityType], [a0].[AdditionalInfo], [a0].[Description], [a0].[Endpoint], [a0].[EntityId], [a0].[EntityName], [a0].[IpAddress], [a0].[Method], [a0].[ResponseTimeMs], [a0].[Source], [a0].[StatusCode], [a0].[Timestamp], [a0].[UserId], [a0].[Username], [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM (
    SELECT TOP(@__p_3) [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
    FROM [ActivityLogs] AS [a]
    WHERE [a].[Timestamp] >= @__fromDate_0 AND [a].[Timestamp] <= @__toDate_1 AND [a].[Source] = @__source_Value_2
    ORDER BY [a].[Timestamp] DESC
) AS [a0]
LEFT JOIN [Users] AS [u] ON [a0].[UserId] = [u].[Id]
ORDER BY [a0].[Timestamp] DESC
2025-05-29 15:59:02.615 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.ActivityLog, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.ActivityLogResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:02.617 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.LogsController.GetActivityLogs (DISAdmin.Api) in 74.7769ms
2025-05-29 15:59:02.619 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.LogsController.GetActivityLogs (DISAdmin.Api)'
2025-05-29 15:59:02.621 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/logs/activity?fromDate=2025-05-15T22:00:00.000Z&toDate=2025-05-23T21:59:59.999Z&maxResults=100&source=2 - 200 null application/json; charset=utf-8 116.2243ms
2025-05-29 15:59:05.080 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/filters/logs-error - null null
2025-05-29 15:59:05.080 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/users - application/json null
2025-05-29 15:59:05.085 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (0ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:05.089 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:05.093 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:59:05.096 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:59:05.097 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetUserFilters", controller = "Filters"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[DISAdmin.Api.Controllers.FilterDto]]] GetUserFilters(System.String) on controller DISAdmin.Api.Controllers.FiltersController (DISAdmin.Api).
2025-05-29 15:59:05.099 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetAllUsers", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers(System.String, System.String, System.String, System.String) on controller DISAdmin.Api.Controllers.UsersController (DISAdmin.Api).
2025-05-29 15:59:05.103 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__entityType_0='?' (Size = 100), @__userId_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[EntityType], [s].[FilterJson], [s].[IsDefault], [s].[IsShared], [s].[LastUsedAt], [s].[Name], [s].[UserId]
FROM [SavedFilters] AS [s]
WHERE [s].[EntityType] = @__entityType_0 AND ([s].[UserId] = @__userId_1 OR [s].[IsShared] = CAST(1 AS bit))
ORDER BY [s].[IsDefault] DESC, [s].[LastUsedAt] DESC, [s].[Name]
2025-05-29 15:59:05.106 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsHidden] = CAST(0 AS bit)
2025-05-29 15:59:05.109 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[DISAdmin.Api.Controllers.FilterDto, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:05.113 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.User, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.UserResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:05.115 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api) in 13.0943ms
2025-05-29 15:59:05.117 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api) in 12.4265ms
2025-05-29 15:59:05.119 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:59:05.122 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:59:05.124 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/users - 200 null application/json; charset=utf-8 43.3247ms
2025-05-29 15:59:05.126 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/filters/logs-error - 200 null application/json; charset=utf-8 46.0431ms
2025-05-29 15:59:05.137 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/logs/errors?fromDate=2025-05-21T22:00:00.000Z&toDate=2025-05-29T21:59:59.999Z&maxResults=100 - null null
2025-05-29 15:59:05.142 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (0ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:05.147 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api)'
2025-05-29 15:59:05.149 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetErrorLogs", controller = "Logs"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetErrorLogs(DISAdmin.Api.Models.LogFilterRequest) on controller DISAdmin.Api.Controllers.LogsController (DISAdmin.Api).
2025-05-29 15:59:05.154 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__p_2='?' (DbType = Int32), @__fromDate_0='?' (DbType = DateTime2), @__toDate_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [e0].[Id], [e0].[AdditionalInfo], [e0].[Category], [e0].[IpAddress], [e0].[LogLevel], [e0].[Message], [e0].[RequestMethod], [e0].[RequestPath], [e0].[Source], [e0].[StackTrace], [e0].[StatusCode], [e0].[Timestamp], [e0].[UserId], [e0].[Username], [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM (
    SELECT TOP(@__p_2) [e].[Id], [e].[AdditionalInfo], [e].[Category], [e].[IpAddress], [e].[LogLevel], [e].[Message], [e].[RequestMethod], [e].[RequestPath], [e].[Source], [e].[StackTrace], [e].[StatusCode], [e].[Timestamp], [e].[UserId], [e].[Username]
    FROM [ErrorLogs] AS [e]
    WHERE [e].[Timestamp] >= @__fromDate_0 AND [e].[Timestamp] <= @__toDate_1
    ORDER BY [e].[Timestamp] DESC
) AS [e0]
LEFT JOIN [Users] AS [u] ON [e0].[UserId] = [u].[Id]
ORDER BY [e0].[Timestamp] DESC
2025-05-29 15:59:05.160 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.ErrorLog, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.ErrorLogResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:05.162 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api) in 9.9056ms
2025-05-29 15:59:05.164 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api)'
2025-05-29 15:59:05.167 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/logs/errors?fromDate=2025-05-21T22:00:00.000Z&toDate=2025-05-29T21:59:59.999Z&maxResults=100 - 200 null application/json; charset=utf-8 30.0486ms
2025-05-29 15:59:13.402 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/users - application/json null
2025-05-29 15:59:13.404 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/filters/logs-activity - null null
2025-05-29 15:59:13.407 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/logs/activity?fromDate=2025-05-15T22:00:00.000Z&toDate=2025-05-23T21:59:59.999Z&maxResults=100&source=1 - null null
2025-05-29 15:59:13.409 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:13.414 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:13.419 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:13.422 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:59:13.427 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:59:13.432 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.LogsController.GetActivityLogs (DISAdmin.Api)'
2025-05-29 15:59:13.433 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetAllUsers", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers(System.String, System.String, System.String, System.String) on controller DISAdmin.Api.Controllers.UsersController (DISAdmin.Api).
2025-05-29 15:59:13.435 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetUserFilters", controller = "Filters"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[DISAdmin.Api.Controllers.FilterDto]]] GetUserFilters(System.String) on controller DISAdmin.Api.Controllers.FiltersController (DISAdmin.Api).
2025-05-29 15:59:13.438 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetActivityLogs", controller = "Logs"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetActivityLogs(DISAdmin.Api.Models.LogFilterRequest) on controller DISAdmin.Api.Controllers.LogsController (DISAdmin.Api).
2025-05-29 15:59:13.443 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsHidden] = CAST(0 AS bit)
2025-05-29 15:59:13.446 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__entityType_0='?' (Size = 100), @__userId_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[EntityType], [s].[FilterJson], [s].[IsDefault], [s].[IsShared], [s].[LastUsedAt], [s].[Name], [s].[UserId]
FROM [SavedFilters] AS [s]
WHERE [s].[EntityType] = @__entityType_0 AND ([s].[UserId] = @__userId_1 OR [s].[IsShared] = CAST(1 AS bit))
ORDER BY [s].[IsDefault] DESC, [s].[LastUsedAt] DESC, [s].[Name]
2025-05-29 15:59:13.452 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.User, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.UserResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:13.455 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[DISAdmin.Api.Controllers.FilterDto, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:13.457 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api) in 15.7424ms
2025-05-29 15:59:13.460 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api) in 14.7871ms
2025-05-29 15:59:13.462 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:59:13.464 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:59:13.466 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/users - 200 null application/json; charset=utf-8 64.7246ms
2025-05-29 15:59:13.468 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/filters/logs-activity - 200 null application/json; charset=utf-8 64.3143ms
2025-05-29 15:59:13.511 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (62ms) [Parameters=[@__p_3='?' (DbType = Int32), @__fromDate_0='?' (DbType = DateTime2), @__toDate_1='?' (DbType = DateTime2), @__source_Value_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Id], [a0].[ActivityType], [a0].[AdditionalInfo], [a0].[Description], [a0].[Endpoint], [a0].[EntityId], [a0].[EntityName], [a0].[IpAddress], [a0].[Method], [a0].[ResponseTimeMs], [a0].[Source], [a0].[StatusCode], [a0].[Timestamp], [a0].[UserId], [a0].[Username], [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM (
    SELECT TOP(@__p_3) [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
    FROM [ActivityLogs] AS [a]
    WHERE [a].[Timestamp] >= @__fromDate_0 AND [a].[Timestamp] <= @__toDate_1 AND [a].[Source] = @__source_Value_2
    ORDER BY [a].[Timestamp] DESC
) AS [a0]
LEFT JOIN [Users] AS [u] ON [a0].[UserId] = [u].[Id]
ORDER BY [a0].[Timestamp] DESC
2025-05-29 15:59:13.518 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.ActivityLog, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.ActivityLogResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:13.522 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.LogsController.GetActivityLogs (DISAdmin.Api) in 73.8272ms
2025-05-29 15:59:13.524 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.LogsController.GetActivityLogs (DISAdmin.Api)'
2025-05-29 15:59:13.526 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/logs/activity?fromDate=2025-05-15T22:00:00.000Z&toDate=2025-05-23T21:59:59.999Z&maxResults=100&source=1 - 200 null application/json; charset=utf-8 119.7715ms
2025-05-29 15:59:13.979 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/users - application/json null
2025-05-29 15:59:13.980 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/logs/activity?fromDate=2025-05-15T22:00:00.000Z&toDate=2025-05-23T21:59:59.999Z&maxResults=100&source=2 - null null
2025-05-29 15:59:13.982 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/filters/logs-api - null null
2025-05-29 15:59:13.986 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:13.991 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:13.997 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:14.000 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:59:14.004 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.LogsController.GetActivityLogs (DISAdmin.Api)'
2025-05-29 15:59:14.009 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:59:14.011 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetAllUsers", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers(System.String, System.String, System.String, System.String) on controller DISAdmin.Api.Controllers.UsersController (DISAdmin.Api).
2025-05-29 15:59:14.014 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetActivityLogs", controller = "Logs"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetActivityLogs(DISAdmin.Api.Models.LogFilterRequest) on controller DISAdmin.Api.Controllers.LogsController (DISAdmin.Api).
2025-05-29 15:59:14.016 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetUserFilters", controller = "Filters"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[DISAdmin.Api.Controllers.FilterDto]]] GetUserFilters(System.String) on controller DISAdmin.Api.Controllers.FiltersController (DISAdmin.Api).
2025-05-29 15:59:14.021 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsHidden] = CAST(0 AS bit)
2025-05-29 15:59:14.029 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__entityType_0='?' (Size = 100), @__userId_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[EntityType], [s].[FilterJson], [s].[IsDefault], [s].[IsShared], [s].[LastUsedAt], [s].[Name], [s].[UserId]
FROM [SavedFilters] AS [s]
WHERE [s].[EntityType] = @__entityType_0 AND ([s].[UserId] = @__userId_1 OR [s].[IsShared] = CAST(1 AS bit))
ORDER BY [s].[IsDefault] DESC, [s].[LastUsedAt] DESC, [s].[Name]
2025-05-29 15:59:14.032 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.User, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.UserResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:14.035 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[DISAdmin.Api.Controllers.FilterDto, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:14.038 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api) in 18.0232ms
2025-05-29 15:59:14.041 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api) in 13.4009ms
2025-05-29 15:59:14.044 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:59:14.047 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:59:14.049 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/users - 200 null application/json; charset=utf-8 70.1599ms
2025-05-29 15:59:14.051 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/filters/logs-api - 200 null application/json; charset=utf-8 69.1223ms
2025-05-29 15:59:14.118 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (93ms) [Parameters=[@__p_3='?' (DbType = Int32), @__fromDate_0='?' (DbType = DateTime2), @__toDate_1='?' (DbType = DateTime2), @__source_Value_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Id], [a0].[ActivityType], [a0].[AdditionalInfo], [a0].[Description], [a0].[Endpoint], [a0].[EntityId], [a0].[EntityName], [a0].[IpAddress], [a0].[Method], [a0].[ResponseTimeMs], [a0].[Source], [a0].[StatusCode], [a0].[Timestamp], [a0].[UserId], [a0].[Username], [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM (
    SELECT TOP(@__p_3) [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
    FROM [ActivityLogs] AS [a]
    WHERE [a].[Timestamp] >= @__fromDate_0 AND [a].[Timestamp] <= @__toDate_1 AND [a].[Source] = @__source_Value_2
    ORDER BY [a].[Timestamp] DESC
) AS [a0]
LEFT JOIN [Users] AS [u] ON [a0].[UserId] = [u].[Id]
ORDER BY [a0].[Timestamp] DESC
2025-05-29 15:59:14.123 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.ActivityLog, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.ActivityLogResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:14.126 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.LogsController.GetActivityLogs (DISAdmin.Api) in 102.6223ms
2025-05-29 15:59:14.129 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.LogsController.GetActivityLogs (DISAdmin.Api)'
2025-05-29 15:59:14.131 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/logs/activity?fromDate=2025-05-15T22:00:00.000Z&toDate=2025-05-23T21:59:59.999Z&maxResults=100&source=2 - 200 null application/json; charset=utf-8 151.1948ms
2025-05-29 15:59:15.434 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/users - application/json null
2025-05-29 15:59:15.434 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/filters/logs-error - null null
2025-05-29 15:59:15.435 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/logs/errors?fromDate=2025-05-21T22:00:00.000Z&toDate=2025-05-29T21:59:59.999Z&maxResults=100 - null null
2025-05-29 15:59:15.439 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (0ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:15.445 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:15.449 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 15:59:15.452 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:59:15.455 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:59:15.459 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api)'
2025-05-29 15:59:15.461 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetAllUsers", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers(System.String, System.String, System.String, System.String) on controller DISAdmin.Api.Controllers.UsersController (DISAdmin.Api).
2025-05-29 15:59:15.463 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetUserFilters", controller = "Filters"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[DISAdmin.Api.Controllers.FilterDto]]] GetUserFilters(System.String) on controller DISAdmin.Api.Controllers.FiltersController (DISAdmin.Api).
2025-05-29 15:59:15.465 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetErrorLogs", controller = "Logs"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetErrorLogs(DISAdmin.Api.Models.LogFilterRequest) on controller DISAdmin.Api.Controllers.LogsController (DISAdmin.Api).
2025-05-29 15:59:15.469 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsHidden] = CAST(0 AS bit)
2025-05-29 15:59:15.472 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (0ms) [Parameters=[@__entityType_0='?' (Size = 100), @__userId_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[EntityType], [s].[FilterJson], [s].[IsDefault], [s].[IsShared], [s].[LastUsedAt], [s].[Name], [s].[UserId]
FROM [SavedFilters] AS [s]
WHERE [s].[EntityType] = @__entityType_0 AND ([s].[UserId] = @__userId_1 OR [s].[IsShared] = CAST(1 AS bit))
ORDER BY [s].[IsDefault] DESC, [s].[LastUsedAt] DESC, [s].[Name]
2025-05-29 15:59:15.478 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__p_2='?' (DbType = Int32), @__fromDate_0='?' (DbType = DateTime2), @__toDate_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [e0].[Id], [e0].[AdditionalInfo], [e0].[Category], [e0].[IpAddress], [e0].[LogLevel], [e0].[Message], [e0].[RequestMethod], [e0].[RequestPath], [e0].[Source], [e0].[StackTrace], [e0].[StatusCode], [e0].[Timestamp], [e0].[UserId], [e0].[Username], [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM (
    SELECT TOP(@__p_2) [e].[Id], [e].[AdditionalInfo], [e].[Category], [e].[IpAddress], [e].[LogLevel], [e].[Message], [e].[RequestMethod], [e].[RequestPath], [e].[Source], [e].[StackTrace], [e].[StatusCode], [e].[Timestamp], [e].[UserId], [e].[Username]
    FROM [ErrorLogs] AS [e]
    WHERE [e].[Timestamp] >= @__fromDate_0 AND [e].[Timestamp] <= @__toDate_1
    ORDER BY [e].[Timestamp] DESC
) AS [e0]
LEFT JOIN [Users] AS [u] ON [e0].[UserId] = [u].[Id]
ORDER BY [e0].[Timestamp] DESC
2025-05-29 15:59:15.480 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.User, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.UserResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:15.484 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[DISAdmin.Api.Controllers.FilterDto, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:15.490 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.ErrorLog, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.ErrorLogResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 15:59:15.493 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api) in 24.7694ms
2025-05-29 15:59:15.495 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api) in 23.9318ms
2025-05-29 15:59:15.498 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api) in 22.3454ms
2025-05-29 15:59:15.500 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 15:59:15.502 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 15:59:15.504 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api)'
2025-05-29 15:59:15.506 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/users - 200 null application/json; charset=utf-8 72.5597ms
2025-05-29 15:59:15.509 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/filters/logs-error - 200 null application/json; charset=utf-8 74.255ms
2025-05-29 15:59:15.511 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/logs/errors?fromDate=2025-05-21T22:00:00.000Z&toDate=2025-05-29T21:59:59.999Z&maxResults=100 - 200 null application/json; charset=utf-8 76.1667ms
2025-05-29 16:03:07.381 +02:00 [WRN] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: C:\Users\<USER>\Documents\VSCodeProjects\DISAdminAugment\DISAdmin.Web\wwwroot. Static files may be unavailable.
2025-05-29 16:03:07.443 +02:00 [INF] Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager: User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-29 16:03:07.484 +02:00 [INF] DISAdmin.Api.Services.UnhandledExceptionService: UnhandledExceptionService byl spuštěn
2025-05-29 16:03:08.987 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 16:03:09.003 +02:00 [INF] Microsoft.EntityFrameworkCore.Migrations: Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-05-29 16:03:09.037 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-05-29 16:03:09.131 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-05-29 16:03:09.145 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-05-29 16:03:09.151 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-05-29 16:03:09.166 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-05-29 16:03:09.179 +02:00 [INF] Microsoft.EntityFrameworkCore.Migrations: No migrations were applied. The database is already up to date.
2025-05-29 16:03:09.188 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
2025-05-29 16:03:09.468 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-05-29 16:03:09.478 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Alerting Background Service is starting
2025-05-29 16:03:09.481 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Alerting Background Service is running check at: "2025-05-29T16:03:09.4815858+02:00"
2025-05-29 16:03:09.746 +02:00 [INF] DISAdmin.Api.BackgroundServices.RealtimeMetricsBackgroundService: Realtime metrics background service is starting
2025-05-29 16:03:09.748 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[ClientCertificateThumbprint] IS NOT NULL AND [d].[CertificateExpirationDate] > GETUTCDATE() AND [d].[CertificateExpirationDate] < DATEADD(day, CAST(30.0E0 AS int), GETUTCDATE())
2025-05-29 16:03:09.759 +02:00 [INF] DISAdmin.Api.BackgroundServices.CertificateRotationBackgroundService: Služba pro automatickou rotaci certifikátů byla spuštěna
2025-05-29 16:03:09.761 +02:00 [INF] DISAdmin.Api.BackgroundServices.CertificateRotationBackgroundService: Spouštění kontroly expirujících certifikátů
2025-05-29 16:03:09.765 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked expiring certificates
2025-05-29 16:03:09.767 +02:00 [INF] DISAdmin.Core.Services.CertificateRotationService: Kontrola expirujících certifikátů
2025-05-29 16:03:09.791 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[FailedCertificateValidationCount] > 5
2025-05-29 16:03:09.850 +02:00 [WRN] Microsoft.EntityFrameworkCore.Query: The query uses the 'First'/'FirstOrDefault' operator without 'OrderBy' and filter operators. This may lead to unpredictable results.
2025-05-29 16:03:09.868 +02:00 [INF] DISAdmin.Api.BackgroundServices.AnomalyDetectionBackgroundService: Služba pro detekci anomálií byla spuštěna
2025-05-29 16:03:09.870 +02:00 [INF] DISAdmin.Api.BackgroundServices.AnomalyDetectionBackgroundService: Spouštění detekce anomálií
2025-05-29 16:03:09.873 +02:00 [INF] DISAdmin.Core.Services.AnomalyDetectionService: Spuštění detekce anomálií v přístupech k API
2025-05-29 16:03:09.874 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[Id], [c].[EnableAutoRotation], [c].[LastUpdated], [c].[LastUpdatedBy], [c].[NewCertificateValidityDays], [c].[NotificationEmails], [c].[NotificationRepeatDays], [c].[NotifyDaysBeforeExpiration], [c].[RotateDaysBeforeExpiration], [c].[SendExpirationNotifications]
FROM [CertificateRotationSettings] AS [c]
2025-05-29 16:03:09.895 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status]
FROM [DISInstances] AS [d]
WHERE [d].[Status] = 0
2025-05-29 16:03:09.934 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[ClientCertificateThumbprint] IS NOT NULL AND [d].[CertificateExpirationDate] IS NOT NULL
2025-05-29 16:03:09.983 +02:00 [WRN] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: C:\Users\<USER>\Documents\VSCodeProjects\DISAdminAugment\DISAdmin.Web\wwwroot. Static files may be unavailable.
2025-05-29 16:03:10.023 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 16:03:10.023 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[@__instance_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Alerts] AS [a]
        WHERE [a].[InstanceId] = @__instance_Id_0 AND [a].[AlertType] = 1 AND [a].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-05-29 16:03:10.030 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (53ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 16:03:10.039 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instance_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Alerts] AS [a]
        WHERE [a].[InstanceId] = @__instance_Id_0 AND [a].[AlertType] = 1 AND [a].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-05-29 16:03:10.045 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked failed connection attempts
2025-05-29 16:03:10.050 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (12ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 16:03:10.060 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 16:03:10.069 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (13ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 16:03:10.069 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (2ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 16:03:10.080 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[Description], [s].[EventType], [s].[InstanceId], [s].[IpAddress], [s].[IsResolved], [s].[Resolution], [s].[ResolvedAt], [s].[ResolvedBy], [s].[Resource], [s].[Severity], [s].[Timestamp], [s].[Username]
FROM [SecurityEvents] AS [s]
WHERE [s].[EventType] = 1 AND [s].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE()) AND [s].[IsResolved] = CAST(0 AS bit)
2025-05-29 16:03:10.083 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (2ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 16:03:10.087 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked suspicious activities
2025-05-29 16:03:10.092 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 16:03:10.099 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (2ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 16:03:10.105 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 16:03:10.110 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [i].[Id], [i].[EnableAutoRotation], [i].[InstanceId], [i].[LastUpdated], [i].[LastUpdatedBy], [i].[NewCertificateValidityDays], [i].[NotificationEmails], [i].[NotificationRepeatDays], [i].[NotifyDaysBeforeExpiration], [i].[OverrideGlobalSettings], [i].[RotateDaysBeforeExpiration], [i].[SendExpirationNotifications]
FROM [InstanceCertificateSettings] AS [i]
WHERE [i].[InstanceId] = @__instanceId_0
2025-05-29 16:03:10.116 +02:00 [INF] DISAdmin.Api.BackgroundServices.CertificateRotationBackgroundService: Kontrola expirujících certifikátů dokončena
2025-05-29 16:03:10.130 +02:00 [WRN] Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServer: The ASP.NET Core developer certificate is not trusted. For information about trusting the ASP.NET Core developer certificate, see https://aka.ms/aspnet/https-trust-dev-cert
2025-05-29 16:03:10.158 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[InstanceId], AVG(CAST([p].[Avg] AS float)) AS [AvgResponseTime], AVG(CAST([p].[Max] AS float)) AS [MaxResponseTime], AVG(CAST([p].[Percentil95] AS float)) AS [P95ResponseTime]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())
GROUP BY [p].[InstanceId]
2025-05-29 16:03:10.161 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AlertType], [a].[Description], [a].[InstanceId], [a].[IsNotificationSent], [a].[IsResolved], [a].[Resolution], [a].[ResolvedAt], [a].[ResolvedBy], [a].[Severity], [a].[Timestamp]
FROM [Alerts] AS [a]
WHERE [a].[InstanceId] = @__8__locals1_instanceId_0 AND [a].[AlertType] = 6 AND [a].[Description] LIKE N'%mimo pracovní dobu%' AND [a].[Timestamp] >= DATEADD(day, CAST(-7.0E0 AS int), GETUTCDATE())
2025-05-29 16:03:10.162 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked method performance
2025-05-29 16:03:10.186 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[InstanceId], COUNT(*) AS [CallsCount]
FROM [DiagnosticLogs] AS [d]
WHERE [d].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())
GROUP BY [d].[InstanceId]
2025-05-29 16:03:10.193 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked API calls count
2025-05-29 16:03:10.200 +02:00 [ERR] Microsoft.Extensions.Hosting.Internal.Host: Hosting failed to start
System.IO.IOException: Failed to bind to address https://127.0.0.1:7029: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Normálně je povoleno pouze jedno použití každé adresy (protokolu, síťové adresy, portu) soketu.
 ---> System.Net.Sockets.SocketException (10048): Normálně je povoleno pouze jedno použití každé adresy (protokolu, síťové adresy, portu) soketu.
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-05-29 16:03:10.211 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (9ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 16:03:10.220 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[InstanceId], COUNT(*) AS [TotalCalls], COUNT(CASE
    WHEN [d].[ResponseStatusCode] >= 400 THEN 1
END) AS [ErrorCalls]
FROM [DiagnosticLogs] AS [d]
WHERE [d].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())
GROUP BY [d].[InstanceId]
2025-05-29 16:03:10.224 +02:00 [ERR] Microsoft.Extensions.Hosting.Internal.Host: BackgroundService failed
System.Threading.Tasks.TaskCanceledException: A task was canceled.
   at DISAdmin.Api.BackgroundServices.CertificateRotationBackgroundService.ExecuteAsync(CancellationToken stoppingToken) in C:\Users\<USER>\Documents\VSCodeProjects\DISAdminAugment\DISAdmin.Api\BackgroundServices\CertificateRotationBackgroundService.cs:line 43
   at Microsoft.Extensions.Hosting.Internal.Host.TryExecuteBackgroundServiceAsync(BackgroundService backgroundService)
2025-05-29 16:21:26.434 +02:00 [INF] DISAdmin.Api.BackgroundServices.AnomalyDetectionBackgroundService: Spouštění detekce anomálií
2025-05-29 16:21:26.438 +02:00 [INF] DISAdmin.Core.Services.AnomalyDetectionService: Spuštění detekce anomálií v přístupech k API
2025-05-29 16:21:26.457 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status]
FROM [DISInstances] AS [d]
WHERE [d].[Status] = 0
2025-05-29 16:21:26.476 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (15ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 16:21:26.485 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 16:21:26.495 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Alerting Background Service is running check at: "2025-05-29T16:21:26.4952553+02:00"
2025-05-29 16:21:26.496 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (8ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 16:21:26.506 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AlertType], [a].[Description], [a].[InstanceId], [a].[IsNotificationSent], [a].[IsResolved], [a].[Resolution], [a].[ResolvedAt], [a].[ResolvedBy], [a].[Severity], [a].[Timestamp]
FROM [Alerts] AS [a]
WHERE [a].[InstanceId] = @__8__locals1_instanceId_0 AND [a].[AlertType] = 6 AND [a].[Description] LIKE N'%mimo pracovní dobu%' AND [a].[Timestamp] >= DATEADD(day, CAST(-7.0E0 AS int), GETUTCDATE())
2025-05-29 16:21:26.510 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[ClientCertificateThumbprint] IS NOT NULL AND [d].[CertificateExpirationDate] > GETUTCDATE() AND [d].[CertificateExpirationDate] < DATEADD(day, CAST(30.0E0 AS int), GETUTCDATE())
2025-05-29 16:21:26.513 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked expiring certificates
2025-05-29 16:21:26.517 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (7ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 16:21:26.520 +02:00 [INF] DISAdmin.Core.Services.AnomalyDetectionService: Spuštění detekce anomálií v přihlašování uživatelů
2025-05-29 16:21:26.520 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[FailedCertificateValidationCount] > 5
2025-05-29 16:21:26.525 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[@__startDate_0='?' (DbType = DateTime2), @__endDate_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [l].[Id], [l].[IpAddress], [l].[Success], [l].[Timestamp], [l].[UserAgent], [l].[UserId], [l].[Username]
FROM [LoginAttempts] AS [l]
WHERE [l].[Timestamp] >= @__startDate_0 AND [l].[Timestamp] <= @__endDate_1
ORDER BY [l].[Timestamp]
2025-05-29 16:21:26.528 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[@__instance_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Alerts] AS [a]
        WHERE [a].[InstanceId] = @__instance_Id_0 AND [a].[AlertType] = 1 AND [a].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-05-29 16:21:26.529 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Spuštění detekce anomálií ve výkonnostních metrikách
2025-05-29 16:21:26.532 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (0ms) [Parameters=[@__instance_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Alerts] AS [a]
        WHERE [a].[InstanceId] = @__instance_Id_0 AND [a].[AlertType] = 1 AND [a].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-05-29 16:21:26.533 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status]
FROM [DISInstances] AS [d]
WHERE [d].[Status] = 0
2025-05-29 16:21:26.535 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked failed connection attempts
2025-05-29 16:21:26.543 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[Description], [s].[EventType], [s].[InstanceId], [s].[IpAddress], [s].[IsResolved], [s].[Resolution], [s].[ResolvedAt], [s].[ResolvedBy], [s].[Resource], [s].[Severity], [s].[Timestamp], [s].[Username]
FROM [SecurityEvents] AS [s]
WHERE [s].[EventType] = 1 AND [s].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE()) AND [s].[IsResolved] = CAST(0 AS bit)
2025-05-29 16:21:26.543 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 16:21:26.546 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked suspicious activities
2025-05-29 16:21:26.550 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Nedostatek dat pro analýzu výkonnostních metrik pro instanci 2
2025-05-29 16:21:26.554 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (0ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 16:21:26.557 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Nedostatek dat pro analýzu výkonnostních metrik pro instanci 3
2025-05-29 16:21:26.557 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[InstanceId], AVG(CAST([p].[Avg] AS float)) AS [AvgResponseTime], AVG(CAST([p].[Max] AS float)) AS [MaxResponseTime], AVG(CAST([p].[Percentil95] AS float)) AS [P95ResponseTime]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())
GROUP BY [p].[InstanceId]
2025-05-29 16:21:26.559 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 16:21:26.561 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked method performance
2025-05-29 16:21:26.567 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 16:21:26.569 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[InstanceId], COUNT(*) AS [CallsCount]
FROM [DiagnosticLogs] AS [d]
WHERE [d].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())
GROUP BY [d].[InstanceId]
2025-05-29 16:21:26.571 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Nedostatek dat pro analýzu výkonnostních metrik pro instanci 8
2025-05-29 16:21:26.573 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked API calls count
2025-05-29 16:21:26.574 +02:00 [INF] DISAdmin.Api.BackgroundServices.AnomalyDetectionBackgroundService: Detekce anomálií dokončena
2025-05-29 16:21:26.580 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[InstanceId], COUNT(*) AS [TotalCalls], COUNT(CASE
    WHEN [d].[ResponseStatusCode] >= 400 THEN 1
END) AS [ErrorCalls]
FROM [DiagnosticLogs] AS [d]
WHERE [d].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())
GROUP BY [d].[InstanceId]
2025-05-29 16:21:26.584 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked error rate
2025-05-29 16:21:26.586 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[EnableIpWhitelisting] = CAST(1 AS bit)
2025-05-29 16:21:26.588 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked unauthorized IP access
2025-05-29 16:21:26.598 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (8ms) [Parameters=[@__cutoffTime_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[IpAddress], COUNT(*) AS [Count]
FROM [ActivityLogs] AS [a]
WHERE [a].[ActivityType] = 0 AND [a].[Timestamp] > @__cutoffTime_0
GROUP BY [a].[IpAddress]
HAVING COUNT(*) > 10
2025-05-29 16:21:26.613 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (11ms) [Parameters=[@__cutoffTime_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[IpAddress], COUNT(*) AS [Count]
FROM [ActivityLogs] AS [a]
WHERE [a].[ActivityType] = 8 AND [a].[Timestamp] > @__cutoffTime_0
GROUP BY [a].[IpAddress]
2025-05-29 16:21:26.619 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[LastKnownIpAddress]
FROM [DISInstances] AS [d]
WHERE [d].[LastKnownIpAddress] IS NOT NULL
2025-05-29 16:21:26.627 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[@__cutoffTime_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityName] = N'Certificate' AND [a].[Timestamp] > @__cutoffTime_0
2025-05-29 16:21:26.632 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Detected 0 suspicious events
2025-05-29 16:30:29.831 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/filters/logs-error - null null
2025-05-29 16:30:29.833 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/users - application/json null
2025-05-29 16:30:29.859 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (14ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 16:30:29.859 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (9ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 16:30:29.864 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 16:30:29.868 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 16:30:29.869 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetUserFilters", controller = "Filters"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[System.Collections.Generic.List`1[DISAdmin.Api.Controllers.FilterDto]]] GetUserFilters(System.String) on controller DISAdmin.Api.Controllers.FiltersController (DISAdmin.Api).
2025-05-29 16:30:29.872 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetAllUsers", controller = "Users"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllUsers(System.String, System.String, System.String, System.String) on controller DISAdmin.Api.Controllers.UsersController (DISAdmin.Api).
2025-05-29 16:30:29.881 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[@__entityType_0='?' (Size = 100), @__userId_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[CreatedAt], [s].[EntityType], [s].[FilterJson], [s].[IsDefault], [s].[IsShared], [s].[LastUsedAt], [s].[Name], [s].[UserId]
FROM [SavedFilters] AS [s]
WHERE [s].[EntityType] = @__entityType_0 AND ([s].[UserId] = @__userId_1 OR [s].[IsShared] = CAST(1 AS bit))
ORDER BY [s].[IsDefault] DESC, [s].[LastUsedAt] DESC, [s].[Name]
2025-05-29 16:30:29.882 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsHidden] = CAST(0 AS bit)
2025-05-29 16:30:29.885 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[DISAdmin.Api.Controllers.FilterDto, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 16:30:29.890 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.User, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.UserResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 16:30:29.892 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api) in 17.3711ms
2025-05-29 16:30:29.895 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api) in 16.5248ms
2025-05-29 16:30:29.897 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.FiltersController.GetUserFilters (DISAdmin.Api)'
2025-05-29 16:30:29.900 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.UsersController.GetAllUsers (DISAdmin.Api)'
2025-05-29 16:30:29.902 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/filters/logs-error - 200 null application/json; charset=utf-8 70.4586ms
2025-05-29 16:30:29.903 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/users - 200 null application/json; charset=utf-8 70.7957ms
2025-05-29 16:30:29.934 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7029/api/logs/errors?fromDate=2025-05-21T22:00:00.000Z&toDate=2025-05-29T21:59:59.999Z&maxResults=100 - null null
2025-05-29 16:30:29.942 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__tokenValidationResult_UserId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[Id] = @__tokenValidationResult_UserId_0
2025-05-29 16:30:29.946 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executing endpoint 'DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api)'
2025-05-29 16:30:29.948 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Route matched with {action = "GetErrorLogs", controller = "Logs"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetErrorLogs(DISAdmin.Api.Models.LogFilterRequest) on controller DISAdmin.Api.Controllers.LogsController (DISAdmin.Api).
2025-05-29 16:30:29.968 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (14ms) [Parameters=[@__p_2='?' (DbType = Int32), @__fromDate_0='?' (DbType = DateTime2), @__toDate_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [e0].[Id], [e0].[AdditionalInfo], [e0].[Category], [e0].[IpAddress], [e0].[LogLevel], [e0].[Message], [e0].[RequestMethod], [e0].[RequestPath], [e0].[Source], [e0].[StackTrace], [e0].[StatusCode], [e0].[Timestamp], [e0].[UserId], [e0].[Username], [u].[Id], [u].[CreatedAt], [u].[Email], [u].[FirstName], [u].[IsAdmin], [u].[IsHidden], [u].[IsSystem], [u].[LastLoginAt], [u].[LastName], [u].[PasswordHash], [u].[RecoveryCodesHash], [u].[TwoFactorEnabled], [u].[TwoFactorEnabledAt], [u].[TwoFactorSecret], [u].[Username]
FROM (
    SELECT TOP(@__p_2) [e].[Id], [e].[AdditionalInfo], [e].[Category], [e].[IpAddress], [e].[LogLevel], [e].[Message], [e].[RequestMethod], [e].[RequestPath], [e].[Source], [e].[StackTrace], [e].[StatusCode], [e].[Timestamp], [e].[UserId], [e].[Username]
    FROM [ErrorLogs] AS [e]
    WHERE [e].[Timestamp] >= @__fromDate_0 AND [e].[Timestamp] <= @__toDate_1
    ORDER BY [e].[Timestamp] DESC
) AS [e0]
LEFT JOIN [Users] AS [u] ON [e0].[UserId] = [u].[Id]
ORDER BY [e0].[Timestamp] DESC
2025-05-29 16:30:29.973 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ObjectResultExecutor: Executing OkObjectResult, writing value of type 'System.Linq.Enumerable+ListSelectIterator`2[[DISAdmin.Core.Data.Entities.ErrorLog, DISAdmin.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[DISAdmin.Api.Models.ErrorLogResponse, DISAdmin.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]'.
2025-05-29 16:30:29.976 +02:00 [INF] Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker: Executed action DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api) in 24.6307ms
2025-05-29 16:30:29.978 +02:00 [INF] Microsoft.AspNetCore.Routing.EndpointMiddleware: Executed endpoint 'DISAdmin.Api.Controllers.LogsController.GetErrorLogs (DISAdmin.Api)'
2025-05-29 16:30:29.981 +02:00 [INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET https://localhost:7029/api/logs/errors?fromDate=2025-05-21T22:00:00.000Z&toDate=2025-05-29T21:59:59.999Z&maxResults=100 - 200 null application/json; charset=utf-8 46.687ms
2025-05-29 19:39:34.265 +02:00 [INF] DISAdmin.Api.BackgroundServices.AnomalyDetectionBackgroundService: Spouštění detekce anomálií
2025-05-29 19:39:34.271 +02:00 [INF] DISAdmin.Core.Services.AnomalyDetectionService: Spuštění detekce anomálií v přístupech k API
2025-05-29 19:39:34.286 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status]
FROM [DISInstances] AS [d]
WHERE [d].[Status] = 0
2025-05-29 19:39:34.305 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (15ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 19:39:34.312 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 19:39:34.319 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 19:39:34.327 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Alerting Background Service is running check at: "2025-05-29T19:39:34.3276479+02:00"
2025-05-29 19:39:34.328 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AlertType], [a].[Description], [a].[InstanceId], [a].[IsNotificationSent], [a].[IsResolved], [a].[Resolution], [a].[ResolvedAt], [a].[ResolvedBy], [a].[Severity], [a].[Timestamp]
FROM [Alerts] AS [a]
WHERE [a].[InstanceId] = @__8__locals1_instanceId_0 AND [a].[AlertType] = 6 AND [a].[Description] LIKE N'%mimo pracovní dobu%' AND [a].[Timestamp] >= DATEADD(day, CAST(-7.0E0 AS int), GETUTCDATE())
2025-05-29 19:39:34.342 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 19:39:34.343 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[ClientCertificateThumbprint] IS NOT NULL AND [d].[CertificateExpirationDate] > GETUTCDATE() AND [d].[CertificateExpirationDate] < DATEADD(day, CAST(30.0E0 AS int), GETUTCDATE())
2025-05-29 19:39:34.346 +02:00 [INF] DISAdmin.Core.Services.AnomalyDetectionService: Spuštění detekce anomálií v přihlašování uživatelů
2025-05-29 19:39:34.350 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked expiring certificates
2025-05-29 19:39:34.355 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[@__startDate_0='?' (DbType = DateTime2), @__endDate_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [l].[Id], [l].[IpAddress], [l].[Success], [l].[Timestamp], [l].[UserAgent], [l].[UserId], [l].[Username]
FROM [LoginAttempts] AS [l]
WHERE [l].[Timestamp] >= @__startDate_0 AND [l].[Timestamp] <= @__endDate_1
ORDER BY [l].[Timestamp]
2025-05-29 19:39:34.356 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[FailedCertificateValidationCount] > 5
2025-05-29 19:39:34.358 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Spuštění detekce anomálií ve výkonnostních metrikách
2025-05-29 19:39:34.363 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status]
FROM [DISInstances] AS [d]
WHERE [d].[Status] = 0
2025-05-29 19:39:34.364 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[@__instance_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Alerts] AS [a]
        WHERE [a].[InstanceId] = @__instance_Id_0 AND [a].[AlertType] = 1 AND [a].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-05-29 19:39:34.370 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (0ms) [Parameters=[@__instance_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Alerts] AS [a]
        WHERE [a].[InstanceId] = @__instance_Id_0 AND [a].[AlertType] = 1 AND [a].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-05-29 19:39:34.372 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (7ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 19:39:34.373 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked failed connection attempts
2025-05-29 19:39:34.377 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Nedostatek dat pro analýzu výkonnostních metrik pro instanci 2
2025-05-29 19:39:34.380 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 19:39:34.381 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[Description], [s].[EventType], [s].[InstanceId], [s].[IpAddress], [s].[IsResolved], [s].[Resolution], [s].[ResolvedAt], [s].[ResolvedBy], [s].[Resource], [s].[Severity], [s].[Timestamp], [s].[Username]
FROM [SecurityEvents] AS [s]
WHERE [s].[EventType] = 1 AND [s].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE()) AND [s].[IsResolved] = CAST(0 AS bit)
2025-05-29 19:39:34.382 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Nedostatek dat pro analýzu výkonnostních metrik pro instanci 3
2025-05-29 19:39:34.385 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked suspicious activities
2025-05-29 19:39:34.387 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 19:39:34.391 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[InstanceId], AVG(CAST([p].[Avg] AS float)) AS [AvgResponseTime], AVG(CAST([p].[Max] AS float)) AS [MaxResponseTime], AVG(CAST([p].[Percentil95] AS float)) AS [P95ResponseTime]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())
GROUP BY [p].[InstanceId]
2025-05-29 19:39:34.391 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (0ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 19:39:34.393 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked method performance
2025-05-29 19:39:34.395 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Nedostatek dat pro analýzu výkonnostních metrik pro instanci 8
2025-05-29 19:39:34.397 +02:00 [INF] DISAdmin.Api.BackgroundServices.AnomalyDetectionBackgroundService: Detekce anomálií dokončena
2025-05-29 19:39:34.399 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[InstanceId], COUNT(*) AS [CallsCount]
FROM [DiagnosticLogs] AS [d]
WHERE [d].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())
GROUP BY [d].[InstanceId]
2025-05-29 19:39:34.401 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked API calls count
2025-05-29 19:39:34.405 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[InstanceId], COUNT(*) AS [TotalCalls], COUNT(CASE
    WHEN [d].[ResponseStatusCode] >= 400 THEN 1
END) AS [ErrorCalls]
FROM [DiagnosticLogs] AS [d]
WHERE [d].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())
GROUP BY [d].[InstanceId]
2025-05-29 19:39:34.408 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked error rate
2025-05-29 19:39:34.409 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[EnableIpWhitelisting] = CAST(1 AS bit)
2025-05-29 19:39:34.411 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked unauthorized IP access
2025-05-29 19:39:34.419 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[@__cutoffTime_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[IpAddress], COUNT(*) AS [Count]
FROM [ActivityLogs] AS [a]
WHERE [a].[ActivityType] = 0 AND [a].[Timestamp] > @__cutoffTime_0
GROUP BY [a].[IpAddress]
HAVING COUNT(*) > 10
2025-05-29 19:39:34.429 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[@__cutoffTime_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[IpAddress], COUNT(*) AS [Count]
FROM [ActivityLogs] AS [a]
WHERE [a].[ActivityType] = 8 AND [a].[Timestamp] > @__cutoffTime_0
GROUP BY [a].[IpAddress]
2025-05-29 19:39:34.433 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[LastKnownIpAddress]
FROM [DISInstances] AS [d]
WHERE [d].[LastKnownIpAddress] IS NOT NULL
2025-05-29 19:39:34.440 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[@__cutoffTime_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityName] = N'Certificate' AND [a].[Timestamp] > @__cutoffTime_0
2025-05-29 19:39:34.443 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Detected 0 suspicious events
2025-05-29 20:09:34.413 +02:00 [INF] DISAdmin.Api.BackgroundServices.AnomalyDetectionBackgroundService: Spouštění detekce anomálií
2025-05-29 20:09:34.416 +02:00 [INF] DISAdmin.Core.Services.AnomalyDetectionService: Spuštění detekce anomálií v přístupech k API
2025-05-29 20:09:34.442 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status]
FROM [DISInstances] AS [d]
WHERE [d].[Status] = 0
2025-05-29 20:09:34.445 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Alerting Background Service is running check at: "2025-05-29T20:09:34.4455798+02:00"
2025-05-29 20:09:34.460 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[ClientCertificateThumbprint] IS NOT NULL AND [d].[CertificateExpirationDate] > GETUTCDATE() AND [d].[CertificateExpirationDate] < DATEADD(day, CAST(30.0E0 AS int), GETUTCDATE())
2025-05-29 20:09:34.464 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked expiring certificates
2025-05-29 20:09:34.465 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (19ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 20:09:34.468 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[FailedCertificateValidationCount] > 5
2025-05-29 20:09:34.474 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 20:09:34.475 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[@__instance_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Alerts] AS [a]
        WHERE [a].[InstanceId] = @__instance_Id_0 AND [a].[AlertType] = 1 AND [a].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-05-29 20:09:34.483 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__instance_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Alerts] AS [a]
        WHERE [a].[InstanceId] = @__instance_Id_0 AND [a].[AlertType] = 1 AND [a].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-05-29 20:09:34.485 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 20:09:34.487 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked failed connection attempts
2025-05-29 20:09:34.494 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AlertType], [a].[Description], [a].[InstanceId], [a].[IsNotificationSent], [a].[IsResolved], [a].[Resolution], [a].[ResolvedAt], [a].[ResolvedBy], [a].[Severity], [a].[Timestamp]
FROM [Alerts] AS [a]
WHERE [a].[InstanceId] = @__8__locals1_instanceId_0 AND [a].[AlertType] = 6 AND [a].[Description] LIKE N'%mimo pracovní dobu%' AND [a].[Timestamp] >= DATEADD(day, CAST(-7.0E0 AS int), GETUTCDATE())
2025-05-29 20:09:34.494 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[Description], [s].[EventType], [s].[InstanceId], [s].[IpAddress], [s].[IsResolved], [s].[Resolution], [s].[ResolvedAt], [s].[ResolvedBy], [s].[Resource], [s].[Severity], [s].[Timestamp], [s].[Username]
FROM [SecurityEvents] AS [s]
WHERE [s].[EventType] = 1 AND [s].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE()) AND [s].[IsResolved] = CAST(0 AS bit)
2025-05-29 20:09:34.501 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked suspicious activities
2025-05-29 20:09:34.504 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityId] = @__8__locals1_instanceId_0 AND [a].[Source] = 2 AND [a].[Timestamp] >= @__startDate_1 AND [a].[Timestamp] <= @__endDate_2
ORDER BY [a].[Timestamp]
2025-05-29 20:09:34.507 +02:00 [INF] DISAdmin.Core.Services.AnomalyDetectionService: Spuštění detekce anomálií v přihlašování uživatelů
2025-05-29 20:09:34.510 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[InstanceId], AVG(CAST([p].[Avg] AS float)) AS [AvgResponseTime], AVG(CAST([p].[Max] AS float)) AS [MaxResponseTime], AVG(CAST([p].[Percentil95] AS float)) AS [P95ResponseTime]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())
GROUP BY [p].[InstanceId]
2025-05-29 20:09:34.512 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked method performance
2025-05-29 20:09:34.518 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[InstanceId], COUNT(*) AS [CallsCount]
FROM [DiagnosticLogs] AS [d]
WHERE [d].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())
GROUP BY [d].[InstanceId]
2025-05-29 20:09:34.521 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (11ms) [Parameters=[@__startDate_0='?' (DbType = DateTime2), @__endDate_1='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [l].[Id], [l].[IpAddress], [l].[Success], [l].[Timestamp], [l].[UserAgent], [l].[UserId], [l].[Username]
FROM [LoginAttempts] AS [l]
WHERE [l].[Timestamp] >= @__startDate_0 AND [l].[Timestamp] <= @__endDate_1
ORDER BY [l].[Timestamp]
2025-05-29 20:09:34.523 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked API calls count
2025-05-29 20:09:34.527 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Spuštění detekce anomálií ve výkonnostních metrikách
2025-05-29 20:09:34.530 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status]
FROM [DISInstances] AS [d]
WHERE [d].[Status] = 0
2025-05-29 20:09:34.534 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[InstanceId], COUNT(*) AS [TotalCalls], COUNT(CASE
    WHEN [d].[ResponseStatusCode] >= 400 THEN 1
END) AS [ErrorCalls]
FROM [DiagnosticLogs] AS [d]
WHERE [d].[Timestamp] > DATEADD(day, CAST(-1.0E0 AS int), GETUTCDATE())
GROUP BY [d].[InstanceId]
2025-05-29 20:09:34.535 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 20:09:34.538 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked error rate
2025-05-29 20:09:34.542 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Nedostatek dat pro analýzu výkonnostních metrik pro instanci 2
2025-05-29 20:09:34.544 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[Id], [d].[ApiKey], [d].[BlockReason], [d].[CertificateExpirationDate], [d].[ClientCertificateIssuer], [d].[ClientCertificateSubject], [d].[ClientCertificateThumbprint], [d].[CustomerId], [d].[EnableIpWhitelisting], [d].[ExpirationDate], [d].[FailedCertificateValidationCount], [d].[InstallationDate], [d].[LastCertificateValidation], [d].[LastConnectionDate], [d].[LastFailedCertificateValidation], [d].[LastKnownIpAddress], [d].[ModuleAdvancedSecurity], [d].[ModuleApiIntegration], [d].[ModuleCustomization], [d].[ModuleDataExport], [d].[ModuleReporting], [d].[Name], [d].[Notes], [d].[ServerUrl], [d].[Status], [c].[Id], [c].[Abbreviation], [c].[City], [c].[CompanyId], [c].[Country], [c].[CreatedAt], [c].[Email], [c].[Name], [c].[Notes], [c].[Phone], [c].[PostalCode], [c].[Street], [c].[TaxId], [c].[UpdatedAt], [c].[Website]
FROM [DISInstances] AS [d]
INNER JOIN [Customers] AS [c] ON [d].[CustomerId] = [c].[Id]
WHERE [d].[EnableIpWhitelisting] = CAST(1 AS bit)
2025-05-29 20:09:34.545 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 20:09:34.548 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Checked unauthorized IP access
2025-05-29 20:09:34.551 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Nedostatek dat pro analýzu výkonnostních metrik pro instanci 3
2025-05-29 20:09:34.555 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 20:09:34.561 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (1ms) [Parameters=[@__8__locals1_instanceId_0='?' (DbType = Int32), @__startDate_1='?' (DbType = DateTime2), @__endDate_2='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Avg], [p].[ClassName], [p].[InstanceId], [p].[Max], [p].[Median], [p].[MethodName], [p].[Min], [p].[NonZeroCount], [p].[Parameters], [p].[Percentil95], [p].[Percentil99], [p].[StdDev], [p].[Timestamp], [p].[TotalCount], [p].[VersionNumber]
FROM [PerformanceMetrics] AS [p]
WHERE [p].[InstanceId] = @__8__locals1_instanceId_0 AND [p].[Timestamp] >= @__startDate_1 AND [p].[Timestamp] <= @__endDate_2
ORDER BY [p].[Timestamp]
2025-05-29 20:09:34.565 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (12ms) [Parameters=[@__cutoffTime_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[IpAddress], COUNT(*) AS [Count]
FROM [ActivityLogs] AS [a]
WHERE [a].[ActivityType] = 0 AND [a].[Timestamp] > @__cutoffTime_0
GROUP BY [a].[IpAddress]
HAVING COUNT(*) > 10
2025-05-29 20:09:34.565 +02:00 [INF] DISAdmin.Core.Services.PerformanceAnomalyDetectionService: Nedostatek dat pro analýzu výkonnostních metrik pro instanci 8
2025-05-29 20:09:34.571 +02:00 [INF] DISAdmin.Api.BackgroundServices.AnomalyDetectionBackgroundService: Detekce anomálií dokončena
2025-05-29 20:09:34.581 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (11ms) [Parameters=[@__cutoffTime_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[IpAddress], COUNT(*) AS [Count]
FROM [ActivityLogs] AS [a]
WHERE [a].[ActivityType] = 8 AND [a].[Timestamp] > @__cutoffTime_0
GROUP BY [a].[IpAddress]
2025-05-29 20:09:34.588 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [d].[LastKnownIpAddress]
FROM [DISInstances] AS [d]
WHERE [d].[LastKnownIpAddress] IS NOT NULL
2025-05-29 20:09:34.601 +02:00 [INF] Microsoft.EntityFrameworkCore.Database.Command: Executed DbCommand (10ms) [Parameters=[@__cutoffTime_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ActivityType], [a].[AdditionalInfo], [a].[Description], [a].[Endpoint], [a].[EntityId], [a].[EntityName], [a].[IpAddress], [a].[Method], [a].[ResponseTimeMs], [a].[Source], [a].[StatusCode], [a].[Timestamp], [a].[UserId], [a].[Username]
FROM [ActivityLogs] AS [a]
WHERE [a].[EntityName] = N'Certificate' AND [a].[Timestamp] > @__cutoffTime_0
2025-05-29 20:09:34.605 +02:00 [INF] DISAdmin.Api.BackgroundServices.AlertingBackgroundService: Detected 0 suspicious events
