using Microsoft.Extensions.FileProviders;
using DISAdmin.Api.BackgroundServices;
using DISAdmin.Api.Hubs;
using DISAdmin.Core;
using DISAdmin.Core.Services;
using DISAdmin.Core.Logging;
using DISAdmin.Core.Extensions;
using DISAdmin.Migrations;
using DISAdmin.Api.Middleware;
using DISAdmin.Api.Auth;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Serilog;

// Vytvoření builder s file logging podporou
var builder = WebApplication.CreateBuilder(args);

// Přidání file logging
builder.Host.AddFileLogging();

// Načtení sdílené konfigurace
builder.Configuration.AddJsonFile(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "appsettings.shared.json"), optional: true, reloadOnChange: true);

// Konfigurace loggingu
builder.Logging.AddDatabaseLogger(options => {
    // Načtení konfigurace z appsettings.json
    var databaseLoggingConfig = builder.Configuration.GetSection("Logging:Database");
    var logLevelString = databaseLoggingConfig["LogLevel"] ?? "Warning";

    if (Enum.TryParse<LogLevel>(logLevelString, out var logLevel))
    {
        options.LogLevel = logLevel;
    }
    else
    {
        options.LogLevel = LogLevel.Warning; // Výchozí hodnota
    }
}, "DISAdmin");

// Add services to the container.
builder.Services.AddControllersWithViews()
    .AddJsonOptions(options =>
    {
        // Přidání všech custom converterů pro DISAdmin
        options.AddDISAdminConverters();
    });
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        // Přidání všech custom converterů pro DISAdmin
        options.AddDISAdminConverters();
    });

// Přidání file logging služeb
builder.Services.AddFileLoggingServices(builder.Configuration);

// Přidání global exception handler
builder.Services.AddExceptionHandler<DISAdmin.Api.Exceptions.GlobalExceptionHandler>();
builder.Services.AddProblemDetails();

// Přidání služby pro zachytávání neošetřených výjimek
builder.Services.AddHostedService<DISAdmin.Api.Services.UnhandledExceptionService>();

// Přidání služeb pro statické soubory
// Zkusíme najít správný adresář pro statické soubory
var staticFilesPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");
if (!Directory.Exists(staticFilesPath))
{
    // Fallback pro vývojové prostředí
    staticFilesPath = Path.Combine(Directory.GetCurrentDirectory(), "ClientApp/dist");
}

if (Directory.Exists(staticFilesPath))
{
    builder.Services.AddSingleton<IFileProvider>(new PhysicalFileProvider(staticFilesPath));
}

// Přidání HttpContextAccessor pro přístup k HTTP kontextu
builder.Services.AddHttpContextAccessor();

// Přidání podpory pro session
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(30);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});
builder.Services.AddEndpointsApiExplorer();

// Konfigurace Swagger/OpenAPI
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "DISAdmin API", Version = "v1" });

    // Konfigurace JWT autentizace pro Swagger
    c.AddSecurityDefinition("Bearer", new()
    {
        Name = "Authorization",
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.Http,
        Scheme = "bearer",
        BearerFormat = "JWT",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Description = "JWT Authorization header using the Bearer scheme."
    });

    c.AddSecurityRequirement(new()
    {
        {
            new()
            {
                Reference = new()
                {
                    Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

// Add CORS services
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", builder =>
    {
        builder.SetIsOriginAllowed(_ => true)
               .AllowAnyMethod()
               .AllowAnyHeader()
               .AllowCredentials()
               .WithExposedHeaders("Content-Disposition")
               .SetPreflightMaxAge(TimeSpan.FromMinutes(10));
    });

    // Speciální CORS politika pro SignalR
    options.AddPolicy("SignalRPolicy", builder =>
    {
        builder.SetIsOriginAllowed(_ => true)
               .AllowAnyMethod()
               .AllowAnyHeader()
               .AllowCredentials()
               .WithExposedHeaders("Content-Disposition")
               // Explicitně povolíme WebSocket
               .SetIsOriginAllowed(origin => true);
    });
});

// Přidání SignalR s rozšířenou konfigurací
builder.Services.AddSignalR(options =>
{
    // Zvýšení timeoutu pro handshake a keep-alive
    options.ClientTimeoutInterval = TimeSpan.FromSeconds(30);
    options.KeepAliveInterval = TimeSpan.FromSeconds(15);

    // Povolení detailního logování
    options.EnableDetailedErrors = true;
});

// Přidání služeb z Core projektu
builder.Services.AddCoreServices(builder.Configuration);

// Konfigurace JWT autentizace
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var key = Encoding.ASCII.GetBytes(jwtSettings["Secret"] ?? "defaultsecretkey12345678901234567890");

builder.Services.AddAuthentication(x =>
{
    x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(x =>
{
    x.RequireHttpsMetadata = false;
    x.SaveToken = true;
    x.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = false,
        ValidateAudience = false,
        ClockSkew = TimeSpan.Zero
    };

    // Konfigurace pro SignalR
    x.Events = new JwtBearerEvents
    {
        OnMessageReceived = context =>
        {
            var accessToken = context.Request.Query["access_token"];

            // Pokud token není v query string, zkusíme ho najít v hlavičce
            if (string.IsNullOrEmpty(accessToken))
            {
                accessToken = context.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
            }

            // Pokud je požadavek na SignalR hub
            var path = context.HttpContext.Request.Path;
            if (!string.IsNullOrEmpty(accessToken) && path.StartsWithSegments("/hubs/metrics"))
            {
                // Přidáme token do kontextu
                context.Token = accessToken;
            }
            return Task.CompletedTask;
        }
    };
});

builder.Services.AddScoped<IJwtUtils, JwtUtils>();
builder.Services.AddHostedService<MigrationHostedService>();

// Registrace nových služeb
builder.Services.AddScoped<SecurityMonitoringService>();
builder.Services.AddScoped<MetricsService>();
builder.Services.AddScoped<AlertingService>();
builder.Services.AddScoped<EmailService>();
builder.Services.AddScoped<CertificateRotationService>();
builder.Services.AddScoped<ServerCertificateService>();
builder.Services.AddScoped<AnomalyDetectionService>();
builder.Services.AddScoped<PerformanceAnomalyDetectionService>();
builder.Services.AddScoped<IpWhitelistingService>();
builder.Services.AddScoped<DISAdmin.Core.Services.DashboardConfigService>();

// Registrace služby pro zpracování pravidel alertů
builder.Services.AddScoped<AlertRuleProcessingService>(sp => {
    var context = sp.GetRequiredService<DISAdmin.Core.Data.DISAdminDbContext>();
    var logger = sp.GetRequiredService<ILogger<AlertRuleProcessingService>>();
    var alertingService = sp.GetRequiredService<AlertingService>();
    var cachingService = sp.GetRequiredService<CachingService>();
    var anomalyDetectionService = sp.GetRequiredService<AnomalyDetectionService>();
    var performanceAnomalyDetectionService = sp.GetRequiredService<PerformanceAnomalyDetectionService>();

    var service = new AlertRuleProcessingService(context, logger, alertingService, cachingService);
    alertingService.SetAlertRuleProcessingService(service);
    anomalyDetectionService.SetAlertRuleProcessingService(service);
    performanceAnomalyDetectionService.SetAlertRuleProcessingService(service);

    return service;
});

// Registrace plánovaných úloh
builder.Services.AddHostedService<AlertingBackgroundService>();
builder.Services.AddHostedService<RealtimeMetricsBackgroundService>();
builder.Services.AddHostedService<CertificateRotationBackgroundService>();
builder.Services.AddHostedService<AnomalyDetectionBackgroundService>();

// Konfigurace statických souborů
var staticFileOptions = new StaticFileOptions
{
    OnPrepareResponse = ctx =>
    {
        ctx.Context.Response.Headers.Append("Cache-Control", "no-cache, no-store, must-revalidate");
        ctx.Context.Response.Headers.Append("Pragma", "no-cache");
        ctx.Context.Response.Headers.Append("Expires", "0");
    }
};

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}
else
{
    app.UseSwagger();
    app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "DISAdmin API v1"));
    app.UseDeveloperExceptionPage();
}

// Použití middleware pro zpracování chyb - musí být jako první
app.Use(async (context, next) => {
    var scope = app.Services.CreateScope();
    var errorHandlingMiddleware = ActivatorUtilities.CreateInstance<ErrorHandlingMiddleware>(
        scope.ServiceProvider,
        (RequestDelegate)next);
    await errorHandlingMiddleware.InvokeAsync(context);
});

app.UseHttpsRedirection();
app.UseStaticFiles(staticFileOptions);

// Přidání podpory pro statické soubory Angular aplikace
// Zkusíme najít správný adresář pro statické soubory
var angularStaticFilesPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");
if (!Directory.Exists(angularStaticFilesPath))
{
    // Fallback pro vývojové prostředí
    angularStaticFilesPath = Path.Combine(Directory.GetCurrentDirectory(), "ClientApp/dist");
}

if (Directory.Exists(angularStaticFilesPath))
{
    app.UseStaticFiles(new StaticFileOptions
    {
        FileProvider = new PhysicalFileProvider(angularStaticFilesPath),
        RequestPath = ""
    });
}
// Použití exception handler middleware - musí být jako první
app.UseExceptionHandler();

// Použití middleware pro zpracování chyb - jako backup
app.UseMiddleware<DISAdmin.Api.Middleware.WebErrorHandlingMiddleware>();

app.UseRouting();

// Use CORS
app.UseCors("AllowAll");

// Použití vlastního JWT middleware
app.Use(async (context, next) => {
    var scope = app.Services.CreateScope();
    var jwtMiddleware = ActivatorUtilities.CreateInstance<DISAdmin.Api.Middleware.JwtMiddleware>(
        scope.ServiceProvider,
        (RequestDelegate)next);
    await jwtMiddleware.InvokeAsync(context);
});

// Použití middleware pro logování aktivit
app.Use(async (context, next) => {
    var scope = app.Services.CreateScope();
    var activityLoggingMiddleware = ActivatorUtilities.CreateInstance<ActivityLoggingMiddleware>(
        scope.ServiceProvider,
        (RequestDelegate)next);
    await activityLoggingMiddleware.InvokeAsync(context);
});

app.UseAuthentication();
app.UseAuthorization();

// Použití session middleware
app.UseSession();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller}/{action=Index}/{id?}");

app.MapControllers();

// Mapování SignalR hubu s použitím speciální CORS politiky
app.MapHub<MetricsHub>("/hubs/metrics").RequireCors("SignalRPolicy");

// Fallback pro Angular routing - musí být na konci
app.MapFallbackToFile("index.html");

app.Run();
