{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "Database": {"Enabled": true, "LogLevel": "Warning", "Source": "DISAdmin"}}, "FileLogging": {"Enabled": true, "LogDirectory": "Logs", "LogLevel": "Information", "RetainedFileCountLimit": 30, "FileSizeLimitBytes": 10485760, "RollingInterval": "Day", "OutputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Data Source=VITA-DELL\\SQL16DEV;Initial Catalog=DISAdminAugment;Integrated Security=True;MultipleActiveResultSets=True;Connection Timeout=180;TrustServerCertificate=True"}, "JwtSettings": {"Secret": "DISAdminSecretKey12345678901234567890"}, "Email": {"SmtpServer": "smtp.example.com", "SmtpPort": 587, "Username": "<EMAIL>", "Password": "password", "SenderEmail": "<EMAIL>", "SenderName": "DIS Admin", "EnableSsl": true}, "Security": {"MaxFailedAttempts": 5, "FailedAttemptsWindow": 15, "MaxRequestsPerMinute": 60, "BlockDuration": 60}, "TaskManagement": {"TfsBaseUrl": "http://dis.diamondsoftware.cz:8080/tfs/Diamond/DiamondIS/_workitems#_a=edit&id="}, "DISApiSettings": {"BaseUrl": "https://localhost:7177", "HealthEndpoint": "/api/health", "TimeoutSeconds": 5, "IgnoreCertificateErrors": true}}