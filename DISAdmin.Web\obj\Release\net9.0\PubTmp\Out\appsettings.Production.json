{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "Database": {"Enabled": true, "LogLevel": "Information", "Source": "DISAdmin"}}, "FileLogging": {"Enabled": true, "LogDirectory": "Logs", "LogLevel": "Information", "RetainedFileCountLimit": 30, "FileSizeLimitBytes": 10485760, "RollingInterval": "Day", "OutputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}