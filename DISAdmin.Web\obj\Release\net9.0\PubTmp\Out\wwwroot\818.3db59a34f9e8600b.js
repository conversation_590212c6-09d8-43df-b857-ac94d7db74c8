"use strict";(self.webpackChunkDISAdmin_Web=self.webpackChunkDISAdmin_Web||[]).push([[818],{2818:($,f,p)=>{p.r(f),p.d(f,{MonitoringModule:()=>H});var g=p(6895),b=p(6123),h=p(433),C=p(5861),m=p(3192),t=p(1571),v=p(4612),y=p(985),_=p(7556),T=p(7997),Z=p(9991),A=p(9523),I=p(7185);function S(a,o){if(1&a&&(t.TgZ(0,"span",15),t._uU(1),t.qZA()),2&a){const r=t.oxw();t.xp6(1),t.Oqu(r.activeFilterCount)}}function R(a,o){if(1&a&&(t.TgZ(0,"option",37),t._uU(1),t.qZA()),2&a){const r=o.$implicit;t.Q6J("value",r.id),t.xp6(1),t.Oqu(r.name)}}function k(a,o){if(1&a&&(t.TgZ(0,"option",37),t._uU(1),t.qZA()),2&a){const r=o.$implicit;t.Q6J("value",r.id),t.xp6(1),t.AsE("",r.customerAbbreviation," - ",r.name,"")}}function x(a,o){1&a&&(t.TgZ(0,"option",38),t._uU(1,"Bezpe\u010dnost"),t.qZA())}function E(a,o){if(1&a&&(t.TgZ(0,"div",16)(1,"form",17)(2,"div",18)(3,"label",19),t._uU(4,"\u010casov\xe9 obdob\xed"),t.qZA(),t.TgZ(5,"select",20)(6,"option",21),t._uU(7,"Posledn\xed 1 den"),t.qZA(),t.TgZ(8,"option",22),t._uU(9,"Posledn\xedch 7 dn\xed"),t.qZA(),t.TgZ(10,"option",23),t._uU(11,"Posledn\xedch 30 dn\xed"),t.qZA(),t.TgZ(12,"option",24),t._uU(13,"Posledn\xedch 90 dn\xed"),t.qZA()()(),t.TgZ(14,"div",18)(15,"label",25),t._uU(16,"Z\xe1kazn\xedk"),t.qZA(),t.TgZ(17,"select",26)(18,"option",27),t._uU(19,"V\u0161ichni z\xe1kazn\xedci"),t.qZA(),t.YNc(20,R,2,2,"option",28),t.qZA()(),t.TgZ(21,"div",18)(22,"label",29),t._uU(23,"Instance"),t.qZA(),t.TgZ(24,"select",30)(25,"option",27),t._uU(26,"V\u0161echny instance"),t.qZA(),t.YNc(27,k,2,3,"option",28),t.qZA()(),t.TgZ(28,"div",18)(29,"label",31),t._uU(30,"Typ metrik"),t.qZA(),t.TgZ(31,"select",32)(32,"option",33),t._uU(33,"V\u0161echny metriky"),t.qZA(),t.TgZ(34,"option",34),t._uU(35,"API metriky"),t.qZA(),t.TgZ(36,"option",35),t._uU(37,"Certifik\xe1ty"),t.qZA(),t.YNc(38,x,2,0,"option",36),t.qZA()()()()),2&a){const r=t.oxw();let e;t.xp6(1),t.Q6J("formGroup",r.filterForm),t.xp6(19),t.Q6J("ngForOf",r.customers),t.xp6(4),t.Q6J("disabled",!(null!=(e=r.filterForm.get("customerId"))&&e.value)),t.xp6(3),t.Q6J("ngForOf",r.sortedInstances),t.xp6(11),t.Q6J("ngIf",r.isAdmin)}}const U=function(a,o){return{"bi-funnel":a,"bi-funnel-fill text-primary":o}},z=function(a,o){return{"bi-chevron-up":a,"bi-chevron-down":o}};let F=(()=>{const o=class{get sortedInstances(){return[...this.instances].sort((e,i)=>{const s=e.customerAbbreviation.localeCompare(i.customerAbbreviation);return 0!==s?s:e.name.localeCompare(i.name)})}constructor(e,i,s,n,u,d){this.fb=e,this.monitoringService=i,this.instanceService=s,this.toastr=n,this.zone=u,this.cdr=d,this.isAdmin=!1,this.filterChange=new t.vpe,this.customers=[],this.instances=[],this.isFilterVisible=!0,this.hasActiveFilters=!1,this.activeFilterCount=0;const l=localStorage.getItem("monitoring_dateRange"),c=localStorage.getItem("monitoring_filterVisible"),B=localStorage.getItem("monitoring_customerId"),W=localStorage.getItem("monitoring_instanceId"),K=localStorage.getItem("monitoring_metricType");this.isFilterVisible=!!c&&"true"===c,this.filterForm=this.fb.group({dateRange:[l||"7"],customerId:[B||""],instanceId:[W||""],metricType:[K||"all"]})}ngOnInit(){this.loadCustomers();const e=this.filterForm.get("customerId")?.value;e&&this.loadInstances(e),this.updateActiveFiltersIndicator(),this.filterForm.valueChanges.subscribe(i=>{console.log("Form value changed:",i),this.updateActiveFiltersIndicator()}),this.filterForm.get("customerId")?.valueChanges.subscribe(i=>{i?(this.loadInstances(i),localStorage.setItem("monitoring_customerId",i)):(this.instances=[],this.filterForm.get("instanceId")?.setValue(""),localStorage.removeItem("monitoring_customerId"),localStorage.removeItem("monitoring_instanceId")),this.emitFilterChange()}),this.filterForm.get("instanceId")?.valueChanges.subscribe(i=>{i?localStorage.setItem("monitoring_instanceId",i):localStorage.removeItem("monitoring_instanceId"),this.emitFilterChange()}),this.filterForm.get("dateRange")?.valueChanges.subscribe(i=>{i&&localStorage.setItem("monitoring_dateRange",i),this.emitFilterChange()}),this.filterForm.get("metricType")?.valueChanges.subscribe(i=>{i&&localStorage.setItem("monitoring_metricType",i),this.emitFilterChange()})}updateActiveFiltersIndicator(){const e=this.filterForm.value,i=Object.keys(e).filter(n=>!("dateRange"===n&&"7"===e[n]||"metricType"===n&&"all"===e[n])&&null!==e[n]&&""!==e[n]&&void 0!==e[n]).reduce((n,u)=>(n[u]=e[u],n),{}),s=Object.keys(i).length;console.log("Aktivn\xed filtry:",s,i),this.activeFilterCount=s,this.hasActiveFilters=s>0,this.zone.run(()=>{this.cdr.detectChanges()})}loadCustomers(){this.monitoringService.getCustomers().subscribe({next:e=>{this.customers=e},error:e=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed z\xe1kazn\xedk\u016f",e)}})}loadInstances(e){this.instanceService.getInstancesByCustomerId(e).subscribe({next:i=>{this.instances=i},error:i=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed instanc\xed",i),this.toastr.error("Nepoda\u0159ilo se na\u010d\xedst instance z\xe1kazn\xedka","Chyba")}})}toggleFilterVisibility(){this.isFilterVisible=!this.isFilterVisible,localStorage.setItem("monitoring_filterVisible",this.isFilterVisible.toString())}saveFilter(){const e=this.filterForm.value;e.dateRange&&localStorage.setItem("monitoring_dateRange",e.dateRange),e.customerId?localStorage.setItem("monitoring_customerId",e.customerId):localStorage.removeItem("monitoring_customerId"),e.instanceId?localStorage.setItem("monitoring_instanceId",e.instanceId):localStorage.removeItem("monitoring_instanceId"),e.metricType&&localStorage.setItem("monitoring_metricType",e.metricType),this.toastr.success("Filtr byl \xfasp\u011b\u0161n\u011b ulo\u017een","Ulo\u017eeno")}loadData(){this.emitFilterChange()}resetFilter(){this.filterForm.setValue({dateRange:"7",customerId:"",instanceId:"",metricType:"all"},{emitEvent:!0}),localStorage.removeItem("monitoring_customerId"),localStorage.removeItem("monitoring_instanceId"),localStorage.setItem("monitoring_dateRange","7"),localStorage.setItem("monitoring_metricType","all"),this.updateActiveFiltersIndicator(),this.emitFilterChange(),this.toastr.info("Filtr byl resetov\xe1n","Reset")}emitFilterChange(){const e=this.filterForm.value;this.filterChange.emit({dateRange:e.dateRange,customerId:e.customerId||void 0,instanceId:e.instanceId||void 0,metricType:e.metricType})}exportData(){const e=this.filterForm.value;this.monitoringService.exportMetricsData(e.instanceId||void 0,e.customerId||void 0,parseInt(e.dateRange),e.metricType).subscribe({next:i=>{const s=new Blob([i],{type:"text/csv"}),n=window.URL.createObjectURL(s),u=document.createElement("a");u.href=n,u.download=`metriky_${(new Date).toISOString().slice(0,10)}.csv`,document.body.appendChild(u),u.click(),window.URL.revokeObjectURL(n),document.body.removeChild(u),this.toastr.success("Data byla exportov\xe1na","Export")},error:i=>{console.error("Chyba p\u0159i exportu dat",i),this.toastr.error("Nepoda\u0159ilo se exportovat data","Chyba")}})}};let a=o;return o.\u0275fac=function(i){return new(i||o)(t.Y36(h.qu),t.Y36(v.W),t.Y36(A._),t.Y36(I._W),t.Y36(t.R0b),t.Y36(t.sBO))},o.\u0275cmp=t.Xpm({type:o,selectors:[["app-monitoring-filter"]],inputs:{isAdmin:"isAdmin"},outputs:{filterChange:"filterChange"},decls:22,vars:10,consts:[[1,"card","mb-4"],[1,"card-header","d-flex","justify-content-between","align-items-center"],[1,"mb-0","filter-title",3,"click"],[1,"bi","fs-5","me-2",3,"ngClass"],["class","badge bg-danger ms-2 badge-smaller",4,"ngIf"],[1,"bi","ms-2",3,"ngClass"],[1,"btn","btn-sm","btn-outline-primary","me-2",3,"click"],[1,"bi","bi-download","me-1"],[1,"btn","btn-sm","btn-outline-primary","me-4",3,"click"],[1,"bi","bi-save","me-1"],[1,"btn","btn-sm","btn-outline-secondary","me-2",3,"click"],[1,"bi","bi-arrow-counterclockwise","me-1"],[1,"btn","btn-sm","btn-outline-primary",3,"click"],[1,"bi","bi-arrow-repeat","me-1"],["class","card-body",4,"ngIf"],[1,"badge","bg-danger","ms-2","badge-smaller"],[1,"card-body"],[1,"row","g-3",3,"formGroup"],[1,"col-md-3"],["for","dateRange",1,"form-label"],["id","dateRange","formControlName","dateRange",1,"form-select"],["value","1"],["value","7"],["value","30"],["value","90"],["for","customerId",1,"form-label"],["id","customerId","formControlName","customerId",1,"form-select"],["value",""],[3,"value",4,"ngFor","ngForOf"],["for","instanceId",1,"form-label"],["id","instanceId","formControlName","instanceId",1,"form-select",3,"disabled"],["for","metricType",1,"form-label"],["id","metricType","formControlName","metricType",1,"form-select"],["value","all"],["value","api"],["value","certificate"],["value","security",4,"ngIf"],[3,"value"],["value","security"]],template:function(i,s){1&i&&(t.TgZ(0,"div",0)(1,"div",1)(2,"h5",2),t.NdJ("click",function(){return s.toggleFilterVisibility()}),t._UZ(3,"i",3),t.TgZ(4,"span"),t._uU(5,"Filtr"),t.qZA(),t.YNc(6,S,2,1,"span",4),t._UZ(7,"i",5),t.qZA(),t.TgZ(8,"div")(9,"button",6),t.NdJ("click",function(){return s.exportData()}),t._UZ(10,"i",7),t._uU(11,"Export "),t.qZA(),t.TgZ(12,"button",8),t.NdJ("click",function(){return s.saveFilter()}),t._UZ(13,"i",9),t._uU(14,"Ulo\u017eit filtr "),t.qZA(),t.TgZ(15,"button",10),t.NdJ("click",function(){return s.resetFilter()}),t._UZ(16,"i",11),t._uU(17,"Reset "),t.qZA(),t.TgZ(18,"button",12),t.NdJ("click",function(){return s.loadData()}),t._UZ(19,"i",13),t._uU(20,"Na\u010d\xedst data "),t.qZA()()(),t.YNc(21,E,39,5,"div",14),t.qZA()),2&i&&(t.xp6(3),t.Q6J("ngClass",t.WLB(4,U,!s.hasActiveFilters,s.hasActiveFilters)),t.xp6(3),t.Q6J("ngIf",s.hasActiveFilters),t.xp6(1),t.Q6J("ngClass",t.WLB(7,z,s.isFilterVisible,!s.isFilterVisible)),t.xp6(14),t.Q6J("ngIf",s.isFilterVisible))},dependencies:[g.mk,g.sg,g.O5,h._Y,h.YN,h.Kr,h.EJ,h.JJ,h.JL,h.sg,h.u],styles:[".filter-title[_ngcontent-%COMP%]{cursor:pointer;-webkit-user-select:none;user-select:none;display:flex;align-items:center}.filter-title[_ngcontent-%COMP%]:hover{color:var(--bs-primary)}.badge-smaller[_ngcontent-%COMP%]{font-size:.7rem;padding:.25em .45em}.filter-title[_ngcontent-%COMP%]   i.bi-chevron-down[_ngcontent-%COMP%], .filter-title[_ngcontent-%COMP%]   i.bi-chevron-up[_ngcontent-%COMP%]{transition:transform .2s ease-in-out}.card-header[_ngcontent-%COMP%]{border-radius:.375rem .375rem 0 0;padding:.75rem 1rem}.card-body[_ngcontent-%COMP%]{border-radius:0 0 .375rem .375rem;padding:1rem}.card[_ngcontent-%COMP%]{transition:all .3s ease}body.dark-theme[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{background-color:#2b3035;border-color:#373b3e}body.dark-theme[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%], body.dark-theme[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]{background-color:#212529;border-color:#495057;color:#e9ecef}body.dark-theme[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus, body.dark-theme[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]:focus{background-color:#2b3035;color:#e9ecef}"]}),a})();const D=["apiCallsTimeChart"],M=["apiResponseTimeChart"],O=["certificateUsageChart"],P=["securityEventsChart"],q=["instancesStatusChart"];function j(a,o){if(1&a){const r=t.EpF();t.TgZ(0,"div",19),t._uU(1),t.TgZ(2,"button",20),t.NdJ("click",function(){t.CHM(r);const i=t.oxw();return t.KtG(i.error=null)}),t.qZA()()}if(2&a){const r=t.oxw();t.xp6(1),t.hij(" ",r.error," ")}}function w(a,o){1&a&&(t.TgZ(0,"div",21)(1,"div",22)(2,"span",23),t._uU(3,"Na\u010d\xedt\xe1n\xed..."),t.qZA()(),t.TgZ(4,"p",24),t._uU(5,"Na\u010d\xedt\xe1n\xed metrik..."),t.qZA()())}function N(a,o){if(1&a){const r=t.EpF();t.TgZ(0,"div",26)(1,"div",27)(2,"div",6)(3,"div",28)(4,"h5",35),t._uU(5,"API vol\xe1n\xed v \u010dase"),t.qZA(),t.TgZ(6,"button",30),t.NdJ("click",function(){t.CHM(r);const i=t.MAs(11),s=t.oxw(2);return t.KtG(s.openFullscreenChart(i,"API vol\xe1n\xed v \u010dase"))}),t._UZ(7,"i",31),t.qZA()(),t.TgZ(8,"div",32)(9,"div",33),t._UZ(10,"canvas",null,36),t.qZA()()()(),t.TgZ(12,"div",27)(13,"div",6)(14,"div",28)(15,"h5",37),t._uU(16,"Odezva DIS metod"),t.qZA(),t.TgZ(17,"button",30),t.NdJ("click",function(){t.CHM(r);const i=t.MAs(22),s=t.oxw(2);return t.KtG(s.openFullscreenChart(i,"Odezva DIS metod"))}),t._UZ(18,"i",31),t.qZA()(),t.TgZ(19,"div",32)(20,"div",33),t._UZ(21,"canvas",null,38),t.qZA()()()()()}}function J(a,o){if(1&a){const r=t.EpF();t.TgZ(0,"div",27)(1,"div",6)(2,"div",28)(3,"h5",40),t._uU(4,"Vyu\u017eit\xed certifik\xe1t\u016f"),t.qZA(),t.TgZ(5,"button",30),t.NdJ("click",function(){t.CHM(r);const i=t.MAs(10),s=t.oxw(3);return t.KtG(s.openFullscreenChart(i,"Vyu\u017eit\xed certifik\xe1t\u016f"))}),t._UZ(6,"i",31),t.qZA()(),t.TgZ(7,"div",32)(8,"div",33),t._UZ(9,"canvas",null,41),t.qZA()()()()}}function L(a,o){if(1&a){const r=t.EpF();t.TgZ(0,"div",27)(1,"div",6)(2,"div",28)(3,"h5",42),t._uU(4,"Bezpe\u010dnostn\xed ud\xe1losti"),t.qZA(),t.TgZ(5,"button",30),t.NdJ("click",function(){t.CHM(r);const i=t.MAs(10),s=t.oxw(3);return t.KtG(s.openFullscreenChart(i,"Bezpe\u010dnostn\xed ud\xe1losti"))}),t._UZ(6,"i",31),t.qZA()(),t.TgZ(7,"div",32)(8,"div",33),t._UZ(9,"canvas",null,43),t.qZA()()()()}}function G(a,o){if(1&a&&(t.TgZ(0,"div",26),t.YNc(1,J,11,0,"div",39),t.YNc(2,L,11,0,"div",39),t.qZA()),2&a){const r=t.oxw(2);t.xp6(1),t.Q6J("ngIf","all"===r.filterData.metricType||"certificate"===r.filterData.metricType),t.xp6(1),t.Q6J("ngIf","all"===r.filterData.metricType||"security"===r.filterData.metricType)}}function V(a,o){if(1&a){const r=t.EpF();t.TgZ(0,"div"),t.YNc(1,N,23,0,"div",25),t.YNc(2,G,3,2,"div",25),t.TgZ(3,"div",26)(4,"div",27)(5,"div",6)(6,"div",28)(7,"h5",29),t._uU(8,"Stav instanc\xed"),t.qZA(),t.TgZ(9,"button",30),t.NdJ("click",function(){t.CHM(r);const i=t.MAs(14),s=t.oxw();return t.KtG(s.openFullscreenChart(i,"Stav instanc\xed"))}),t._UZ(10,"i",31),t.qZA()(),t.TgZ(11,"div",32)(12,"div",33),t._UZ(13,"canvas",null,34),t.qZA()()()()()()}if(2&a){const r=t.oxw();t.xp6(1),t.Q6J("ngIf","all"===r.filterData.metricType||"api"===r.filterData.metricType),t.xp6(1),t.Q6J("ngIf",r.isAdmin&&("all"===r.filterData.metricType||"certificate"===r.filterData.metricType||"security"===r.filterData.metricType))}}m.kL.register(...m.zX);let Y=(()=>{const o=class{constructor(e,i,s,n,u){this.monitoringService=e,this.chartService=i,this.authService=s,this.signalRService=n,this.chartModalService=u,this.loading=!0,this.error=null,this.isAdmin=!1,this.systemStatistics={},this.apiCallsTimeChart=null,this.apiResponseTimeChart=null,this.certificateUsageChart=null,this.securityEventsChart=null,this.instancesStatusChart=null,this.filterData={dateRange:"30",metricType:"all"},this.updateSubscription=null,this.signalRSubscriptions=[],this.authService.currentUser.subscribe(d=>{this.currentUser=d,this.isAdmin=d?.isAdmin||!1})}ngOnInit(){this.loadSystemStatistics(),this.initSignalR()}onFilterChange(e){this.filterData=e,this.refreshData(),setTimeout(()=>{this.initPopovers()},500)}ngOnDestroy(){this.updateSubscription&&this.updateSubscription.unsubscribe(),this.signalRSubscriptions.forEach(e=>e.unsubscribe()),this.signalRService.stopConnection(),this.destroyCharts()}openFullscreenChart(e,i){if(e instanceof HTMLCanvasElement){const s=m.kL.getChart(e);this.chartModalService.openChartModal(s||null,i)}else this.chartModalService.openChartModal(e,i)}loadSystemStatistics(){this.monitoringService.getSystemStatistics().subscribe({next:e=>{this.systemStatistics=e},error:e=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed syst\xe9mov\xfdch statistik",e),this.error="Nepoda\u0159ilo se na\u010d\xedst syst\xe9mov\xe9 statistiky"}})}refreshData(){this.loadSystemStatistics(),this.refreshCharts(),setTimeout(()=>{this.initPopovers()},500)}initCharts(){this.loading=!0,this.loadApiCallsTimeChart(),this.loadApiResponseTimeChart(),this.loadInstancesStatusChart(),this.isAdmin&&(this.loadCertificateUsageChart(),this.loadSecurityEventsChart()),this.loading=!1}refreshCharts(){this.loadApiCallsTimeChart(),this.loadApiResponseTimeChart(),this.loadInstancesStatusChart(),this.isAdmin&&(this.loadCertificateUsageChart(),this.loadSecurityEventsChart())}destroyCharts(){this.apiCallsTimeChart&&(this.apiCallsTimeChart.destroy(),this.apiCallsTimeChart=null),this.apiResponseTimeChart&&(this.apiResponseTimeChart.destroy(),this.apiResponseTimeChart=null),this.certificateUsageChart&&(this.certificateUsageChart.destroy(),this.certificateUsageChart=null),this.securityEventsChart&&(this.securityEventsChart.destroy(),this.securityEventsChart=null),this.instancesStatusChart&&(this.instancesStatusChart.destroy(),this.instancesStatusChart=null)}loadApiCallsTimeChart(){const e=this.getFilters();this.chartService.getApiCallsTimeChartData(e.instanceId,e.customerId,parseInt(e.dateRange)).subscribe({next:i=>{this.apiCallsTimeChart?(this.apiCallsTimeChart.data.labels=i.labels,this.apiCallsTimeChart.data.datasets=i.datasets,this.apiCallsTimeChart.update()):this.apiCallsTimeChartRef&&(this.apiCallsTimeChart=new m.kL(this.apiCallsTimeChartRef.nativeElement,{type:"line",data:{labels:i.labels,datasets:i.datasets},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:i.title,font:{size:16}},legend:{display:!0,position:"top"},tooltip:{mode:"index",intersect:!1}},scales:{y:{beginAtZero:!0,title:{display:!0,text:"Po\u010det vol\xe1n\xed"}},x:{title:{display:!0,text:"Datum a \u010das"}}}}}))},error:i=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed dat pro graf API vol\xe1n\xed v \u010dase",i),this.error="Nepoda\u0159ilo se na\u010d\xedst data pro graf API vol\xe1n\xed v \u010dase"}})}loadApiResponseTimeChart(){const e=this.getFilters();this.chartService.getApiResponseTimeChartData(e.instanceId,e.customerId,parseInt(e.dateRange)).subscribe({next:i=>{this.apiResponseTimeChart?(this.apiResponseTimeChart.data.labels=i.labels,this.apiResponseTimeChart.data.datasets=i.datasets,this.apiResponseTimeChart.update()):this.apiResponseTimeChartRef&&(this.apiResponseTimeChart=new m.kL(this.apiResponseTimeChartRef.nativeElement,{type:"line",data:{labels:i.labels,datasets:i.datasets},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:i.title,font:{size:16}},legend:{display:!0,position:"top"},tooltip:{mode:"index",intersect:!1}},scales:{y:{beginAtZero:!0,title:{display:!0,text:"Doba odezvy (ms)"}},x:{title:{display:!0,text:"Datum a \u010das"}}}}}))},error:i=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed dat pro graf doby odezvy API",i),this.error="Nepoda\u0159ilo se na\u010d\xedst data pro graf doby odezvy API"}})}loadCertificateUsageChart(){const e=this.getFilters();this.chartService.getCertificateUsageChartData(e.instanceId,e.customerId,parseInt(e.dateRange)).subscribe({next:i=>{this.certificateUsageChart?(this.certificateUsageChart.data.labels=i.labels,this.certificateUsageChart.data.datasets=i.datasets,this.certificateUsageChart.update()):this.certificateUsageChartRef&&(this.certificateUsageChart=new m.kL(this.certificateUsageChartRef.nativeElement,{type:"bar",data:{labels:i.labels,datasets:i.datasets},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:i.title,font:{size:16}},legend:{display:!0,position:"top"},tooltip:{mode:"index",intersect:!1}},scales:{y:{beginAtZero:!0,title:{display:!0,text:"Po\u010det operac\xed"}},x:{title:{display:!0,text:"Typ operace"}}}}}))},error:i=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed dat pro graf vyu\u017eit\xed certifik\xe1t\u016f",i),this.error="Nepoda\u0159ilo se na\u010d\xedst data pro graf vyu\u017eit\xed certifik\xe1t\u016f"}})}loadSecurityEventsChart(){const e=this.getFilters();this.chartService.getSecurityEventsChartData(parseInt(e.dateRange)).subscribe({next:i=>{this.securityEventsChart?(this.securityEventsChart.data.labels=i.labels,this.securityEventsChart.data.datasets=i.datasets,this.securityEventsChart.update()):this.securityEventsChartRef&&(this.securityEventsChart=new m.kL(this.securityEventsChartRef.nativeElement,{type:"line",data:{labels:i.labels,datasets:i.datasets},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:i.title,font:{size:16}},legend:{display:!0,position:"top"},tooltip:{mode:"index",intersect:!1}},scales:{y:{beginAtZero:!0,title:{display:!0,text:"Po\u010det ud\xe1lost\xed"}},x:{title:{display:!0,text:"Datum"}}}}}))},error:i=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed dat pro graf bezpe\u010dnostn\xedch ud\xe1lost\xed",i),this.error="Nepoda\u0159ilo se na\u010d\xedst data pro graf bezpe\u010dnostn\xedch ud\xe1lost\xed"}})}loadInstancesStatusChart(){const e=this.getFilters();this.chartService.getInstancesStatusChartData(e.customerId).subscribe({next:i=>{this.instancesStatusChart?(this.instancesStatusChart.data.labels=i.labels,this.instancesStatusChart.data.datasets=i.datasets,this.instancesStatusChart.update()):this.instancesStatusChartRef&&(this.instancesStatusChart=new m.kL(this.instancesStatusChartRef.nativeElement,{type:"doughnut",data:{labels:i.labels,datasets:i.datasets},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:i.title,font:{size:16}},legend:{display:!0,position:"right"},tooltip:{mode:"index",intersect:!1}}}}))},error:i=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed dat pro graf stavu instanc\xed",i),this.error="Nepoda\u0159ilo se na\u010d\xedst data pro graf stavu instanc\xed"}})}getFilters(){return this.filterData}initSignalR(){var e=this;return(0,C.Z)(function*(){try{if(console.log("Initializing SignalR connection..."),yield e.signalRService.startConnection(),!(yield e.signalRService.testConnection()))return void console.warn("SignalR connection test failed. Falling back to interval-based updates.");console.log("SignalR connection test successful. Joining groups..."),yield e.signalRService.joinGroup("api-metrics"),yield e.signalRService.joinGroup("certificate-metrics"),e.isAdmin&&(yield e.signalRService.joinGroup("security-metrics"),yield e.signalRService.joinGroup("all-metrics")),e.signalRSubscriptions.push(e.signalRService.apiMetrics$.subscribe(s=>{Object.keys(s).length>0&&e.updateApiCharts(s)})),e.signalRSubscriptions.push(e.signalRService.certificateMetrics$.subscribe(s=>{Object.keys(s).length>0&&e.isAdmin&&e.updateCertificateChart(s)})),e.signalRSubscriptions.push(e.signalRService.securityMetrics$.subscribe(s=>{Object.keys(s).length>0&&e.isAdmin&&e.updateSecurityChart(s)})),e.signalRSubscriptions.push(e.signalRService.allMetrics$.subscribe(s=>{Object.keys(s).length>0&&e.loadSystemStatistics()})),console.log("SignalR initialization completed successfully.")}catch(i){console.error("Error during SignalR initialization:",i),console.warn("Falling back to interval-based updates.")}})()}updateApiCharts(e){if(this.apiCallsTimeChart&&e.apiCalls){const s=new Date(e.apiCalls.timestamp).toLocaleTimeString();for(let n=0;n<this.apiCallsTimeChart.data.datasets.length;n++){const u=this.apiCallsTimeChart.data.datasets[n],d=u.label;if(e.apiCalls[d]){const l=this.apiCallsTimeChart.data.labels,c=u.data;l.length>0&&l[l.length-1]===s?c[c.length-1]=e.apiCalls[d]:(0===n&&l.push(s),c.push(e.apiCalls[d]),l.length>20&&0===n?(l.shift(),c.shift()):c.length>l.length&&c.shift())}}this.apiCallsTimeChart.update("none")}if(this.apiResponseTimeChart&&e.apiResponseTime){const s=new Date(e.apiResponseTime.timestamp).toLocaleTimeString();for(let n=0;n<this.apiResponseTimeChart.data.datasets.length;n++){const u=this.apiResponseTimeChart.data.datasets[n],d=u.label;if(e.apiResponseTime[d]){const l=this.apiResponseTimeChart.data.labels,c=u.data;l.length>0&&l[l.length-1]===s?c[c.length-1]=e.apiResponseTime[d]:(0===n&&l.push(s),c.push(e.apiResponseTime[d]),l.length>20&&0===n?(l.shift(),c.shift()):c.length>l.length&&c.shift())}}this.apiResponseTimeChart.update("none")}}updateCertificateChart(e){if(this.certificateUsageChart&&e.certificateUsage){const i=Object.keys(e.certificateUsage);for(const s of i){const n=this.certificateUsageChart.data.labels,u=n.indexOf(s);if(-1!==u)for(let d=0;d<this.certificateUsageChart.data.datasets.length;d++){const l=this.certificateUsageChart.data.datasets[d],c=l.label;void 0!==e.certificateUsage[s][c]&&(l.data[u]=e.certificateUsage[s][c])}else{n.push(s);for(let d=0;d<this.certificateUsageChart.data.datasets.length;d++){const l=this.certificateUsageChart.data.datasets[d],c=l.label;l.data.push(void 0!==e.certificateUsage[s][c]?e.certificateUsage[s][c]:0)}}}this.certificateUsageChart.update("none")}}updateSecurityChart(e){if(this.securityEventsChart&&e.securityEvents){const s=new Date(e.securityEvents.timestamp).toLocaleTimeString();for(let n=0;n<this.securityEventsChart.data.datasets.length;n++){const u=this.securityEventsChart.data.datasets[n],d=u.label;if(e.securityEvents[d]){const l=this.securityEventsChart.data.labels,c=u.data;l.length>0&&l[l.length-1]===s?c[c.length-1]=e.securityEvents[d]:(0===n&&l.push(s),c.push(e.securityEvents[d]),l.length>20&&0===n?(l.shift(),c.shift()):c.length>l.length&&c.shift())}}this.securityEventsChart.update("none")}}ngAfterViewInit(){setTimeout(()=>{this.initCharts(),setTimeout(()=>{this.initPopovers()},300)},500)}initPopovers(){const e={"dis-response":"Graf zobrazuje pr\u016fm\u011brnou dobu odezvy metod v \u010dase. Data jsou z\xedsk\xe1v\xe1na z tabulky PerformanceMetrics za zvolen\xe9 obdob\xed.","dis-calls":"Tato statistika zobrazuje celkov\xfd po\u010det vol\xe1n\xed DIS metod za posledn\xedch 24 hodin. Hodnota je vypo\u010d\xedt\xe1na jako suma hodnoty TotalCount ze v\u0161ech z\xe1znam\u016f v tabulce PerformanceMetrics za posledn\xedch 24 hodin.","weighted-avg":"Tato hodnota p\u0159edstavuje v\xe1\u017een\xfd pr\u016fm\u011br doby odezvy metod za posledn\xedch 24 hodin. V\xe1\u017een\xfd pr\u016fm\u011br je vypo\u010d\xedt\xe1n s ohledem na po\u010det nenulov\xfdch vol\xe1n\xed ka\u017ed\xe9 metody (NonZeroCount), co\u017e poskytuje p\u0159esn\u011bj\u0161\xed obraz o skute\u010dn\xe9 dob\u011b odezvy ne\u017e prost\xfd pr\u016fm\u011br.","api-calls-time":"Graf zobrazuje po\u010det API vol\xe1n\xed v \u010dase pro vybran\xe9 obdob\xed. Data jsou z\xedsk\xe1v\xe1na z tabulky DiagnosticLogs a jsou agregov\xe1na podle \u010dasov\xfdch interval\u016f.","certificate-usage":"Graf zobrazuje vyu\u017eit\xed certifik\xe1t\u016f podle typu operace. Zahrnuje operace jako ov\u011b\u0159en\xed, podpis, \u0161ifrov\xe1n\xed a de\u0161ifrov\xe1n\xed. Data jsou z\xedsk\xe1v\xe1na z tabulky CertificateUsageLogs za zvolen\xe9 obdob\xed.","security-events":"Graf zobrazuje po\u010det bezpe\u010dnostn\xedch ud\xe1lost\xed v \u010dase. Zahrnuje ud\xe1losti jako ne\xfasp\u011b\u0161n\xe9 pokusy o p\u0159ihl\xe1\u0161en\xed, podez\u0159el\xe9 aktivity a poru\u0161en\xed bezpe\u010dnosti. Data jsou z\xedsk\xe1v\xe1na z tabulky SecurityLogs za zvolen\xe9 obdob\xed.","instances-status":"Graf zobrazuje aktu\xe1ln\xed stav v\u0161ech instanc\xed v syst\xe9mu. Ukazuje po\u010det aktivn\xedch, neaktivn\xedch a probl\xe9mov\xfdch instanc\xed.","active-instances":"Tato statistika zobrazuje po\u010det aktu\xe1ln\u011b aktivn\xedch instanc\xed z celkov\xe9ho po\u010dtu. Instance je pova\u017eov\xe1na za aktivn\xed, pokud se p\u0159ipojila v posledn\xedch 24 hodin\xe1ch.","expiring-certificates":"Tato statistika zobrazuje po\u010det certifik\xe1t\u016f, kter\xfdm vypr\u0161\xed platnost v p\u0159\xed\u0161t\xedch 30 dnech. V\u010dasn\xe1 obnova certifik\xe1t\u016f je d\u016fle\u017eit\xe1 pro zaji\u0161t\u011bn\xed nep\u0159eru\u0161en\xe9ho provozu.","security-events-24h":"Tato statistika zobrazuje po\u010det bezpe\u010dnostn\xedch ud\xe1lost\xed za posledn\xedch 24 hodin. Zahrnuje tak\xe9 po\u010det aktivn\xedch upozorn\u011bn\xed, kter\xe1 vy\u017eaduj\xed pozornost."};document.querySelectorAll('[data-bs-toggle="popover"]').forEach(i=>{const s=bootstrap.Popover.getInstance(i);s&&s.dispose()}),document.querySelectorAll('[data-bs-toggle="popover"]').forEach(i=>{const s=i.getAttribute("data-help-type");if(console.log("Initializing popover for element with help-type:",s),s&&s in e)try{new bootstrap.Popover(i,{content:e[s],html:!0,trigger:"hover",placement:"top",container:"body"})}catch(n){console.error("Error initializing popover:",n)}else s&&console.warn("Help content not found for type:",s)})}};let a=o;return o.\u0275fac=function(i){return new(i||o)(t.Y36(v.W),t.Y36(y.C),t.Y36(_.e),t.Y36(T.M),t.Y36(Z.i))},o.\u0275cmp=t.Xpm({type:o,selectors:[["app-monitoring"]],viewQuery:function(i,s){if(1&i&&(t.Gf(D,5),t.Gf(M,5),t.Gf(O,5),t.Gf(P,5),t.Gf(q,5)),2&i){let n;t.iGM(n=t.CRH())&&(s.apiCallsTimeChartRef=n.first),t.iGM(n=t.CRH())&&(s.apiResponseTimeChartRef=n.first),t.iGM(n=t.CRH())&&(s.certificateUsageChartRef=n.first),t.iGM(n=t.CRH())&&(s.securityEventsChartRef=n.first),t.iGM(n=t.CRH())&&(s.instancesStatusChartRef=n.first)}},decls:48,vars:14,consts:[[1,"container"],[1,"d-flex","justify-content-between","align-items-center","mb-4"],["class","alert alert-danger alert-dismissible fade show","role","alert",4,"ngIf"],[3,"isAdmin","filterChange"],[1,"row","mb-4"],[1,"col-md-3","mb-3"],[1,"card","h-100"],[1,"card-body","text-center"],["data-bs-toggle","popover","data-help-type","active-instances",1,"card-title",2,"cursor","help"],[1,"display-4","mb-0"],[1,"text-muted"],["data-bs-toggle","popover","data-help-type","dis-calls",1,"card-title",2,"cursor","help"],["data-bs-toggle","popover","data-help-type","weighted-avg",2,"cursor","help"],["data-bs-toggle","popover","data-help-type","expiring-certificates",1,"card-title",2,"cursor","help"],[1,"display-4","mb-0","text-warning"],["data-bs-toggle","popover","data-help-type","security-events-24h",1,"card-title",2,"cursor","help"],[1,"display-4","mb-0","text-danger"],["class","text-center my-5",4,"ngIf"],[4,"ngIf"],["role","alert",1,"alert","alert-danger","alert-dismissible","fade","show"],["type","button","aria-label","Close",1,"btn-close",3,"click"],[1,"text-center","my-5"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-2"],["class","row",4,"ngIf"],[1,"row"],[1,"col-lg-6","mb-4"],[1,"card-header","d-flex","justify-content-between","align-items-center"],["data-bs-toggle","popover","data-help-type","instances-status",1,"mb-0",2,"cursor","help"],["title","Zobrazit graf na celou obrazovku",1,"btn","btn-sm","btn-outline-info",3,"click"],[1,"bi","bi-arrows-fullscreen"],[1,"card-body"],[1,"chart-container",2,"position","relative","height","300px"],["instancesStatusChart",""],["data-bs-toggle","popover","data-help-type","api-calls-time",1,"mb-0",2,"cursor","help"],["apiCallsTimeChart",""],["data-bs-toggle","popover","data-help-type","dis-response",1,"mb-0",2,"cursor","help"],["apiResponseTimeChart",""],["class","col-lg-6 mb-4",4,"ngIf"],["data-bs-toggle","popover","data-help-type","certificate-usage",1,"mb-0",2,"cursor","help"],["certificateUsageChart",""],["data-bs-toggle","popover","data-help-type","security-events",1,"mb-0",2,"cursor","help"],["securityEventsChart",""]],template:function(i,s){1&i&&(t.TgZ(0,"div",0)(1,"div",1)(2,"h2"),t._uU(3,"Monitoring a metriky"),t.qZA()(),t.YNc(4,j,3,1,"div",2),t.TgZ(5,"app-monitoring-filter",3),t.NdJ("filterChange",function(u){return s.onFilterChange(u)}),t.qZA(),t.TgZ(6,"div",4)(7,"div",5)(8,"div",6)(9,"div",7)(10,"h5",8),t._uU(11,"Aktivn\xed instance"),t.qZA(),t.TgZ(12,"p",9),t._uU(13),t.qZA(),t.TgZ(14,"p",10),t._uU(15),t.qZA()()()(),t.TgZ(16,"div",5)(17,"div",6)(18,"div",7)(19,"h5",11),t._uU(20,"Po\u010det vol\xe1n\xed DIS metod"),t.qZA(),t.TgZ(21,"p",9),t._uU(22),t.qZA(),t.TgZ(23,"p",10)(24,"span",12),t._uU(25,"Pr\u016fm\u011brn\xe1 odezva"),t.qZA(),t._uU(26),t.ALo(27,"number"),t.qZA()()()(),t.TgZ(28,"div",5)(29,"div",6)(30,"div",7)(31,"h5",13),t._uU(32,"Expiruj\xedc\xed certifik\xe1ty"),t.qZA(),t.TgZ(33,"p",14),t._uU(34),t.qZA(),t.TgZ(35,"p",10),t._uU(36,"V p\u0159\xed\u0161t\xedch 30 dnech"),t.qZA()()()(),t.TgZ(37,"div",5)(38,"div",6)(39,"div",7)(40,"h5",15),t._uU(41,"Bezpe\u010dnostn\xed ud\xe1losti (24h)"),t.qZA(),t.TgZ(42,"p",16),t._uU(43),t.qZA(),t.TgZ(44,"p",10),t._uU(45),t.qZA()()()()(),t.YNc(46,w,6,0,"div",17),t.YNc(47,V,15,2,"div",18),t.qZA()),2&i&&(t.xp6(4),t.Q6J("ngIf",s.error),t.xp6(1),t.Q6J("isAdmin",s.isAdmin),t.xp6(8),t.Oqu(s.systemStatistics.ActiveInstancesCount||0),t.xp6(2),t.hij("z celkov\xfdch ",s.systemStatistics.InstancesCount||0,""),t.xp6(7),t.Oqu(s.systemStatistics.TotalMethodCalls||0),t.xp6(4),t.hij(": ",t.xi3(27,11,s.systemStatistics.WeightedAvgResponseTime||0,"1.0-2")," ms"),t.xp6(8),t.Oqu(s.systemStatistics.ExpiringCertificatesCount||0),t.xp6(9),t.Oqu(s.systemStatistics.SecurityEventsLast24h||0),t.xp6(2),t.hij("Aktivn\xed upozorn\u011bn\xed: ",s.systemStatistics.ActiveAlertsCount||0,""),t.xp6(1),t.Q6J("ngIf",s.loading),t.xp6(1),t.Q6J("ngIf",!s.loading))},dependencies:[g.O5,F,g.JJ],styles:[".chart-container[_ngcontent-%COMP%]{position:relative;height:300px;width:100%}.card[_ngcontent-%COMP%]{box-shadow:0 .125rem .25rem #00000013;border-radius:.5rem;border:1px solid rgba(0,0,0,.125)}.card-header[_ngcontent-%COMP%]{background-color:#00000008;border-bottom:1px solid rgba(0,0,0,.125)}.display-4[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:300;line-height:1.2}.text-warning[_ngcontent-%COMP%]{color:#ffc107!important}.text-danger[_ngcontent-%COMP%]{color:#dc3545!important}.text-muted[_ngcontent-%COMP%]{color:#6c757d!important}.form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus{border-color:#86b7fe;box-shadow:0 0 0 .25rem #0d6efd40}.btn-primary[_ngcontent-%COMP%]{background-color:#0d6efd;border-color:#0d6efd}.btn-primary[_ngcontent-%COMP%]:hover{background-color:#0b5ed7;border-color:#0a58ca}@media (max-width: 768px){.display-4[_ngcontent-%COMP%]{font-size:2rem}.card-title[_ngcontent-%COMP%]{font-size:1rem}}"]}),a})();var Q=p(4466);let H=(()=>{const o=class{};let a=o;return o.\u0275fac=function(i){return new(i||o)},o.\u0275mod=t.oAB({type:o}),o.\u0275inj=t.cJS({imports:[g.ez,h.u5,h.UX,Q.m,b.Bz.forChild([{path:"",component:Y}])]}),a})()}}]);