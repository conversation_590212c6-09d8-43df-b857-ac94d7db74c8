# ClipboardService - Univerzální služba pro kopírování do schránky

## P<PERSON>ehled

`ClipboardService` je univerzální Angular služba pro robustní kopírování textu do schránky s automatickým fallback mechanismem a toast notifikacemi.

## Problém

Původní implementace kopírování do schránky:
- ❌ Nefungovala v různých kontextech (HTTP vs HTTPS)
- ❌ Neměla fallback pro star<PERSON><PERSON> prohl<PERSON>e<PERSON>e
- ❌ Chyběly toast notifikace
- ❌ Duplikovaný kód v různých komponentách
- ❌ Nedostatečné error handling

## Řešení

### ✅ **Univerzální ClipboardService**
- Jeden service pro celou aplikaci
- Automatická detekce Clipboard API vs fallback
- Konzistentní toast notifikace
- Robustní error handling
- Type safety s TypeScript

## Implementace

### 1. ClipboardService (`clipboard.service.ts`)

```typescript
@Injectable({
  providedIn: 'root'
})
export class ClipboardService {
  
  // Hlavní metoda pro kopírování textu
  async copyToClipboard(text: string, successMessage?: string, errorMessage?: string): Promise<boolean>
  
  // Metoda pro kopírování z input elementu
  async copyFromInput(inputElement: HTMLInputElement, successMessage?: string, errorMessage?: string): Promise<boolean>
  
  // Kontrola dostupnosti Clipboard API
  isClipboardApiAvailable(): boolean
}
```

### 2. Automatická detekce API

```typescript
// Moderní Clipboard API (preferováno)
if (navigator.clipboard && window.isSecureContext) {
  await navigator.clipboard.writeText(text);
} else {
  // Fallback pro starší prohlížeče
  this.fallbackCopyToClipboard(text);
}
```

### 3. Fallback mechanismus

```typescript
private fallbackCopyToClipboard(text: string): boolean {
  // Vytvoření dočasného textarea elementu
  const textArea = document.createElement('textarea');
  textArea.value = text;
  
  // Skrytí elementu mimo viewport
  textArea.style.position = 'fixed';
  textArea.style.left = '-999999px';
  
  // Přidání do DOM, označení, kopírování, odstranění
  document.body.appendChild(textArea);
  textArea.select();
  const successful = document.execCommand('copy');
  document.body.removeChild(textArea);
  
  return successful;
}
```

## Použití

### 1. **Kopírování textu**

```typescript
// V komponentě
constructor(private clipboardService: ClipboardService) {}

async copyPassword(): Promise<void> {
  const success = await this.clipboardService.copyToClipboard(
    this.password,
    'Heslo bylo zkopírováno do schránky',
    'Nepodařilo se zkopírovat heslo'
  );
  
  if (success) {
    // Další akce při úspěchu
  }
}
```

### 2. **Kopírování z input elementu**

```typescript
async copyApiKey(inputElement: HTMLInputElement): Promise<void> {
  await this.clipboardService.copyFromInput(
    inputElement,
    'API klíč byl zkopírován do schránky',
    'Nepodařilo se zkopírovat API klíč'
  );
}
```

### 3. **Kontrola dostupnosti API**

```typescript
ngOnInit(): void {
  if (this.clipboardService.isClipboardApiAvailable()) {
    console.log('Moderní Clipboard API je dostupné');
  } else {
    console.log('Bude použit fallback mechanismus');
  }
}
```

## Aktualizované komponenty

### 1. **CertificateModalComponent**

**Před:**
```typescript
copyPasswordToClipboard(): void {
  navigator.clipboard.writeText(this.certificate.password)
    .then(() => {
      // Ruční handling úspěchu
    })
    .catch(err => {
      console.error('Chyba:', err);
    });
}
```

**Po:**
```typescript
async copyPasswordToClipboard(): Promise<void> {
  const success = await this.clipboardService.copyToClipboard(
    this.certificate.password,
    'Heslo certifikátu bylo zkopírováno do schránky',
    'Nepodařilo se zkopírovat heslo certifikátu'
  );
  
  if (success) {
    this.passwordCopied = true;
    setTimeout(() => this.passwordCopied = false, 3000);
  }
}
```

### 2. **CustomerDetailComponent**

**Před:**
```typescript
copyApiKey(inputElement: HTMLInputElement): void {
  inputElement.select();
  document.execCommand('copy');
  this.modalService.alert('API klíč byl zkopírován', 'Informace');
}
```

**Po:**
```typescript
async copyApiKey(inputElement: HTMLInputElement): Promise<void> {
  await this.clipboardService.copyFromInput(
    inputElement,
    'API klíč byl zkopírován do schránky',
    'Nepodařilo se zkopírovat API klíč'
  );
}
```

## Výhody

### ✅ **Robustnost**
- Funguje v HTTPS i HTTP kontextech
- Automatický fallback pro starší prohlížeče
- Optimalizace pro mobilní zařízení
- Detailní error handling

### ✅ **Konzistence**
- Jednotné chování v celé aplikaci
- Konzistentní toast notifikace
- Standardizované error messages

### ✅ **Znovupoužitelnost**
- Jeden service pro všechny komponenty
- Konfigurovatelné zprávy
- Type safety s TypeScript

### ✅ **Uživatelská přívětivost**
- Toast notifikace při úspěchu i chybě
- Vizuální feedback (ikona checkmark)
- Jasné chybové zprávy

## Podporované kontexty

### ✅ **HTTPS kontexty**
- Moderní Clipboard API
- Nejlepší výkon a bezpečnost

### ✅ **HTTP kontexty**
- Automatický fallback
- Funkční i bez Clipboard API

### ✅ **Starší prohlížeče**
- execCommand fallback
- Podpora pro IE11+

### ✅ **Mobilní zařízení**
- Optimalizované označování textu
- setSelectionRange pro lepší kompatibilitu

## Toast notifikace

### **Úspěch**
- ✅ Zelená notifikace
- ✅ Checkmark ikona
- ✅ Pozice: pravý horní roh
- ✅ Doba: 3 sekundy

### **Chyba**
- ❌ Červená notifikace
- ❌ Error ikona
- ❌ Detailní popis problému
- ❌ Návod k ručnímu kopírování

## Testování

### **Test scénáře:**
1. **HTTPS kontext** - moderní Clipboard API
2. **HTTP kontext** - fallback mechanismus
3. **Starší prohlížeče** - execCommand
4. **Mobilní zařízení** - touch optimalizace
5. **Prázdný text** - error handling
6. **Chybové stavy** - robustní fallback

### **Spuštění testů:**
```powershell
.\scripts\test-clipboard-fix.ps1
```

## Soubory

- ✅ `DISAdmin.Web/ClientApp/src/app/services/clipboard.service.ts` (nový)
- ✅ `DISAdmin.Web/ClientApp/src/app/shared/certificate-modal/certificate-modal.component.ts` (aktualizován)
- ✅ `DISAdmin.Web/ClientApp/src/app/customers/customer-detail/customer-detail.component.ts` (aktualizován)

## Budoucí rozšíření

### **Možná vylepšení:**
- Podpora pro kopírování obrázků
- Batch kopírování více textů
- Clipboard history
- Šifrování citlivých dat před kopírováním
- Analytics pro sledování použití

ClipboardService poskytuje robustní, znovupoužitelné a uživatelsky přívětivé řešení pro kopírování do schránky v celé aplikaci! 🎉
