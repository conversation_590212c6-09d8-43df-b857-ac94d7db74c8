# Test skript pro overeni opravy kopirovani do schranky

Write-Host "=== Test opravy kopirovani do schranky ===" -ForegroundColor Green

Write-Host "`n=== Opravene problemy ===" -ForegroundColor Green
Write-Host "1. <PERSON><PERSON><PERSON><PERSON><PERSON> 'Zkopirovat heslo do schranky' nefungovalo" -ForegroundColor White
Write-Host "2. Chybela toast notifikace pri uspesnem kopirovani" -ForegroundColor White
Write-Host "3. Nebyl fallback pro starsi prohlizece" -ForegroundColor White
Write-Host "4. Ko<PERSON>rovani API klice pouzivalo zastarale metody" -ForegroundColor White

Write-Host "`n=== Implementovane opravy ===" -ForegroundColor Green
Write-Host "1. Univerzalni ClipboardService pro celou aplikaci" -ForegroundColor White
Write-Host "2. Automaticky fallback pro starsi prohlizece" -ForegroundColor White
Write-Host "3. Toast notifikace pri uspesnem i neuspesnem kopirovani" -ForegroundColor White
Write-Host "4. Podpora pro HTTPS i HTTP kontexty" -ForegroundColor White
Write-Host "5. Optimalizace pro mobilni zarizeni" -ForegroundColor White

Write-Host "`n=== Nove soubory ===" -ForegroundColor Green
Write-Host "- DISAdmin.Web/ClientApp/src/app/services/clipboard.service.ts" -ForegroundColor White
Write-Host "- Aktualizovana certificate-modal.component.ts" -ForegroundColor White
Write-Host "- Aktualizovana customer-detail.component.ts" -ForegroundColor White

Write-Host "`n=== Funkcionalita ClipboardService ===" -ForegroundColor Green
Write-Host "Metody:" -ForegroundColor Yellow
Write-Host "- copyToClipboard(text, successMsg, errorMsg)" -ForegroundColor White
Write-Host "- copyFromInput(inputElement, successMsg, errorMsg)" -ForegroundColor White
Write-Host "- isClipboardApiAvailable()" -ForegroundColor White

Write-Host "`nFunkce:" -ForegroundColor Yellow
Write-Host "- Automaticka detekce Clipboard API vs fallback" -ForegroundColor White
Write-Host "- Podpora pro HTTPS i HTTP kontexty" -ForegroundColor White
Write-Host "- Toast notifikace s konfigurovatelnym textem" -ForegroundColor White
Write-Host "- Optimalizace pro mobilni zarizeni" -ForegroundColor White
Write-Host "- Robustni error handling" -ForegroundColor White

Write-Host "`n=== Test scenare ===" -ForegroundColor Green
Write-Host "Otevri aplikaci a otestuj tyto scenare:" -ForegroundColor Cyan

Write-Host "`n1. Test kopirovani hesla certifikatu:" -ForegroundColor Yellow
Write-Host "   - Jdi do Zakaznici -> Detail zakaznika" -ForegroundColor White
Write-Host "   - Otevri detail instance DIS" -ForegroundColor White
Write-Host "   - Vygeneruj novy certifikat" -ForegroundColor White
Write-Host "   - Klikni na tlacitko 'Zkopirovat heslo do schranky'" -ForegroundColor White
Write-Host "   - Ocekavany vysledek:" -ForegroundColor Green
Write-Host "     * Heslo se zkopiruje do schranky" -ForegroundColor Green
Write-Host "     * Zobrazi se toast notifikace 'Heslo certifikatu bylo zkopirovano'" -ForegroundColor Green
Write-Host "     * Ikona se zmeni na checkmark na 3 sekundy" -ForegroundColor Green

Write-Host "`n2. Test kopirovani API klice:" -ForegroundColor Yellow
Write-Host "   - Jdi do Zakaznici -> Detail zakaznika" -ForegroundColor White
Write-Host "   - V sekci 'Informace o zakaznikovi' najdi API klic" -ForegroundColor White
Write-Host "   - Klikni na tlacitko 'Kopirovat'" -ForegroundColor White
Write-Host "   - Ocekavany vysledek:" -ForegroundColor Green
Write-Host "     * API klic se zkopiruje do schranky" -ForegroundColor Green
Write-Host "     * Zobrazi se toast notifikace 'API klic byl zkopirovan'" -ForegroundColor Green

Write-Host "`n3. Test fallback mechanismu:" -ForegroundColor Yellow
Write-Host "   - Otevri aplikaci v HTTP kontextu (ne HTTPS)" -ForegroundColor White
Write-Host "   - Nebo pouzij starsi prohlizec" -ForegroundColor White
Write-Host "   - Zkus zkopirovat heslo nebo API klic" -ForegroundColor White
Write-Host "   - Ocekavany vysledek:" -ForegroundColor Green
Write-Host "     * Automaticky se pouzije fallback metoda" -ForegroundColor Green
Write-Host "     * Kopirovani funguje i bez Clipboard API" -ForegroundColor Green
Write-Host "     * Toast notifikace se zobrazi" -ForegroundColor Green

Write-Host "`n4. Test chybovych stavu:" -ForegroundColor Yellow
Write-Host "   - Zkus zkopirovat prazdny text" -ForegroundColor White
Write-Host "   - Ocekavany vysledek:" -ForegroundColor Green
Write-Host "     * Zobrazi se chybova toast notifikace" -ForegroundColor Green
Write-Host "     * Aplikace se nezhruti" -ForegroundColor Green

Write-Host "`n=== Technicky popis ===" -ForegroundColor Green
Write-Host "Clipboard API detekce:" -ForegroundColor White
Write-Host "- navigator.clipboard && window.isSecureContext" -ForegroundColor Gray
Write-Host "- Pokud je dostupne, pouzije se navigator.clipboard.writeText()" -ForegroundColor Gray
Write-Host "- Pokud ne, pouzije se fallback s document.execCommand('copy')" -ForegroundColor Gray

Write-Host "`nFallback mechanismus:" -ForegroundColor White
Write-Host "- Vytvori docasny textarea element" -ForegroundColor Gray
Write-Host "- Nastavi text a skryje element" -ForegroundColor Gray
Write-Host "- Oznaci text a zkopiruje pomoci execCommand" -ForegroundColor Gray
Write-Host "- Odstrani docasny element" -ForegroundColor Gray

Write-Host "`nToast notifikace:" -ForegroundColor White
Write-Host "- Uspech: Zelena notifikace s checkmark ikonou" -ForegroundColor Gray
Write-Host "- Chyba: Cervena notifikace s error ikonou" -ForegroundColor Gray
Write-Host "- Pozice: Pravy horni roh obrazovky" -ForegroundColor Gray
Write-Host "- Doba zobrazeni: 3 sekundy" -ForegroundColor Gray

Write-Host "`n=== Kontrola build chyb ===" -ForegroundColor Green
try {
    $buildResult = dotnet build --no-restore --verbosity quiet 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build uspesny - zadne chyby!" -ForegroundColor Green
    } else {
        Write-Host "Build chyby:" -ForegroundColor Red
        Write-Host $buildResult -ForegroundColor Red
    }
} catch {
    Write-Host "Nelze spustit dotnet build" -ForegroundColor Yellow
}

Write-Host "`n=== Kontrola TypeScript chyb ===" -ForegroundColor Green
$clientAppPath = Join-Path $PSScriptRoot "..\DISAdmin.Web\ClientApp"
if (Test-Path $clientAppPath) {
    try {
        Push-Location $clientAppPath
        $tscResult = npx tsc --noEmit 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "TypeScript kompilace uspesna!" -ForegroundColor Green
        } else {
            Write-Host "TypeScript chyby:" -ForegroundColor Red
            Write-Host $tscResult -ForegroundColor Red
        }
        Pop-Location
    } catch {
        Write-Host "Nelze spustit TypeScript kompilaci" -ForegroundColor Yellow
        Pop-Location
    }
} else {
    Write-Host "ClientApp slozka nenalezena" -ForegroundColor Yellow
}

Write-Host "`n=== Finalni test ===" -ForegroundColor Green
Write-Host "1. Spusť aplikaci: dotnet run --project DISAdmin.Web" -ForegroundColor White
Write-Host "2. Otevri: https://localhost:7001" -ForegroundColor White
Write-Host "3. Otestuj kopirovani hesla certifikatu a API klice" -ForegroundColor White
Write-Host "4. Zkontroluj toast notifikace" -ForegroundColor White
Write-Host "5. Otestuj v ruznych prohlizecich" -ForegroundColor White

Write-Host "`nOprava kopirovani do schranky je pripravena!" -ForegroundColor Green
